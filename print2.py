import os
from datetime import datetime
import sqlite3
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.units import cm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib import colors
from reportlab.lib.styles import ParagraphStyle
import sys
import traceback
from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt5.QtGui import QFont, QIcon, QPixmap
from PyQt5.QtCore import Qt

# استيراد مكتبات معالجة النصوص العربية
import arabic_reshaper
from bidi.algorithm import get_display

# تسجيل الخطوط العربية
try:
    # محاولة تسجيل الخط العربي الأساسي
    pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
    print("تم تسجيل خط Arabic (arial.ttf) بنجاح")
except Exception as font_error:
    print(f"خطأ في تسجيل خط Arabic (arial.ttf): {font_error}")
    try:
        # محاولة استخدام مسار مطلق للخط
        script_dir = os.path.dirname(os.path.abspath(__file__))
        arial_path = os.path.join(script_dir, "arial.ttf")
        if os.path.exists(arial_path):
            pdfmetrics.registerFont(TTFont("Arabic", arial_path))
            print(f"تم تسجيل خط Arabic من المسار المطلق: {arial_path}")
        else:
            print(f"خطأ: ملف الخط غير موجود في المسار: {arial_path}")
    except Exception as alt_font_error:
        print(f"خطأ في تسجيل خط Arabic من المسار المطلق: {alt_font_error}")

# محاولة تسجيل خطوط Amiri و Calibri
try:
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # تحديد مسارات الخطوط
    amiri_regular_path = os.path.join(script_dir, "Amiri-Regular.ttf")
    amiri_bold_path = os.path.join(script_dir, "Amiri-Bold.ttf")
    calibri_regular_path = os.path.join(script_dir, "calibri.ttf")
    calibri_bold_path = os.path.join(script_dir, "calibrib.ttf")

    # البحث عن خطوط Calibri في مجلد الخطوط في Windows
    if os.name == 'nt':  # Windows
        windows_fonts_dir = os.path.join(os.environ.get('SystemRoot', 'C:\\Windows'), 'Fonts')
        if not os.path.exists(calibri_regular_path):
            windows_calibri_path = os.path.join(windows_fonts_dir, 'calibri.ttf')
            if os.path.exists(windows_calibri_path):
                calibri_regular_path = windows_calibri_path
                print(f"تم العثور على خط Calibri في مجلد خطوط Windows: {calibri_regular_path}")

        if not os.path.exists(calibri_bold_path):
            windows_calibri_bold_path = os.path.join(windows_fonts_dir, 'calibrib.ttf')
            if os.path.exists(windows_calibri_bold_path):
                calibri_bold_path = windows_calibri_bold_path
                print(f"تم العثور على خط Calibri Bold في مجلد خطوط Windows: {calibri_bold_path}")

    # تسجيل خطوط Amiri
    if os.path.exists(amiri_regular_path):
        pdfmetrics.registerFont(TTFont("Amiri", amiri_regular_path))
        print(f"تم تسجيل خط Amiri من المسار: {amiri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri في المسار: {amiri_regular_path}")

    if os.path.exists(amiri_bold_path):
        pdfmetrics.registerFont(TTFont("Amiri-Bold", amiri_bold_path))
        print(f"تم تسجيل خط Amiri-Bold من المسار: {amiri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Amiri-Bold في المسار: {amiri_bold_path}")

    # تسجيل خطوط Calibri
    if os.path.exists(calibri_regular_path):
        pdfmetrics.registerFont(TTFont("Calibri", calibri_regular_path))
        print(f"تم تسجيل خط Calibri من المسار: {calibri_regular_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri في المسار: {calibri_regular_path}")

    if os.path.exists(calibri_bold_path):
        pdfmetrics.registerFont(TTFont("Calibri-Bold", calibri_bold_path))
        print(f"تم تسجيل خط Calibri-Bold من المسار: {calibri_bold_path}")
    else:
        print(f"تحذير: لم يتم العثور على خط Calibri-Bold في المسار: {calibri_bold_path}")

except Exception as font_error:
    print(f"خطأ في تسجيل الخطوط: {font_error}")

def fix_arabic_text(text):
    """معالجة النص العربي (تشكيل فقط) وتجهيزه للعرض."""
    if not text:
        return ""
    text = str(text).strip()
    if not text:
        return ""
    try:
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        # تطبيق خوارزمية BIDI لعرض النص بشكل صحيح
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"خطأ في معالجة النص العربي '{text[:20]}...': {e}")
        return text

def open_pdf(filename):
    """Opens the generated PDF file using the default system viewer."""
    try:
        absolute_path = os.path.abspath(filename)
        print(f"محاولة فتح الملف: {absolute_path}")

        # استخدام subprocess بدلاً من os.startfile/os.system
        import subprocess

        # فتح الملف باستخدام الطريقة المناسبة للنظام - تبسيط للعمل على Windows
        try:
            # محاولة فتح المجلد الذي يحتوي على الملف مع تحديد الملف
            subprocess.Popen(f'explorer /select,"{absolute_path}"', shell=True)
            return True
        except Exception as e:
            print(f"خطأ في فتح المجلد: {e}")
            try:
                # محاولة بديلة باستخدام os.startfile
                os.startfile(absolute_path)
                return True
            except Exception as e2:
                print(f"خطأ في فتح الملف: {e2}")
                if hasattr(e2, 'winerror') and e2.winerror == 1155:
                    QMessageBox.warning(
                        None,
                        "فشل فتح الملف",
                        f"تم إنشاء ملف PDF بنجاح في:\n{absolute_path}\n\n"
                        "ولكن، لم يتم العثور على تطبيق افتراضي لفتح ملفات PDF.\n"
                        "الرجاء فتح الملف يدوياً."
                    )
                else:
                    QMessageBox.critical(
                        None,
                        "خطأ",
                        f"حدث خطأ أثناء محاولة فتح الملف:\n{e2}"
                    )
                return False

    except Exception as path_error:
        print(f"خطأ في تحديد مسار الملف: {path_error}")
        traceback.print_exc()
        QMessageBox.critical(
            None,
            "خطأ",
            f"حدث خطأ في تحديد مسار الملف:\n{path_error}"
        )
        return False

def generate_entry_records_pdf(file_path, institution_data, entry_data):
    """إنشاء ملف PDF لسجلات الدخول والتأخر باستخدام SimpleDocTemplate"""
    try:
        # استخراج معلومات المؤسسة من قاعدة البيانات
        institution_name = institution_data.get('institution', '')
        school_year = institution_data.get('school_year', '')
        logo_path = institution_data.get('logo_path', '')

        # الحصول على مسار الشعار من قاعدة البيانات
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result and result[0]:
                logo_path = result[0]
            conn.close()
        except Exception as db_error:
            print(f"خطأ في استخراج مسار الشعار من قاعدة البيانات: {db_error}")

        # إنشاء مستند PDF (أفقي)
        doc = SimpleDocTemplate(
            file_path,
            pagesize=landscape(A4),  # صفحة أفقية
            rightMargin=0.2*cm,  # هامش 0.2 سم
            leftMargin=0.2*cm,
            topMargin=0.2*cm,
            bottomMargin=0.2*cm
        )

        # تحديد اللون الأزرق الغامق
        dark_blue = colors.Color(0, 0.35, 0.6)  # أزرق غامق

        # قائمة لتخزين عناصر المستند
        elements = []

        # إضافة الشعار إذا كان موجوداً
        if logo_path and os.path.exists(logo_path):
            try:
                # إضافة الشعار في وسط الصفحة بعرض 200 وارتفاع 90
                img = Image(logo_path, width=200, height=90)
                img.hAlign = 'CENTER'  # محاذاة وسط
                elements.append(img)
                elements.append(Spacer(1, 0.5*cm))  # مسافة 0.5 سم
            except Exception as img_error:
                print(f"خطأ في إضافة الشعار: {img_error}")

        # إنشاء أنماط الخطوط
        institution_style = ParagraphStyle(
            name='InstitutionName',
            fontName='Calibri-Bold' if 'Calibri-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
            fontSize=17,
            alignment=1,  # وسط
            textColor=dark_blue,
            spaceAfter=0.5*cm
        )

        main_title_style = ParagraphStyle(
            name='MainTitle',
            fontName='Calibri-Bold' if 'Calibri-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
            fontSize=16,
            alignment=1,  # وسط
            textColor=dark_blue,
            spaceAfter=0.5*cm
        )

        subtitle_style = ParagraphStyle(
            name='SubTitle',
            fontName='Calibri' if 'Calibri' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
            fontSize=14,
            alignment=1,  # وسط
            textColor=dark_blue,
            spaceAfter=0.5*cm
        )

        content_style = ParagraphStyle(
            name='Content',
            fontName='Calibri' if 'Calibri' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
            fontSize=12,
            alignment=1,  # وسط
            spaceAfter=0.5*cm
        )

        # إضافة اسم المؤسسة
        elements.append(Paragraph(fix_arabic_text(institution_name), institution_style))

        # إضافة العنوان الرئيسي
        main_title = "سجلات ورقة السماح بالدخول والتأخر"
        elements.append(Paragraph(fix_arabic_text(main_title), main_title_style))

        # إضافة العنوان الفرعي (السنة الدراسية)
        subtitle = f"السنة الدراسية: {school_year}"
        elements.append(Paragraph(fix_arabic_text(subtitle), subtitle_style))

        # إضافة بيانات التلميذ
        student_info = f"التلميذ(ة): {entry_data.get('student_name', '')} - الرمز: {entry_data.get('student_code', '')}"
        elements.append(Paragraph(fix_arabic_text(student_info), content_style))

        # إضافة معلومات المستوى والقسم
        level_info = f"المستوى: {entry_data.get('level', '')}"
        class_info = f"القسم: {entry_data.get('class', '')}"

        # إنشاء سطر واحد للمستوى والقسم
        level_class_info = f"{level_info} - {class_info}"
        elements.append(Paragraph(fix_arabic_text(level_class_info), content_style))
        elements.append(Spacer(1, 0.5*cm))

        # تهيئة رؤوس الجدول
        headers = [
            fix_arabic_text("التأخر (تاريخ - وقت)"),
            fix_arabic_text("الدخول (تاريخ - وقت)"),
            fix_arabic_text("ر.ت")
        ]

        # استخراج السجلات
        permission_records = entry_data.get('permission_records', [])
        late_records = entry_data.get('late_records', [])

        # تحديد عدد الصفوف بناءً على الأطول
        max_len = max(len(permission_records), len(late_records))
        table_data = [headers]

        # إنشاء الصفوف
        for i in range(max_len):
            row = []

            # بيانات التأخر
            if i < len(late_records):
                l_date = late_records[i][1]
                l_time = late_records[i][2]
                row.append(fix_arabic_text(f"{l_date} - {l_time}"))
            else:
                row.append("")

            # بيانات الدخول
            if i < len(permission_records):
                p_date = permission_records[i][1]
                p_time = permission_records[i][2]
                row.append(fix_arabic_text(f"{p_date} - {p_time}"))
            else:
                row.append("")

            # رقم الورقة (ر.ت)
            row_number = str(i + 1)
            row.append(fix_arabic_text(row_number))

            table_data.append(row)

        # تحديد عرض الأعمدة (محدث للصفحة الأفقية)
        col_widths = [8*cm, 8*cm, 4*cm]  # مساحة أكبر للصفحة الأفقية

        # إنشاء الجدول
        table = Table(table_data, colWidths=col_widths)

        # تنسيق الجدول
        table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('FONTNAME', (0, 0), (-1, -1), 'Calibri' if 'Calibri' in pdfmetrics.getRegisteredFontNames() else 'Arabic'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        # إنشاء نمط للتوقيع
        signature_style = ParagraphStyle(
            name='Signature',
            fontName='Calibri-Bold' if 'Calibri-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
            fontSize=12,
            alignment=2,  # يمين
        )

        date_style = ParagraphStyle(
            name='Date',
            fontName='Calibri' if 'Calibri' in pdfmetrics.getRegisteredFontNames() else 'Arabic',
            fontSize=12,
            alignment=0,  # يسار
        )

        # إضافة التوقيع والتاريخ
        today = datetime.now().strftime("%d-%m-%Y")  # تنسيق التاريخ مثل الصورة

        # إنشاء جدول للتوقيع والتاريخ
        signature_data = [[
            Paragraph(fix_arabic_text(f"تاريخ الطباعة: {today}"), date_style),
            Paragraph(fix_arabic_text("توقيع الحراسة العامة"), signature_style)
        ]]

        signature_table = Table(signature_data, colWidths=[doc.width/2, doc.width/2])
        signature_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        # إنشاء قائمة للعناصر النهائية
        final_elements = []

        # إضافة الجدول إلى العناصر النهائية
        final_elements.append(table)

        # إضافة مسافة 2 سم قبل التوقيع
        final_elements.append(Spacer(1, 2*cm))

        # إضافة التوقيع والتاريخ
        final_elements.append(signature_table)

        # إضافة العناصر النهائية إلى المستند
        elements.extend(final_elements)

        # تعريف دالة مخصصة لإضافة العناوين في الصفحات التالية
        class CustomCanvas:
            def __init__(self, canvas, doc, entry_data, main_title, subtitle, dark_blue):
                self.canvas = canvas
                self.doc = doc
                self.entry_data = entry_data
                self.main_title = main_title
                self.subtitle = subtitle
                self.dark_blue = dark_blue
                self.page_count = 0

            def on_page(self, canvas, _):
                # استخدام _ بدلاً من doc لتجنب تحذير عدم استخدام المتغير
                self.page_count += 1
                canvas.saveState()

                # إضافة العناوين في الصفحات التالية (إذا وجدت)
                if self.page_count > 1:
                    # تحديد موضع العناوين (للصفحة الأفقية)
                    page_width = landscape(A4)[0]  # عرض الصفحة الأفقية
                    page_height = landscape(A4)[1]  # ارتفاع الصفحة الأفقية

                    # إضافة العنوان الرئيسي
                    canvas.setFont('Calibri-Bold' if 'Calibri-Bold' in pdfmetrics.getRegisteredFontNames() else 'Arabic', 16)
                    canvas.setFillColor(self.dark_blue)
                    canvas.drawCentredString(page_width / 2, page_height - 30, fix_arabic_text(self.main_title))

                    # إضافة العنوان الفرعي
                    canvas.setFont('Calibri' if 'Calibri' in pdfmetrics.getRegisteredFontNames() else 'Arabic', 14)
                    canvas.drawCentredString(page_width / 2, page_height - 50, fix_arabic_text(self.subtitle))

                    # إضافة بيانات التلميذ
                    student_info = f"التلميذ(ة): {self.entry_data.get('student_name', '')} - الرمز: {self.entry_data.get('student_code', '')}"
                    level_info = f"المستوى: {self.entry_data.get('level', '')}"
                    class_info = f"القسم: {self.entry_data.get('class', '')}"
                    level_class_info = f"{level_info} - {class_info}"

                    canvas.setFont('Calibri' if 'Calibri' in pdfmetrics.getRegisteredFontNames() else 'Arabic', 12)
                    canvas.drawCentredString(page_width / 2, page_height - 70, fix_arabic_text(student_info))
                    canvas.drawCentredString(page_width / 2, page_height - 85, fix_arabic_text(level_class_info))

                    # إضافة مسافة 0.5 سم بعد بيانات التلميذ
                    # تعديل موضع بداية المحتوى
                    canvas.translate(0, -100)

                canvas.restoreState()

        # إنشاء كائن للتعامل مع الصفحات التالية
        canvas_handler = CustomCanvas(None, None, entry_data, main_title, subtitle, dark_blue)

        # بناء المستند مع إضافة العناوين في الصفحات التالية
        doc.build(elements, onLaterPages=canvas_handler.on_page)

        # التحقق من وجود الملف بعد الحفظ
        if os.path.exists(file_path):
            print(f"تم إنشاء ملف PDF بنجاح: {file_path}")
            print(f"حجم الملف: {os.path.getsize(file_path)} بايت")
            return True
        else:
            print(f"خطأ: الملف غير موجود بعد محاولة الحفظ: {file_path}")
            return False

    except Exception as e:
        print(f"خطأ في إنشاء ملف PDF: {str(e)}")
        traceback.print_exc()
        return False

def print_entry_records(institution_data, entry_data):
    """طباعة سجلات الدخول والتأخر"""
    try:
        print("بدء تنفيذ دالة print_entry_records")
        print(f"بيانات المؤسسة: {institution_data}")
        print(f"بيانات التلميذ: {entry_data}")

        # استخراج اسم التلميذ ورمزه لاستخدامهما في اسم الملف
        student_name = entry_data.get('student_name', 'بدون_اسم').replace(' ', '_')
        student_code = entry_data.get('student_code', 'بدون_رمز')
        current_date = datetime.now().strftime('%Y%m%d')
        print(f"اسم التلميذ: {student_name}, رمز التلميذ: {student_code}, التاريخ: {current_date}")

        # استخدام المجلد المحدد في البيانات المرسلة إذا كان موجوداً
        output_dir = institution_data.get('output_dir', None)
        custom_filename = institution_data.get('file_name', None)

        if output_dir and os.path.exists(output_dir):
            entry_records_dir = output_dir
            print(f"استخدام المجلد المحدد: {entry_records_dir}")
        else:
            # إنشاء مجلد تقارير_سجلات_ورقة_الدخول داخل مجلد البرنامج كاحتياطي
            entry_records_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "تقارير_سجلات_ورقة_الدخول")
            os.makedirs(entry_records_dir, exist_ok=True)
            print(f"تم إنشاء المجلد الاحتياطي: {entry_records_dir}")

        # إنشاء اسم الملف باستخدام اسم التلميذ ورمزه وتاريخ الطباعة
        if custom_filename:
            filename = os.path.join(entry_records_dir, f"{custom_filename}.pdf")
            print(f"استخدام اسم الملف المخصص: {filename}")
        else:
            filename = os.path.join(entry_records_dir, f"سجلات_الدخول_{student_name}_{student_code}_{current_date}.pdf")
        print(f"اسم الملف: {filename}")

        # إنشاء ملف PDF
        print("جاري إنشاء ملف PDF...")

        # استدعاء دالة إنشاء ملف PDF
        result = generate_entry_records_pdf(filename, institution_data, entry_data)

        if result:
            print(f"تم إنشاء ملف PDF بنجاح: {filename}")

            # استخدام رسالة تأكيد مخصصة من ملف sub100_window.py
            try:
                # استيراد فئة ConfirmationDialogs من ملف sub100_window.py
                from sub100_window import ConfirmationDialogs

                # إنشاء نافذة حوار مخصصة للتقرير
                class ReportConfirmationDialog(QDialog):
                    def __init__(self, parent=None):
                        super().__init__(parent)
                        self.setWindowTitle("تم إنشاء التقرير")
                        self.setFixedSize(450, 250)
                        self.setLayoutDirection(Qt.RightToLeft)

                        # إضافة أيقونة البرنامج
                        try:
                            app_icon = QIcon("01.ico")
                            self.setWindowIcon(app_icon)
                        except Exception as e:
                            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

                        # تنسيق النافذة
                        self.setStyleSheet("""
                            QDialog {
                                background-color: #f0f8ff;
                                border: 2px solid #0066cc;
                                border-radius: 10px;
                            }
                            QLabel {
                                color: #333333;
                                font-weight: bold;
                            }
                            QPushButton {
                                border-radius: 5px;
                                padding: 8px 15px;
                                font-weight: bold;
                                min-height: 35px;
                            }
                            QPushButton#preview_btn {
                                background-color: #27ae60;
                                color: white;
                            }
                            QPushButton#preview_btn:hover {
                                background-color: #2ecc71;
                                border: 2px solid #27ae60;
                            }
                            QPushButton#cancel_btn {
                                background-color: #e74c3c;
                                color: white;
                            }
                            QPushButton#cancel_btn:hover {
                                background-color: #c0392b;
                                border: 2px solid #e74c3c;
                            }
                        """)

                        # إنشاء تخطيط النافذة
                        layout = QVBoxLayout(self)
                        layout.setContentsMargins(20, 20, 20, 20)
                        layout.setSpacing(15)

                        # إضافة أيقونة وعنوان
                        header_layout = QHBoxLayout()

                        # محاولة إضافة أيقونة البرنامج
                        icon_label = QLabel()
                        icon_label.setAlignment(Qt.AlignCenter)
                        try:
                            program_icon = QPixmap("01.ico")
                            if not program_icon.isNull():
                                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                                icon_label.setPixmap(program_icon)
                                header_layout.addWidget(icon_label)
                        except Exception as e:
                            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

                        # إضافة عنوان النافذة
                        title_label = QLabel("تم إنشاء التقرير بنجاح")
                        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
                        title_label.setStyleSheet("color: #0066cc;")
                        title_label.setAlignment(Qt.AlignCenter)
                        header_layout.addWidget(title_label, 1)

                        layout.addLayout(header_layout)

                        # إضافة رسالة التأكيد
                        message_label = QLabel("تم إنشاء تقرير سجلات الدخول والتأخر بنجاح.\nهل ترغب في معاينة وطباعة التقرير الآن؟")
                        message_label.setFont(QFont("Calibri", 13, QFont.Bold))
                        message_label.setAlignment(Qt.AlignCenter)
                        message_label.setWordWrap(True)
                        layout.addWidget(message_label)

                        # إضافة أزرار المعاينة والإلغاء
                        buttons_layout = QHBoxLayout()

                        preview_btn = QPushButton("معاينة وطباعة")
                        preview_btn.setObjectName("preview_btn")
                        preview_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                        preview_btn.setCursor(Qt.PointingHandCursor)
                        preview_btn.setFixedWidth(150)
                        preview_btn.clicked.connect(self.accept)

                        cancel_btn = QPushButton("إلغاء")
                        cancel_btn.setObjectName("cancel_btn")
                        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                        cancel_btn.setCursor(Qt.PointingHandCursor)
                        cancel_btn.setFixedWidth(105)
                        cancel_btn.clicked.connect(self.reject)

                        buttons_layout.addWidget(preview_btn)
                        buttons_layout.addWidget(cancel_btn)

                        layout.addLayout(buttons_layout)

                # استخدام دالة عرض رسالة النجاح المخصصة من ConfirmationDialogs إذا كانت متوفرة
                if hasattr(ConfirmationDialogs, 'show_report_confirmation_dialog'):
                    # استخدام دالة عرض رسالة تأكيد التقرير المخصصة
                    custom_dialog = ConfirmationDialogs.show_report_confirmation_dialog(None, "تم إنشاء تقرير سجلات الدخول والتأخر بنجاح")
                    response = custom_dialog.exec_()

                    # تحويل نتيجة النافذة المخصصة إلى ما يعادل QMessageBox.Yes أو QMessageBox.No
                    if response == QDialog.Accepted:
                        response = QMessageBox.Yes
                    else:
                        response = QMessageBox.No
                else:
                    # استخدام النافذة المخصصة المحلية
                    custom_dialog = ReportConfirmationDialog(None)
                    response = custom_dialog.exec_()

                    # تحويل نتيجة النافذة المخصصة إلى ما يعادل QMessageBox.Yes أو QMessageBox.No
                    if response == QDialog.Accepted:
                        response = QMessageBox.Yes
                    else:
                        response = QMessageBox.No

            except ImportError:
                # في حالة عدم وجود ملف sub100_window.py، استخدم رسالة التأكيد العادية
                print("تعذر استيراد ملف sub100_window.py، سيتم استخدام رسالة التأكيد العادية")

                # عرض رسالة تأكيد للمستخدم
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Information)
                msg_box.setWindowTitle("تم إنشاء التقرير")
                msg_box.setText(f"تم إنشاء تقرير سجلات الدخول والتأخر بنجاح.")
                msg_box.setInformativeText("هل ترغب في فتح التقرير الآن؟")
                msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
                msg_box.setDefaultButton(QMessageBox.Yes)

                # تعديل أنماط الخطوط في مربع الحوار
                font = msg_box.font()
                font.setPointSize(10)
                font.setBold(True)
                msg_box.setFont(font)

                # عرض مربع الحوار وانتظار رد المستخدم
                response = msg_box.exec_()

            # إذا اختار المستخدم "نعم"، افتح الملف
            if response == QMessageBox.Yes:
                print("جاري فتح الملف...")
                if open_pdf(filename):
                    print("تم فتح الملف بنجاح")
                else:
                    print("فشل في فتح الملف")
                    QMessageBox.warning(None, "تنبيه", f"تم إنشاء الملف بنجاح في:\n{filename}\nولكن تعذر فتحه تلقائياً.")

            return True
        else:
            print("فشل في إنشاء ملف PDF")
            QMessageBox.critical(None, "خطأ", "فشل في إنشاء ملف PDF.")
            return False

    except Exception as e:
        print(f"خطأ في طباعة سجلات الدخول: {str(e)}")
        traceback.print_exc()
        QMessageBox.critical(None, "خطأ طباعة", f"حدث خطأ أثناء طباعة سجلات الدخول:\n{e}")
        return False

# اختبار الطباعة
if __name__ == "__main__":
    # بيانات اختبار
    institution_data = {
        'institution': 'الثانوية الإعدادية النموذجية',
        'school_year': '2024/2025'
    }

    entry_data = {
        'student_code': 'A123456',
        'student_name': 'محمد أحمد',
        'level': 'الثالثة إعدادي',
        'class': '3-2',
        'permission_records': [
            ['1', '2024-05-01', '08:30', 'الأول', '2024/2025'],
            ['2', '2024-05-05', '09:15', 'الأول', '2024/2025']
        ],
        'late_records': [
            ['3', '2024-05-10', '08:45', 'الأول', '2024/2025'],
            ['4', '2024-05-15', '09:30', 'الأول', '2024/2025']
        ]
    }

    print_entry_records(institution_data, entry_data)
