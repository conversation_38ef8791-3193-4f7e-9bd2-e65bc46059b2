"""
print0.py - وحدة إنشاء تقرير الإحصائيات العامة للمؤسسة (محسّن)

هذا الملف مسؤول عن إنشاء تقارير PDF جميلة وجذابة للإحصائيات العامة للمؤسسة.
يتم استدعاؤه من ملف sub5_window.py عند الضغط على زر "طباعة التقرير".

الميزات الجديدة:
- تصميم أنيق بألوان زاهية ومتدرجة
- تأثيرات بصرية ثلاثية الأبعاد
- ظلال متدرجة وحدود ذهبية
- بطاقات إحصائية جذابة
- تذييل متطور

تاريخ الإنشاء: 2023-10-01
آخر تحديث: 2024-01-15 (تحسينات التصميم)
"""

import os
import sqlite3
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.colors import HexColor
import arabic_reshaper
from bidi.algorithm import get_display

class StatisticsReport:
    """فئة مسؤولة عن إنشاء تقرير الإحصائيات العامة للمؤسسة"""

    def __init__(self, db_path="data.db"):
        """تهيئة الفئة مع مسار قاعدة البيانات"""
        self.db_path = db_path

    def get_academic_year(self):
        """استخراج السنة الدراسية الحالية من جدول بيانات_المؤسسة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            return ""

        except Exception as e:
            print(f"خطأ في استخراج السنة الدراسية: {e}")
            return ""

    def get_statistics(self, academic_year=""):
        """استخراج البيانات الإحصائية من قاعدة البيانات"""
        stats = {
            "levels": 0,
            "sections": 0,
            "students": 0,
            "males": 0,
            "females": 0,
            "age_groups": {}  # سيتم تخزين كل عمر كمفتاح مع عدد التلاميذ كقيمة
        }

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج الإحصائيات العامة
            year_condition = " WHERE السنة_الدراسية = ?" if academic_year else ""
            params = [academic_year] if academic_year else []

            # استعلامات الإحصائيات الأساسية
            cursor.execute(f"SELECT COUNT(DISTINCT المستوى) FROM البنية_التربوية{year_condition}", params)
            stats["levels"] = cursor.fetchone()[0] or 0

            cursor.execute(f"SELECT COUNT(DISTINCT القسم) FROM البنية_التربوية{year_condition}", params)
            stats["sections"] = cursor.fetchone()[0] or 0

            cursor.execute(f"SELECT SUM(مجموع_التلاميذ) FROM البنية_التربوية{year_condition}", params)
            stats["students"] = cursor.fetchone()[0] or 0

            # استعلام النوع
            try:
                gender_query = """
                    SELECT النوع, COUNT(*)
                    FROM السجل_العام s
                    JOIN اللوائح l ON s.الرمز = l.الرمز
                """
                if academic_year:
                    gender_query += " WHERE l.السنة_الدراسية = ?"
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query, [academic_year])
                else:
                    gender_query += " GROUP BY النوع"
                    cursor.execute(gender_query)

                gender_stats = cursor.fetchall()
                for gender, count in gender_stats:
                    if gender and gender.strip().lower() in ['ذكر', 'ذ', 'm', 'male']:
                        stats["males"] += count
                    elif gender and gender.strip().lower() in ['أنثى', 'ا', 'f', 'female']:
                        stats["females"] += count

            except sqlite3.Error as e:
                print(f"خطأ في استرجاع إحصائيات النوع: {e}")

            conn.close()

        except Exception as e:
            print(f"خطأ في استرجاع الإحصائيات: {e}")

        return stats

    def calculate_age_stats(self, academic_year, reference_date):
        """حساب إحصائيات الأعمار بناءً على تاريخ مرجعي محدد"""
        age_stats = {}  # قاموس لتخزين كل عمر مع عدد التلاميذ

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            age_query = """
                SELECT تاريخ_الازدياد
                FROM السجل_العام s
                JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE l.السنة_الدراسية = ?
            """

            cursor.execute(age_query, [academic_year])
            birth_dates = cursor.fetchall()

            for (birth_date,) in birth_dates:
                try:
                    if isinstance(birth_date, str):
                        birth_date = birth_date.strip()

                        if '/' in birth_date:
                            day, month, year = map(int, birth_date.split('/'))
                        elif '-' in birth_date:
                            year, month, day = map(int, birth_date.split('-'))
                        else:
                            year = int(birth_date)
                            month, day = 1, 1

                        if year < 100:
                            year += 2000 if year < 30 else 1900

                        try:
                            birth_date = datetime(year, month, day)
                        except ValueError as e:
                            if "day is out of range for month" in str(e):
                                if month == 2:
                                    day = 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28
                                elif month in [4, 6, 9, 11]:
                                    day = 30
                                else:
                                    day = 31
                                birth_date = datetime(year, month, day)
                            else:
                                raise
                    else:
                        birth_date = datetime(int(birth_date), 1, 1)

                    # حساب العمر
                    age = reference_date.year - birth_date.year
                    if reference_date.month < birth_date.month or \
                       (reference_date.month == birth_date.month and \
                        reference_date.day < birth_date.day):
                        age -= 1

                    # إضافة العمر إلى القاموس مع زيادة العداد
                    age_stats[age] = age_stats.get(age, 0) + 1

                except Exception as e:
                    print(f"خطأ في معالجة تاريخ الميلاد: {birth_date}, {str(e)}")
                    continue

            conn.close()

        except Exception as e:
            print(f"خطأ في حساب إحصائيات الأعمار: {str(e)}")

        return age_stats

    def get_school_info(self):
        """استخراج بيانات المؤسسة من قاعدة البيانات"""
        school_info = {}
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT * FROM بيانات_المؤسسة LIMIT 1")
            columns = [column[0] for column in cursor.description]
            result = cursor.fetchone()

            if result:
                school_info = {columns[i]: result[i] for i in range(len(columns))}

                # إضافة مسار الشعار إذا كان موجوداً
                school_info["logo_path"] = school_info.get("ImagePath1", "")

            conn.close()

        except Exception as e:
            print(f"خطأ في استخراج بيانات المؤسسة: {e}")

        return school_info

    def create_pdf_report(self, filepath, stats_data, reference_date):
        """إنشاء ملف PDF للتقرير مطابق تماماً لواجهة sub5_window_html.py

        المعلمات:
            filepath (str): مسار ملف PDF الذي سيتم إنشاؤه
            stats_data (dict): بيانات الإحصائيات التي سيتم عرضها في التقرير
            reference_date (datetime): تاريخ المرجع المستخدم لحساب أعمار التلاميذ

        العائد:
            bool: True إذا تم إنشاء التقرير بنجاح، False في حالة حدوث خطأ
        """
        try:
            # محاولة تسجيل الخط العربي من عدة مصادر
            font_paths = [
                os.path.join(os.path.dirname(__file__), "fonts", "arial.ttf"),
                r"C:\Windows\Fonts\arial.ttf",
                r"C:\Windows\Fonts\arialbd.ttf",
                r"C:\Windows\Fonts\calibri.ttf",
                r"C:\Windows\Fonts\tahoma.ttf"
            ]

            font_registered = False
            for font_path in font_paths:
                try:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont("Arabic", font_path))
                        font_registered = True
                        break
                except:
                    continue

            # إنشاء ملف PDF بتوجه أفقي (landscape)
            from reportlab.lib.pagesizes import landscape
            c = canvas.Canvas(filepath, pagesize=landscape(A4))
            width, height = landscape(A4)

            def reshape_arabic(text):
                return get_display(arabic_reshaper.reshape(str(text)))

            # استخراج البيانات
            school_info = self.get_school_info()

            # تعيين الخط المناسب
            font_name = "Arabic" if font_registered else "Helvetica"

            # خلفية متدرجة مطابقة للواجهة الأصلية (من الأزرق إلى البنفسجي)
            gradient_steps = 100
            for i in range(gradient_steps):
                progress = i / gradient_steps
                # تدرج من #667eea إلى #764ba2 مطابق للواجهة الأصلية
                start_r, start_g, start_b = 102, 126, 234  # #667eea
                end_r, end_g, end_b = 118, 75, 162         # #764ba2

                r = int(start_r + (end_r - start_r) * progress)
                g = int(start_g + (end_g - start_g) * progress)
                b = int(start_b + (end_b - start_b) * progress)

                step_height = height / gradient_steps
                c.setFillColor(HexColor(f'#{r:02x}{g:02x}{b:02x}'))
                c.rect(0, i * step_height, width, step_height, fill=True, stroke=False)

            # رأس التقرير مطابق للواجهة الأصلية ومحاذي لليمين
            header_height = 80
            header_y = height - 100

            # خلفية الرأس بتدرج أخضر مطابق للواجهة الأصلية (#90EE90 إلى #98FB98)
            for i in range(header_height):
                progress = i / header_height
                # تدرج من #90EE90 إلى #98FB98
                start_r, start_g, start_b = 144, 238, 144  # #90EE90
                end_r, end_g, end_b = 152, 251, 152        # #98FB98

                r = int(start_r + (end_r - start_r) * progress)
                g = int(start_g + (end_g - start_g) * progress)
                b = int(start_b + (end_b - start_b) * progress)

                c.setFillColor(HexColor(f'#{r:02x}{g:02x}{b:02x}'))
                c.rect(20, header_y + i, width - 40, 1, fill=True, stroke=False)  # البدء من اليمين

            # حدود الرأس مطابقة للواجهة الأصلية
            c.setStrokeColor(HexColor('#FFFFFF'))  # حدود بيضاء شفافة
            c.setLineWidth(2)
            c.roundRect(20, header_y, width - 40, header_height, 20, fill=False, stroke=True)  # البدء من اليمين

            # العنوان الرئيسي مطابق للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
            c.setFont(font_name, 24)  # حجم مطابق للواجهة
            title_text = " الإحصائيات العامة"
            c.drawCentredString(width/2, header_y + 50, reshape_arabic(title_text))

            # معلومات السنة الدراسية مطابقة للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
            c.setFont(font_name, 14)
            academic_year = school_info.get('السنة_الدراسية', 'غير محدد')
            year_text = f"للسنة الدراسية: {academic_year}"
            c.drawCentredString(width/2, header_y + 30, reshape_arabic(year_text))

            # اسم المؤسسة مطابق للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
            c.setFont(font_name, 14)
            school_name = school_info.get('المؤسسة', 'مؤسسة التعليم')
            c.drawCentredString(width/2, header_y + 10, reshape_arabic(school_name))

            # قسم الإحصائيات العامة مطابق للواجهة الأصلية
            y = height - 150

            # عنوان القسم الأول مطابق للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
            c.setFont(font_name, 20)
            section_title = " الإحصائيات العامة"
            c.drawCentredString(width/2, y, reshape_arabic(section_title))

            # البطاقات الإحصائية مطابقة للواجهة الأصلية
            y -= 40
            card_data = [
                {
                    "icon": "",
                    "value": str(stats_data.get("students", 0)),
                    "label": "إجمالي التلاميذ",
                    "description": "العدد الكلي للتلاميذ المسجلين",
                    "color": "#2196f3"  # أزرق مطابق للواجهة
                },
                {
                    "icon": "",
                    "value": str(stats_data.get("sections", 0)),
                    "label": "عدد الأقسام",
                    "description": "الأقسام المختلفة في المؤسسة",
                    "color": "#4caf50"  # أخضر مطابق للواجهة
                },
                {
                    "icon": "",
                    "value": str(stats_data.get("levels", 0)),
                    "label": "عدد المستويات",
                    "description": "المستويات التعليمية المتاحة",
                    "color": "#ff9800"  # برتقالي مطابق للواجهة
                },
                {
                    "icon": "",
                    "value": str(int(stats_data.get("students", 0) / stats_data.get("sections", 1)) if stats_data.get("sections", 0) > 0 else 0),
                    "label": "معدل التلاميذ/قسم",
                    "description": "متوسط عدد التلاميذ في كل قسم",
                    "color": "#9c27b0"  # بنفسجي مطابق للواجهة
                }
            ]

            # رسم البطاقات في شبكة 4x1 في وسط الصفحة
            cards_per_row = 4
            card_width = 180  # عرض ثابت للبطاقة
            card_spacing = 15  # المسافة بين البطاقات
            total_cards_width = (card_width * cards_per_row) + (card_spacing * (cards_per_row - 1))
            start_x = (width - total_cards_width) / 2  # البدء من الوسط
            card_height = 100

            for i, card in enumerate(card_data):
                col = i % cards_per_row
                x = start_x + col * (card_width + card_spacing)  # توسيط البطاقات
                card_y = y

                # ظل البطاقة مطابق للواجهة الأصلية
                c.setFillColor(HexColor('#E0E0E0'))
                c.roundRect(x + 3, card_y - card_height - 3, card_width, card_height, 20, fill=True, stroke=False)

                # خلفية البطاقة البيضاء مطابقة للواجهة الأصلية
                c.setFillColor(HexColor('#FFFFFF'))
                c.setStrokeColor(HexColor('#F0F0F0'))
                c.setLineWidth(1)
                c.roundRect(x, card_y - card_height, card_width, card_height, 20, fill=True, stroke=True)

              
                

                # الأيقونة مطابقة للواجهة الأصلية
                c.setFillColor(HexColor(card["color"]))
                c.setFont(font_name, 28)
                c.drawCentredString(x + card_width/2, card_y - 30, card["icon"])

                # القيمة الرقمية مطابقة للواجهة الأصلية
                c.setFillColor(HexColor(card["color"]))
                c.setFont(font_name, 28)
                c.drawCentredString(x + card_width/2, card_y - 55, card["value"])

                # العنوان مطابق للواجهة الأصلية
                c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
                c.setFont(font_name, 14)
                c.drawCentredString(x + card_width/2, card_y - 75, reshape_arabic(card["label"]))

                # الوصف مطابق للواجهة الأصلية
                c.setFillColor(HexColor('#333333'))  # رمادي غامق للوضوح
                c.setFont(font_name, 10)
                c.drawCentredString(x + card_width/2, card_y - 90, reshape_arabic(card["description"]))

            # قسم إحصائيات النوع مطابق للواجهة الأصلية
            y -= 140

            # عنوان القسم الثاني مطابق للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
            c.setFont(font_name, 20)
            gender_title = " الإحصائيات حسب النوع"
            c.drawCentredString(width/2, y, reshape_arabic(gender_title))

            # حساب الإحصائيات
            males = stats_data.get("males", 0)
            females = stats_data.get("females", 0)
            total_gender = males + females
            female_percentage = round((females / total_gender) * 100, 1) if total_gender > 0 else 0

            # بطاقات النوع مطابقة للواجهة الأصلية
            y -= 40
            gender_cards = [
                {
                    "icon": "",
                    "value": str(males),
                    "label": "عدد الذكور",
                    "description": "التلاميذ الذكور",
                    "color": "#3f51b5"  # أزرق مطابق للواجهة
                },
                {
                    "icon": "",
                    "value": str(females),
                    "label": "عدد الإناث",
                    "description": "التلميذات الإناث",
                    "color": "#e91e63"  # وردي مطابق للواجهة
                },
                {
                    "icon": "",
                    "value": str(total_gender),
                    "label": "الإجمالي",
                    "description": "المجموع الكلي",
                    "color": "#009688"  # تركوازي مطابق للواجهة
                },
                {
                    "icon": "",
                    "value": f"{female_percentage}%",
                    "label": "نسبة الإناث",
                    "description": "النسبة المئوية للإناث",
                    "color": "#ffc107"  # أصفر مطابق للواجهة
                }
            ]

            # رسم بطاقات النوع في وسط الصفحة
            for i, card in enumerate(gender_cards):
                col = i % cards_per_row
                x = start_x + col * (card_width + card_spacing)  # توسيط البطاقات
                card_y = y

                # ظل البطاقة مطابق للواجهة الأصلية
                c.setFillColor(HexColor('#E0E0E0'))
                c.roundRect(x + 3, card_y - card_height - 3, card_width, card_height, 20, fill=True, stroke=False)

                # خلفية البطاقة البيضاء مطابقة للواجهة الأصلية
                c.setFillColor(HexColor('#FFFFFF'))
                c.setStrokeColor(HexColor('#F0F0F0'))
                c.setLineWidth(1)
                c.roundRect(x, card_y - card_height, card_width, card_height, 20, fill=True, stroke=True)

               

                # الأيقونة مطابقة للواجهة الأصلية
                c.setFillColor(HexColor(card["color"]))
                c.setFont(font_name, 28)
                c.drawCentredString(x + card_width/2, card_y - 30, card["icon"])

                # القيمة الرقمية مطابقة للواجهة الأصلية
                c.setFillColor(HexColor(card["color"]))
                c.setFont(font_name, 28)
                c.drawCentredString(x + card_width/2, card_y - 55, card["value"])

                # العنوان مطابق للواجهة الأصلية
                c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
                c.setFont(font_name, 14)
                c.drawCentredString(x + card_width/2, card_y - 75, reshape_arabic(card["label"]))

                # الوصف مطابق للواجهة الأصلية
                c.setFillColor(HexColor('#333333'))  # رمادي غامق للوضوح
                c.setFont(font_name, 10)
                c.drawCentredString(x + card_width/2, card_y - 90, reshape_arabic(card["description"]))

            # تذييل بسيط مطابق للواجهة الأصلية
            y = 50

            # معلومات التقرير بتصميم بسيط
            c.setFillColor(HexColor('#000000'))  # أسود غامق للوضوح
            c.setFont(font_name, 12)
            report_info = f"تم إنشاء التقرير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            c.drawCentredString(width/2, y, reshape_arabic(report_info))

            # حفظ الملف
            c.save()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {e}")
            return False

    def create_age_report(self, filepath, reference_date):
        """إنشاء تقرير PDF للأعمار مطابق تماماً لواجهة sub5_window_html.py

        المعلمات:
            filepath (str): مسار ملف PDF الذي سيتم إنشاؤه
            reference_date (datetime): تاريخ المرجع المستخدم لحساب أعمار التلاميذ

        العائد:
            bool: True إذا تم إنشاء التقرير بنجاح، False في حالة حدوث خطأ
        """
        try:
            # محاولة تسجيل الخط العربي من عدة مصادر
            font_paths = [
                os.path.join(os.path.dirname(__file__), "fonts", "arial.ttf"),
                r"C:\Windows\Fonts\arial.ttf",
                r"C:\Windows\Fonts\arialbd.ttf",
                r"C:\Windows\Fonts\calibri.ttf",
                r"C:\Windows\Fonts\tahoma.ttf"
            ]

            font_registered = False
            for font_path in font_paths:
                try:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont("Arabic", font_path))
                        font_registered = True
                        break
                except:
                    continue

            # إنشاء ملف PDF بتوجه أفقي (landscape)
            from reportlab.lib.pagesizes import landscape
            c = canvas.Canvas(filepath, pagesize=landscape(A4))
            width, height = landscape(A4)

            def reshape_arabic(text):
                return get_display(arabic_reshaper.reshape(str(text)))

            # استخراج البيانات
            school_info = self.get_school_info()
            academic_year = self.get_academic_year()
            age_stats = self.calculate_age_stats(academic_year, reference_date)

            # تعيين الخط المناسب
            font_name = "Arabic" if font_registered else "Helvetica"

            # خلفية متدرجة مطابقة للواجهة الأصلية (من الأزرق إلى البنفسجي)
            gradient_steps = 100
            for i in range(gradient_steps):
                progress = i / gradient_steps
                # تدرج من #667eea إلى #764ba2 مطابق للواجهة الأصلية
                start_r, start_g, start_b = 102, 126, 234  # #667eea
                end_r, end_g, end_b = 118, 75, 162         # #764ba2

                r = int(start_r + (end_r - start_r) * progress)
                g = int(start_g + (end_g - start_g) * progress)
                b = int(start_b + (end_b - start_b) * progress)

                step_height = height / gradient_steps
                c.setFillColor(HexColor(f'#{r:02x}{g:02x}{b:02x}'))
                c.rect(0, i * step_height, width, step_height, fill=True, stroke=False)

            # رأس التقرير مطابق للواجهة الأصلية
            header_height = 80
            header_y = height - 100

            # خلفية الرأس بتدرج أخضر مطابق للواجهة الأصلية (#90EE90 إلى #98FB98)
            for i in range(header_height):
                progress = i / header_height
                # تدرج من #90EE90 إلى #98FB98
                start_r, start_g, start_b = 144, 238, 144  # #90EE90
                end_r, end_g, end_b = 152, 251, 152        # #98FB98

                r = int(start_r + (end_r - start_r) * progress)
                g = int(start_g + (end_g - start_g) * progress)
                b = int(start_b + (end_b - start_b) * progress)

                c.setFillColor(HexColor(f'#{r:02x}{g:02x}{b:02x}'))
                c.rect(20, header_y + i, width - 40, 1, fill=True, stroke=False)

            # حدود الرأس مطابقة للواجهة الأصلية
            c.setStrokeColor(HexColor('#FFFFFF'))
            c.setLineWidth(2)
            c.roundRect(20, header_y, width - 40, header_height, 20, fill=False, stroke=True)

            # العنوان الرئيسي مطابق للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))
            c.setFont(font_name, 24)
            title_text = " إحصائيات السن"
            c.drawCentredString(width/2, header_y + 50, reshape_arabic(title_text))

            # معلومات السنة الدراسية مطابقة للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))
            c.setFont(font_name, 14)
            year_text = f"للسنة الدراسية: {academic_year}"
            c.drawCentredString(width/2, header_y + 30, reshape_arabic(year_text))

            # اسم المؤسسة مطابق للواجهة الأصلية
            c.setFillColor(HexColor('#000000'))
            c.setFont(font_name, 14)
            school_name = school_info.get('المؤسسة', 'مؤسسة التعليم')
            c.drawCentredString(width/2, header_y + 10, reshape_arabic(school_name))

            # حساب الإحصائيات العامة للسن
            total_students = sum(age_stats.values())
            min_age = min(age_stats.keys()) if age_stats else 0
            max_age = max(age_stats.keys()) if age_stats else 0
            avg_age = sum(age * count for age, count in age_stats.items()) / total_students if total_students > 0 else 0

            # إحصائيات فئات السن
            age_groups = {
                "6-10": sum(count for age, count in age_stats.items() if 6 <= age <= 10),
                "11-15": sum(count for age, count in age_stats.items() if 11 <= age <= 15),
                "16-20": sum(count for age, count in age_stats.items() if 16 <= age <= 20),
                "21+": sum(count for age, count in age_stats.items() if age >= 21)
            }

            # قسم إحصائيات السن مرتبة من الأصغر إلى الأكبر
            y = height - 150

            # عنوان القسم الرئيسي
            c.setFillColor(HexColor('#000000'))
            c.setFont(font_name, 20)
            section_title = " إحصائيات السن مرتبة من الأصغر إلى الأكبر"
            c.drawCentredString(width/2, y, reshape_arabic(section_title))

            # ترتيب الأعمار من الأصغر إلى الأكبر
            if age_stats:
                sorted_ages = sorted(age_stats.items(), key=lambda x: x[0])  # ترتيب حسب العمر
                
                # إعداد البطاقات
                y -= 40
                cards_per_row = 6  # 6 بطاقات في الصف الواحد
                card_width = 120   # عرض أصغر للبطاقات
                card_spacing = 10  # مسافة أصغر بين البطاقات
                card_height = 80   # ارتفاع أصغر للبطاقات
                
                # ألوان متدرجة للبطاقات
                colors = [
                    "#2196f3",  # أزرق
                    "#4caf50",  # أخضر
                    "#ff9800",  # برتقالي
                    "#9c27b0",  # بنفسجي
                    "#3f51b5",  # أزرق غامق
                    "#e91e63",  # وردي
                    "#009688",  # تركوازي
                    "#ffc107",  # أصفر
                    "#795548",  # بني
                    "#607d8b",  # رمادي مزرق
                    "#f44336",  # أحمر
                    "#8bc34a"   # أخضر فاتح
                ]
                
                # رسم البطاقات بترتيب الأعمار
                for i, (age, count) in enumerate(sorted_ages):
                    row = i // cards_per_row
                    col = i % cards_per_row
                    
                    # حساب موضع البطاقة
                    total_cards_in_row = min(cards_per_row, len(sorted_ages) - (row * cards_per_row))
                    total_width_in_row = (card_width * total_cards_in_row) + (card_spacing * (total_cards_in_row - 1))
                    start_x = (width - total_width_in_row) / 2
                    
                    x = start_x + col * (card_width + card_spacing)
                    card_y = y - (row * (card_height + 20))
                    
                    # اختيار لون البطاقة
                    color = colors[i % len(colors)]
                    
                    # ظل البطاقة
                    c.setFillColor(HexColor('#E0E0E0'))
                    c.roundRect(x + 2, card_y - card_height - 2, card_width, card_height, 15, fill=True, stroke=False)

                    # خلفية البطاقة البيضاء
                    c.setFillColor(HexColor('#FFFFFF'))
                    c.setStrokeColor(HexColor('#F0F0F0'))
                    c.setLineWidth(1)
                    c.roundRect(x, card_y - card_height, card_width, card_height, 15, fill=True, stroke=True)

                    
                    # رقم السن (القيمة الرئيسية)
                    c.setFillColor(HexColor(color))
                    c.setFont(font_name, 24)
                    c.drawCentredString(x + card_width/2, card_y - 30, str(age))

                    # كلمة "سنة"
                    c.setFillColor(HexColor('#000000'))
                    c.setFont(font_name, 13)
                    c.drawCentredString(x + card_width/2, card_y - 45, reshape_arabic("سنة"))

                    # عدد التلاميذ
                    c.setFillColor(HexColor('#333333'))
                    c.setFont(font_name, 14)
                    student_text = f"{count} تلميذ"
                    c.drawCentredString(x + card_width/2, card_y - 60, reshape_arabic(student_text))

                    # حساب النسبة المئوية
                    percentage = (count / total_students) * 100 if total_students > 0 else 0
                    c.setFillColor(HexColor('#666666'))
                    c.setFont(font_name, 12)
                    percentage_text = f"{percentage:.1f}%"
                    c.drawCentredString(x + card_width/2, card_y - 72, percentage_text)

                # إضافة بطاقة إحصائية عامة في الأسفل
                y = card_y - card_height - 60
                
                # عنوان الملخص
                c.setFillColor(HexColor('#000000'))
                c.setFont(font_name, 18)
                summary_title = " ملخص إحصائيات السن"
                c.drawCentredString(width/2, y, reshape_arabic(summary_title))
                
                # بطاقات الملخص
                y -= 40
                summary_cards = [
                    {
                        "icon": "",
                        "value": str(total_students),
                        "label": "إجمالي التلاميذ",
                        "description": "العدد الكلي للتلاميذ",
                        "color": "#2196f3"
                    },
                    {
                        "icon": "",
                        "value": str(min_age),
                        "label": "أصغر سن",
                        "description": "أصغر تلميذ في المؤسسة",
                        "color": "#4caf50"
                    },
                    {
                        "icon": "",
                        "value": str(max_age),
                        "label": "أكبر سن",
                        "description": "أكبر تلميذ في المؤسسة",
                        "color": "#ff9800"
                    },
                    {
                        "icon": "",
                        "value": f"{avg_age:.1f}",
                        "label": "متوسط السن",
                        "description": "متوسط أسنان التلاميذ",
                        "color": "#9c27b0"
                    }
                ]
                
                # رسم بطاقات الملخص
                cards_per_row = 4
                card_width = 160
                card_spacing = 15
                card_height = 90
                total_cards_width = (card_width * cards_per_row) + (card_spacing * (cards_per_row - 1))
                start_x = (width - total_cards_width) / 2

                for i, card in enumerate(summary_cards):
                    col = i % cards_per_row
                    x = start_x + col * (card_width + card_spacing)
                    card_y = y

                    # ظل البطاقة
                    c.setFillColor(HexColor('#E0E0E0'))
                    c.roundRect(x + 3, card_y - card_height - 3, card_width, card_height, 15, fill=True, stroke=False)

                    # خلفية البطاقة البيضاء
                    c.setFillColor(HexColor('#FFFFFF'))
                    c.setStrokeColor(HexColor('#F0F0F0'))
                    c.setLineWidth(1)
                    c.roundRect(x, card_y - card_height, card_width, card_height, 15, fill=True, stroke=True)

                    # الأيقونة
                    c.setFillColor(HexColor(card["color"]))
                    c.setFont(font_name, 24)
                    c.drawCentredString(x + card_width/2, card_y - 25, card["icon"])

                    # القيمة الرقمية
                    c.setFillColor(HexColor(card["color"]))
                    c.setFont(font_name, 22)
                    c.drawCentredString(x + card_width/2, card_y - 45, card["value"])

                    # العنوان
                    c.setFillColor(HexColor('#000000'))
                    c.setFont(font_name, 12)
                    c.drawCentredString(x + card_width/2, card_y - 60, reshape_arabic(card["label"]))

                    # الوصف
                    c.setFillColor(HexColor('#333333'))
                    c.setFont(font_name, 9)
                    c.drawCentredString(x + card_width/2, card_y - 75, reshape_arabic(card["description"]))

            # تذييل التقرير
            c.setFillColor(HexColor('#000000'))
            c.setFont(font_name, 12)
           
            # حفظ الملف
            c.save()
            return True

        except Exception as e:
            print(f"خطأ في إنشاء تقرير السن: {e}")
            return False
