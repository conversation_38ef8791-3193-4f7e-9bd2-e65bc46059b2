import sys
import os
import sqlite3
import traceback
import shutil
import time
import webbrowser
import re
from datetime import datetime
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFrame,
    QPushButton, QLabel, QComboBox, QDateEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QCheckBox, QDialog, QDialogButtonBox,
    QApplication, QStackedWidget, QTreeWidget, QTreeWidgetItem, QGridLayout,
    QScrollArea, QSizePolicy
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

# استيراد وحدة رسائل التأكيد المخصصة
from sub100_window import ConfirmationDialogs
# استيراد دالة إنشاء التقرير من print10.py
from print10 import generate_teacher_pdf_report_code, download_arabic_fonts, ArabicPDF
# استيراد دالة تقرير القسم من print100.py
from print100 import generate_section_pdf_report
# استيراد نافذة حذف الفروض الممسوكة
from sub210_window import show_delete_exams_dialog

# استيراد المكتبات اللازمة لإنشاء ملفات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.pdfbase import pdfmetrics
    from reportlab.lib.units import cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT

    # استيراد دعم العربية
    import arabic_reshaper
    from bidi.algorithm import get_display

    # تسجيل الخطوط العربية
    REPORTLAB_AVAILABLE = True

    # محاولة تسجيل خط عربي
    try:
        # البحث عن ملفات الخطوط في مجلد الخطوط
        fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
        print(f"DEBUG: Determined fonts_dir: {fonts_dir}") # Diagnostic print

        # إذا لم يكن مجلد الخطوط موجودًا، قم بإنشائه
        if not os.path.exists(fonts_dir):
            os.makedirs(fonts_dir)
            print(f"DEBUG: Created fonts_dir: {fonts_dir}") # Diagnostic print

        # قائمة بالخطوط العربية المحتملة
        arabic_fonts = [
            "Arial.ttf",
            "arialbd.ttf",
            "Calibri.ttf",
            "calibrib.ttf",
            "Tahoma.ttf",
            "tahomabd.ttf",
            "Amiri-Regular.ttf",
            "Amiri-Bold.ttf",
            "Amiri-BoldItalic.ttf",
            "Amiri-Italic.ttf"
        ]

        # البحث عن الخطوط في مجلد النظام
        system_fonts_dir = os.path.join(os.environ.get('WINDIR', ''), 'Fonts')

        # تسجيل أول خط عربي متاح
        arabic_font_registered = False

        for font_name in arabic_fonts:
            # البحث في مجلد الخطوط المحلي
            local_font_path = os.path.join(fonts_dir, font_name)
            print(f"DEBUG: Checking local_font_path: {local_font_path}") # Diagnostic print

            # البحث في مجلد خطوط النظام
            system_font_path = os.path.join(system_fonts_dir, font_name)
            print(f"DEBUG: Checking system_font_path: {system_font_path}") # Diagnostic print

            if os.path.exists(local_font_path):
                # تسجيل الخط من المجلد المحلي
                print(f"DEBUG: Attempting to register font from local_font_path: {local_font_path}") # Diagnostic print
                pdfmetrics.registerFont(TTFont('Arabic', local_font_path))
                arabic_font_registered = True
                print(f"تم تسجيل الخط العربي: {font_name} من المجلد المحلي")
                break
            elif os.path.exists(system_font_path):
                # نسخ الخط من مجلد النظام إلى المجلد المحلي
                import shutil
                shutil.copy2(system_font_path, local_font_path)
                print(f"DEBUG: Copied font from system to: {local_font_path}") # Diagnostic print

                # تسجيل الخط
                print(f"DEBUG: Attempting to register font from copied local_font_path: {local_font_path}") # Diagnostic print
                pdfmetrics.registerFont(TTFont('Arabic', local_font_path))
                arabic_font_registered = True
                print(f"تم تسجيل الخط العربي: {font_name} من مجلد النظام")
                break

        # إذا لم يتم تسجيل أي خط عربي، استخدم الخط الافتراضي
        if not arabic_font_registered:
            print("لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
    except Exception as e:
        print(f"خطأ في تسجيل الخط العربي: {e}")

except ImportError as e:
    print(f"خطأ في استيراد مكتبات إنشاء التقارير: {e}")
    REPORTLAB_AVAILABLE = False

def reshape_ar(text):
    """إعادة تشكيل النص العربي للعرض الصحيح في PDF"""
    try:
        # تحويل النص إلى سلسلة نصية إذا لم يكن كذلك
        text = str(text)
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        # تطبيق خوارزمية BIDI لعرض النص من اليمين إلى اليسار
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص العربي: {e}")
        return text

class CreateAbsenceTableWindow(QMainWindow):
    """نافذة مسك أوراق الفروض - بنظام المعالج"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("معالج مسك أوراق الفروض")
        self.setObjectName("CreateAbsenceTableWindow")
        self.setFixedSize(1100, 650) # حجم النافذة ثابت 900*900

        # تعريف الألوان الأساسية - محدثة لتناسب تصميم sub252_window
        self.primary_color = "#2196f3"  # أزرق أساسي
        self.dark_blue_color = "#1565c0"  # أزرق غامق
        self.light_blue_color = "#e3f2fd"  # أزرق فاتح جداً
        self.accent_color = "#ff5722"  # برتقالي للتأكيد
        self.success_color = "#4caf50"  # أخضر للنجاح
        self.warning_color = "#ff9800"  # برتقالي للتحذير
        self.error_color = "#f44336"  # أحمر للأخطاء
        self.widget_bg_color = "white"
        self.window_bg_color = "qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1, stop: 0 #f8f9fc, stop: 1 #e9ecef)"
        self.border_color = "#bdc3c7"

        # تعريف الخطوط
        self.font_main_title_obj = QFont("Calibri", 18, QFont.Bold) # عنوان النافذة الرئيسي - تم تغيير الحجم إلى 18
        self.font_stage_title_obj = QFont("Calibri", 18, QFont.Bold) # عنوان المرحلة
        self.font_label_obj = QFont("Calibri", 14) # للتسميات والحقول

        # تهيئة القوائم مبكراً
        self.section_checkboxes = []
        self.exam_checkboxes = []

        self.setStyleSheet(f"""
            /* ===== تنسيق النافذة الرئيسية ===== */
            QMainWindow#CreateAbsenceTableWindow {{
                background: {self.window_bg_color};
            }}

            /* ===== تنسيق الإطارات ===== */
            QFrame.StageFrame {{
                background-color: {self.widget_bg_color};
                border: 1px solid {self.border_color};
                border-radius: 10px;
                padding: 20px;
                margin: 5px;
            }}

            QFrame#TitleFrame {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.light_blue_color},
                    stop: 1 #bbdefb
                );
                border: 1px solid {self.primary_color};
                border-radius: 8px;
                min-width: 450px;
                max-width: 450px;
                min-height: 45px;
                max-height: 45px;
                margin-top: 0px;
                margin-bottom: 0px;
            }}

            QFrame#MainButtonsFrame {{
                background-color: {self.widget_bg_color};
                border: 2px solid {self.border_color};
                border-radius: 10px;
                margin-top: 10px;
                padding: 15px;
            }}

            /* ===== تنسيق التسميات ===== */
            QLabel.MainTitleLabel {{
                font-family: Calibri;
                font-size: 20pt;
                font-weight: bold;
                color: {self.dark_blue_color};
            }}

            QLabel.StageTitleLabel {{
                font-family: Calibri;
                font-size: 18pt;
                font-weight: bold;
                color: {self.dark_blue_color};
            }}

            QLabel.FieldLabel {{
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: #495057;
                padding: 0px 5px;
                qproperty-alignment: 'AlignVCenter | AlignRight';
                background-color: transparent;
                border: none;
            }}

            QLabel#StatusBarLabel {{
                font-family: Calibri;
                font-size: 12pt;
                color: {self.dark_blue_color};
                padding: 8px;
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }}

            /* ===== تنسيق عناصر الإدخال ===== */
            QComboBox, QDateEdit {{
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: #495057;
                padding: 5px;
                background-color: white;
                border: 1px solid #ced4da;
                border-radius: 5px;
                selection-background-color: {self.light_blue_color};
                selection-color: {self.dark_blue_color};
            }}

            QComboBox:hover, QDateEdit:hover {{
                border: 1px solid {self.primary_color};
                background-color: white;
            }}

            QComboBox:focus, QDateEdit:focus {{
                border: 1px solid {self.primary_color};
                background-color: white;
            }}

            QComboBox::drop-down, QDateEdit::drop-down {{
                subcontrol-origin: padding;
                subcontrol-position: center right;
                width: 20px;
                border: none;
            }}

            QComboBox::down-arrow, QDateEdit::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #495057;
                margin-right: 5px;
            }}

            QComboBox QAbstractItemView {{
                font-family: Calibri;
                font-size: 13pt;
                color: #495057;
                border: 1px solid {self.primary_color};
                background-color: white;
                selection-background-color: {self.light_blue_color};
                selection-color: {self.dark_blue_color};
                padding: 5px;
                border-radius: 5px;
            }}

            /* ===== تنسيق الجداول ===== */
            QTableWidget {{
                font-family: Calibri;
                font-size: 14pt;
                color: #495057;
                border: 2px solid {self.border_color};
                border-radius: 8px;
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: {self.light_blue_color};
                selection-color: {self.dark_blue_color};
            }}

            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }}

            QTableWidget::item:selected {{
                background-color: {self.light_blue_color};
                color: {self.dark_blue_color};
            }}

            QTableWidget QHeaderView::section {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.light_blue_color},
                    stop: 1 #bbdefb
                );
                color: {self.dark_blue_color};
                font-family: Calibri;
                font-size: 14pt;
                font-weight: bold;
                padding: 10px;
                border: 1px solid #90caf9;
                text-align: center;
            }}

            QTableWidget QHeaderView::section:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #bbdefb,
                    stop: 1 #90caf9
                );
                color: {self.primary_color};
            }}

            /* ===== تنسيق شجرة العناصر ===== */
            QTreeWidget {{
                font-family: Calibri;
                font-size: 14pt;
                color: #495057;
                border: 2px solid {self.border_color};
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: {self.light_blue_color};
                selection-color: {self.dark_blue_color};
                show-decoration-selected: 1;
            }}

            QTreeWidget::item {{
                padding: 5px;
                border-bottom: 1px solid #dee2e6;
                min-height: 30px;
            }}

            QTreeWidget::item:selected {{
                background-color: {self.light_blue_color};
                color: {self.dark_blue_color};
            }}

            QTreeWidget::item:hover {{
                background-color: #f8f9fa;
            }}

            QTreeWidget QHeaderView::section {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.light_blue_color},
                    stop: 1 #bbdefb
                );
                color: {self.dark_blue_color};
                font-family: Calibri;
                font-size: 14pt;
                font-weight: bold;
                padding: 10px;
                border: 1px solid #90caf9;
            }}

            /* ===== تنسيق مربعات الاختيار ===== */
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 4px;
                border: 2px solid {self.border_color};
                background-color: white;
            }}

            QCheckBox::indicator:hover {{
                border: 2px solid {self.primary_color};
                background-color: {self.light_blue_color};
            }}

            QCheckBox::indicator:checked {{
                background-color: #ffc107;
                border: 2px solid #ff8f00;
                color: {self.dark_blue_color};
            }}

            QCheckBox::indicator:checked:hover {{
                background-color: #ff8f00;
                border: 2px solid #e65100;
            }}""")

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        main_v_layout = QVBoxLayout(self.central_widget)
        main_v_layout.setContentsMargins(20, 10, 20, 20)  # تقليل الهامش العلوي
        main_v_layout.setSpacing(0)  # إزالة التباعد بين العناصر

        # --- 1. العنوان الرئيسي للنافذة ---
        main_title_frame = QFrame()
        main_title_frame.setObjectName("TitleFrame") # Apply the new style

        window_title_label = QLabel("🎯 نظام إدارة الفروض المدرسية المتطور")
        window_title_label.setObjectName("MainTitleLabel") # For font and color
        window_title_label.setFont(self.font_main_title_obj)
        window_title_label.setAlignment(Qt.AlignCenter) # Center text within the label

        # Layout for the label within its frame (to ensure it fills and centers)
        frame_internal_layout = QVBoxLayout(main_title_frame)
        frame_internal_layout.addWidget(window_title_label)
        frame_internal_layout.setContentsMargins(0, 0, 0, 0)

        # Layout to center the title frame in the window
        title_frame_container_layout = QHBoxLayout()
        title_frame_container_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش
        title_frame_container_layout.setSpacing(0)  # إزالة التباعد
        title_frame_container_layout.addStretch(1)
        title_frame_container_layout.addWidget(main_title_frame)
        title_frame_container_layout.addStretch(1)

        main_v_layout.addLayout(title_frame_container_layout)

        # --- 2. QStackedWidget للمراحل ---
        self.stacked_widget = QStackedWidget()
        main_v_layout.addWidget(self.stacked_widget, 1) # إعطاء مرونة للتمدد

        # --- إنشاء المراحل ---
        self._create_stage1()
        self._create_stage2()
        self._create_stage3()

        self.stacked_widget.addWidget(self.stage1_widget)
        self.stacked_widget.addWidget(self.stage2_widget)
        self.stacked_widget.addWidget(self.stage3_widget)

        # --- 3. أزرار التنقل الرئيسية (التالي/السابق) ---
        self.navigation_buttons_layout = QHBoxLayout()
        self.navigation_buttons_layout.setSpacing(15)

        # إنشاء أزرار التنقل المحسنة
        self.prev_button = self.create_enhanced_button(
            "« السابق", "#6c757d", "#495057", "#343a40", "white", 80, 35
        )
        self.prev_button.clicked.connect(self.go_to_previous_stage)

        self.next_button = self.create_enhanced_button(
            "التالي »", "#6c757d", "#495057", "#343a40", "white", 80, 35
        )
        self.next_button.clicked.connect(self.go_to_next_stage)

        self.navigation_buttons_layout.addStretch(1)
        self.navigation_buttons_layout.addWidget(self.prev_button)
        self.navigation_buttons_layout.addWidget(self.next_button)
        self.navigation_buttons_layout.addStretch(1)
        main_v_layout.addLayout(self.navigation_buttons_layout)

        # --- 4. منطقة الأزرار الرئيسية (تظهر فقط في المرحلة الأخيرة) ---
        self.main_buttons_frame = QFrame()
        self.main_buttons_frame.setObjectName("MainButtonsFrame")
        main_buttons_h_layout = QHBoxLayout(self.main_buttons_frame)
        main_buttons_h_layout.setSpacing(10)
        main_buttons_h_layout.addStretch(1)

        # إنشاء أزرار محسنة
        self.save_button = self.create_enhanced_button(
            "مسك الفروض", "#4caf50", "#388e3c", "#2e7d32", "white", 150, 35
        )
        self.save_button.clicked.connect(self.save_all_exams)
        main_buttons_h_layout.addWidget(self.save_button)

        self.cancel_button = self.create_enhanced_button(
            "إلغاء", "#f44336", "#d32f2f", "#c62828", "white", 80, 35
        )
        self.cancel_button.clicked.connect(self.clear_form_and_reset_wizard)
        main_buttons_h_layout.addWidget(self.cancel_button)

        # تم إزالة زر ورقة التنقيط

        # --- إضافة خط فاصل بتنسيق جديد ---
        separator_line = QFrame()
        separator_line.setFrameShape(QFrame.VLine)
        separator_line.setFrameShadow(QFrame.Sunken)
        separator_line.setStyleSheet(f"color: #E0E0E0; margin: 0px 10px;")
        separator_line.setFixedWidth(2)
        main_buttons_h_layout.addWidget(separator_line)

        self.delete_exam_button = self.create_enhanced_button(
            "حذف الفروض الممسوكة", "#e53935", "#d32f2f", "#c62828", "white", 150, 35
        )
        self.delete_exam_button.clicked.connect(self.show_delete_exam_dialog)
        main_buttons_h_layout.addWidget(self.delete_exam_button)

        self.section_report_button = self.create_enhanced_button(
            "تقرير القسم", "#00897b", "#00796b", "#00695c", "white", 100, 35
        )
        self.section_report_button.clicked.connect(self.show_section_exams_report)
        main_buttons_h_layout.addWidget(self.section_report_button)

        self.teacher_report_button = self.create_enhanced_button(
            "تقرير الأستاذ", "#039be5", "#0288d1", "#0277bd", "white", 120, 35
        )
        self.teacher_report_button.clicked.connect(self.show_teacher_exams_report)
        main_buttons_h_layout.addWidget(self.teacher_report_button)
        # --- نهاية إضافة الأزرار ---


        main_buttons_h_layout.addStretch(1)
        main_v_layout.addWidget(self.main_buttons_frame)


        # --- 5. شريط الحالة ---
        status_bar_frame = QFrame()
        status_bar_frame.setObjectName("StatusBarFrame")
        status_bar_frame.setStyleSheet(f"""
            QFrame#StatusBarFrame {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f8f9fa,
                    stop: 1 #e9ecef
                );
                border-top: 2px solid #dee2e6;
                padding: 8px;
            }}
        """)
        status_bar_layout = QHBoxLayout(status_bar_frame)
        status_bar_layout.setContentsMargins(15, 8, 15, 8)

        version_label = QLabel("📝 الإصدار 2.0")
        version_label.setStyleSheet("""
            font-family: Calibri; 
            font-size: 12pt; 
            color: #6c757d;
            font-weight: bold;
        """)

        status_bar_label = QLabel("جميع الحقوق محفوظة © 2025 - معالج مسك أوراق الفروض")
        status_bar_label.setObjectName("StatusBarLabel")
        status_bar_label.setAlignment(Qt.AlignCenter)
        status_bar_label.setStyleSheet("""
            font-family: Calibri; 
            font-size: 13pt; 
            color: #1565c0; 
            font-weight: bold;
            background: qlineargradient(
                x1: 0, y1: 0, x2: 0, y2: 1,
                stop: 0 #e3f2fd,
                stop: 1 #bbdefb
            );
            border: 1px solid #90caf9;
            border-radius: 5px;
            padding: 5px 15px;
        """)

        date_label = QLabel(f"📅 {QDate.currentDate().toString('yyyy/MM/dd')}")
        date_label.setStyleSheet("""
            font-family: Calibri; 
            font-size: 12pt; 
            color: #6c757d;
            font-weight: bold;
        """)
        date_label.setAlignment(Qt.AlignRight)

        status_bar_layout.addWidget(version_label)
        status_bar_layout.addStretch(1)
        status_bar_layout.addWidget(status_bar_label)
        status_bar_layout.addStretch(1)
        status_bar_layout.addWidget(date_label)

        main_v_layout.addWidget(status_bar_frame)

        self.ensure_table_exists()
        self.load_data()
        self.connect_signals()

        self.update_navigation_buttons()
        self.stacked_widget.setCurrentIndex(0) # البدء من المرحلة الأولى

    def create_enhanced_button(self, text, color_start, color_end, border_color, text_color="white", width=100, height=35):
        """إنشاء زر محسن بتدرج لوني جميل"""
        button = QPushButton(text)
        button.setFixedSize(width, height)
        button.setFont(QFont("Calibri", 12, QFont.Bold))
        button.setCursor(Qt.PointingHandCursor)
        
        # تطبيق التنسيق مع التدرج اللوني
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color_start},
                    stop: 1 {color_end}
                );
                color: {text_color};
                border: 2px solid {border_color};
                border-radius: 8px;
                font-weight: bold;
                padding: 5px;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color_end},
                    stop: 1 {color_start}
                );
                border: 2px solid {color_start};
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {border_color},
                    stop: 1 {color_end}
                );
                border: 2px solid {border_color};
                transform: translateY(1px);
            }}
            QPushButton:disabled {{
                background-color: #e9ecef;
                color: #6c757d;
                border: 2px solid #dee2e6;
            }}
        """)
        
        return button

    def _create_stage_frame(self, title_text):
        """إنشاء إطار ومخطط وتسمية عنوان لمرحلة"""
        stage_widget = QWidget()
        stage_layout = QVBoxLayout(stage_widget)
        stage_layout.setContentsMargins(20, 0, 20, 20)  # إزالة الهامش العلوي
        stage_layout.setSpacing(0)  # إزالة التباعد
        stage_layout.setAlignment(Qt.AlignTop)

        frame = QFrame(stage_widget)
        frame.setObjectName("StageFrame")

        frame_layout = QVBoxLayout(frame)
        frame_layout.setContentsMargins(10, 10, 10, 15)
        frame_layout.setSpacing(15)  # زيادة التباعد بين العناصر داخل الإطار
        frame_layout.setAlignment(Qt.AlignTop)

        # --- Stage Title Frame ---
        stage_title_frame = QFrame()
        stage_title_frame.setObjectName("TitleFrame") # Apply the new style

        title_label = QLabel(title_text)
        title_label.setObjectName("StageTitleLabel") # For font and color
        title_label.setFont(self.font_stage_title_obj)
        title_label.setAlignment(Qt.AlignCenter) # Center text within the label

        # Layout for the label within its frame
        stage_frame_internal_layout = QVBoxLayout(stage_title_frame)
        stage_frame_internal_layout.addWidget(title_label)
        stage_frame_internal_layout.setContentsMargins(0, 0, 0, 0)

        # Layout to center the stage title frame
        stage_title_container_layout = QHBoxLayout()
        stage_title_container_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش
        stage_title_container_layout.setSpacing(0)  # إزالة التباعد
        stage_title_container_layout.addStretch(1)
        stage_title_container_layout.addWidget(stage_title_frame)
        stage_title_container_layout.addStretch(1)

        # إضافة العنوان في أعلى الإطار
        frame_layout.addLayout(stage_title_container_layout)

        stage_layout.addWidget(frame)
        return stage_widget, frame_layout

    def _create_field_row(self, label_text, widget_to_add):
        """إنشاء صف يحتوي على تسمية وحقل إدخال - محدث بالتصميم الجديد"""
        row_layout = QHBoxLayout()
        row_layout.setSpacing(5)
        row_layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء إطار للتسمية
        label_frame = QFrame()
        label_frame.setFixedSize(150, 35)
        label_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd,
                    stop: 1 #bbdefb
                );
                border: 1px solid #2196f3;
                border-radius: 5px;
            }
            QFrame:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #bbdefb,
                    stop: 1 #90caf9
                );
                border: 1px solid #1565c0;
            }
        """)

        # إنشاء تسمية
        label = QLabel(label_text)
        label.setObjectName("FieldLabel")
        label.setFont(QFont("Calibri", 13, QFont.Bold))
        label.setAlignment(Qt.AlignVCenter | Qt.AlignRight)
        label.setStyleSheet("color: #1565c0; border: none; background: transparent; font-weight: bold;")

        # إضافة التسمية إلى الإطار
        label_layout = QVBoxLayout(label_frame)
        label_layout.setContentsMargins(5, 0, 5, 0)
        label_layout.addWidget(label)

        # إنشاء إطار للحقل
        field_frame = QFrame()
        field_frame.setFixedSize(250, 35)
        field_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #2196f3;
                border-radius: 5px;
            }
            QFrame:hover {
                border: 1px solid #1565c0;
                background-color: #fafafa;
            }
        """)

        # إضافة حقل الإدخال إلى الإطار
        field_layout = QVBoxLayout(field_frame)
        field_layout.setContentsMargins(2, 2, 2, 2)
        field_layout.addWidget(widget_to_add)

        widget_to_add.setLayoutDirection(Qt.RightToLeft)

        # تطبيق التنسيق على حقل الإدخال
        if isinstance(widget_to_add, (QComboBox, QDateEdit)):
            widget_to_add.setFont(QFont("Calibri", 13, QFont.Bold))
            widget_to_add.setStyleSheet("""
                border: none; 
                background-color: white;
                color: #212529;
            """)
            
            # تنسيق إضافي للقائمة المنسدلة
            if isinstance(widget_to_add, QComboBox):
                widget_to_add.setStyleSheet("""
                    QComboBox {
                        border: none; 
                        background-color: white;
                        color: #212529;
                        font-weight: bold;
                    }
                    QComboBox QAbstractItemView {
                        background-color: white;
                        border: 1px solid #2196f3;
                        selection-background-color: #e3f2fd;
                        selection-color: #1565c0;
                        color: #212529;
                        font-weight: bold;
                    }
                    QComboBox::drop-down {
                        border: none;
                        background-color: white;
                    }
                    QComboBox::down-arrow {
                        image: none;
                        border-left: 5px solid transparent;
                        border-right: 5px solid transparent;
                        border-top: 5px solid #212529;
                        margin-right: 5px;
                    }
                """)

        row_layout.addStretch(1)
        row_layout.addWidget(label_frame)
        row_layout.addWidget(field_frame)
        row_layout.addStretch(1)
        return row_layout

    def _create_stage1(self):
        """إنشاء واجهة المرحلة الأولى: بيانات المادة والأستاذ"""
        self.stage1_widget, stage1_frame_layout = self._create_stage_frame("🗓️ الخطوة الأولى: معلومات التاريخ والمادة والأستاذ")

        stage1_frame_layout.addStretch(1) # إضافة امتداد قبل حقول الإدخال للتوسيط العمودي

        # تاريخ المسك
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        stage1_frame_layout.addLayout(self._create_field_row("📅 تاريخ إجراء الفرض:", self.date_edit))

        # المادة الدراسية
        self.subject_combo = QComboBox()
        stage1_frame_layout.addLayout(self._create_field_row("📚 المادة الدراسية:", self.subject_combo))

        # اسم الأستاذ
        self.teacher_combo = QComboBox()
        stage1_frame_layout.addLayout(self._create_field_row("👨‍🏫 الأستاذ(ة) المشرف:", self.teacher_combo))

        stage1_frame_layout.addStretch(1) # الإبقاء على الامتداد الحالي بعد حقول الإدخال


    def _create_stage2(self):
        """إنشاء واجهة المرحلة الثانية: المستويات والأقسام بطريقة التالي والسابق"""
        self.stage2_widget, stage2_frame_layout = self._create_stage_frame("المرحلة 2: اختيار المستويات والأقسام")

        # متغيرات لتتبع المستوى الحالي والأقسام المختارة
        self.current_level_index = 0
        self.levels_data = []  # سيتم تعبئتها بأسماء المستويات
        self.sections_by_level = {}  # قاموس يحتوي على الأقسام لكل مستوى
        self.selected_sections = {}  # قاموس لتخزين الأقسام المختارة لكل مستوى
        self.section_checkboxes = []  # سيتم استخدامها لتخزين مراجع لمربعات الاختيار

        # إنشاء إطار للتعليمات
        instruction_frame = QFrame()
        instruction_frame.setFixedHeight(40)
        instruction_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa,
                    stop: 1 #e9ecef
                );
                border: 1px solid #ced4da;
                border-radius: 8px;
                margin: 5px;
            }
        """)

        # إنشاء تسمية التعليمات
        instruction_label = QLabel("🔍 اختر المستوى ثم حدد الأقسام المطلوبة")
        instruction_label.setFont(QFont("Calibri", 14, QFont.Bold))
        instruction_label.setStyleSheet("color: #495057; border: none; background: transparent;")
        instruction_label.setAlignment(Qt.AlignCenter)

        # إضافة التسمية إلى الإطار
        instruction_layout = QVBoxLayout(instruction_frame)
        instruction_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش
        instruction_layout.addWidget(instruction_label)

        stage2_frame_layout.addWidget(instruction_frame)

        # إنشاء تخطيط للمستوى الحالي مع أزرار التنقل
        level_layout = QHBoxLayout()
        level_layout.setContentsMargins(0, 10, 0, 10)
        level_layout.setSpacing(10)

        # إنشاء زر المستوى السابق
        self.prev_level_button = QPushButton("« السابق")
        self.prev_level_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.prev_level_button.setFixedSize(90, 35)
        self.prev_level_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6c757d,
                    stop: 1 #495057
                );
                color: white;
                border: 2px solid #495057;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #868e96,
                    stop: 1 #6c757d
                );
                border: 2px solid #6c757d;
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #495057,
                    stop: 1 #343a40
                );
                border: 2px solid #343a40;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
                border: 2px solid #dee2e6;
            }
        """)
        self.prev_level_button.clicked.connect(self.go_to_previous_level)

        # إنشاء بطاقة المستوى
        level_frame = QFrame()
        level_frame.setFixedHeight(40)
        level_frame.setFixedWidth(500)
        level_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd,
                    stop: 1 #bbdefb
                );
                border: 2px solid #2196f3;
                border-radius: 8px;
            }
        """)

        # تسمية المستوى
        self.level_label = QLabel("المستوى")
        self.level_label.setFont(QFont("Calibri", 16, QFont.Bold))
        self.level_label.setStyleSheet("color: #1565c0; font-weight: bold; border: none; background: transparent;")
        self.level_label.setAlignment(Qt.AlignCenter)

        # إضافة التسمية إلى بطاقة المستوى
        level_frame_layout = QVBoxLayout(level_frame)
        level_frame_layout.setContentsMargins(10, 5, 10, 5)
        level_frame_layout.addWidget(self.level_label)

        # إنشاء زر المستوى التالي
        self.next_level_button = QPushButton("التالي »")
        self.next_level_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.next_level_button.setFixedSize(90, 35)
        self.next_level_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6c757d,
                    stop: 1 #495057
                );
                color: white;
                border: 2px solid #495057;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #868e96,
                    stop: 1 #6c757d
                );
                border: 2px solid #6c757d;
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #495057,
                    stop: 1 #343a40
                );
                border: 2px solid #343a40;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #6c757d;
                border: 2px solid #dee2e6;
            }
        """)
        self.next_level_button.clicked.connect(self.go_to_next_level)

        # إضافة العناصر إلى تخطيط المستوى
        level_layout.addWidget(self.prev_level_button)
        level_layout.addStretch(1)
        level_layout.addWidget(level_frame)
        level_layout.addStretch(1)
        level_layout.addWidget(self.next_level_button)

        # إنشاء تخطيط للأقسام
        sections_layout = QVBoxLayout()
        sections_layout.setContentsMargins(0, 5, 0, 10)
        sections_layout.setSpacing(10)

        # إنشاء منطقة تمرير للأقسام
        scroll_area = QScrollArea()
        scroll_area.setFixedHeight(140)
        scroll_area.setFixedWidth(900)
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.StyledPanel)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
            }
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #6c757d;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #495057;
            }
        """)

        # إنشاء ويدجت داخلي لمنطقة التمرير
        self.sections_container = QWidget()
        self.sections_container.setStyleSheet("background-color: white;")

        # تخطيط للأقسام داخل الويدجت - استخدام تخطيط شبكي (Grid) لتوزيع الأقسام بشكل أفضل
        self.sections_grid = QGridLayout(self.sections_container)
        self.sections_grid.setContentsMargins(10, 10, 10, 10)
        self.sections_grid.setSpacing(10)  # زيادة المسافة بين الأقسام
        self.sections_grid.setAlignment(Qt.AlignTop | Qt.AlignLeft)  # محاذاة للأعلى واليسار

        # تعيين الويدجت الداخلي لمنطقة التمرير
        scroll_area.setWidget(self.sections_container)

        # إضافة منطقة التمرير إلى التخطيط مع توسيط
        sections_container_layout = QHBoxLayout()
        sections_container_layout.addStretch(1)
        sections_container_layout.addWidget(scroll_area)
        sections_container_layout.addStretch(1)
        sections_layout.addLayout(sections_container_layout)

        # حذف أزرار التنقل بين المستويات من هنا لأننا أضفناها بالفعل في تخطيط المستوى

        # إضافة كل التخطيطات إلى تخطيط الإطار الرئيسي
        stage2_frame_layout.addLayout(level_layout)
        stage2_frame_layout.addLayout(sections_layout, 1)

        # تحميل المستويات والأقسام من قاعدة البيانات
        self.load_levels_and_sections_new()

        # عرض المستوى الأول وأقسامه
        self.update_level_display()


    def _create_stage3(self):
        """إنشاء واجهة المرحلة الثالثة: نوع الفرض"""
        self.stage3_widget, stage3_frame_layout = self._create_stage_frame("المرحلة 3: اختيار نوع الفرض")

        # تم إزالة عنوان "أنواع الفروض المتاحة"

        self.exam_types_table = QTableWidget()
        self.exam_types_table.setColumnCount(2)
        self.exam_types_table.setRowCount(4)
        self.exam_types_table.setHorizontalHeaderLabels(["تحديد", "نوعه"])
        self.exam_types_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.exam_types_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.exam_types_table.verticalHeader().setVisible(False)
        self.exam_types_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.exam_types_table.setSelectionMode(QAbstractItemView.NoSelection)
        stage3_frame_layout.addWidget(self.exam_types_table, 1) # إعطاء الجدول مرونة

        exam_types = ["الفرض 1", "الفرض 2", "الفرض 3", "الفرض 4"]
        for i, exam_type_text in enumerate(exam_types):
            checkbox_widget = QWidget()
            chk_layout = QHBoxLayout(checkbox_widget)
            chk_layout.setContentsMargins(0,0,0,0)
            chk_layout.setAlignment(Qt.AlignCenter)
            checkbox = QCheckBox()
            checkbox.setCursor(Qt.PointingHandCursor)
            chk_layout.addWidget(checkbox)
            self.exam_checkboxes.append(checkbox) # تأكد من أن self.exam_checkboxes مهيأة
            self.exam_types_table.setCellWidget(i, 0, checkbox_widget)
            item = QTableWidgetItem(exam_type_text)
            item.setTextAlignment(Qt.AlignCenter)
            self.exam_types_table.setItem(i, 1, item)
            self.exam_types_table.setRowHeight(i, 40)
        stage3_frame_layout.addStretch(0)

    def go_to_next_stage(self):
        current_index = self.stacked_widget.currentIndex()
        if current_index == 0: # الانتقال من المرحلة 1 إلى 2
            if not self.subject_combo.currentText() or not self.teacher_combo.currentText():
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار المادة والأستاذ أولاً.", "بيانات غير مكتملة")
                return
        elif current_index == 1: # الانتقال من المرحلة 2 إلى 3
            # التحقق من وجود أقسام محددة بالطريقة الجديدة
            total_selected_sections = 0
            for level in self.selected_sections:
                total_selected_sections += len(self.selected_sections[level])

            if total_selected_sections == 0:
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار قسم واحد على الأقل.", "بيانات غير مكتملة")
                return

        if current_index < self.stacked_widget.count() - 1:
            self.stacked_widget.setCurrentIndex(current_index + 1)
        self.update_navigation_buttons()

    def go_to_previous_stage(self):
        current_index = self.stacked_widget.currentIndex()
        if current_index > 0:
            self.stacked_widget.setCurrentIndex(current_index - 1)
        self.update_navigation_buttons()

    def update_navigation_buttons(self):
        current_index = self.stacked_widget.currentIndex()
        self.prev_button.setEnabled(current_index > 0)
        self.next_button.setEnabled(current_index < self.stacked_widget.count() - 1)

        # إظهار أزرار الحفظ والإلغاء الرئيسية دائماً
        self.main_buttons_frame.show()

        # تحديث حالة أزرار التنقل
        if current_index == self.stacked_widget.count() - 1: # إذا كنا في المرحلة الأخيرة
            self.navigation_buttons_layout.setEnabled(False) # تعطيل أزرار التنقل
            self.prev_button.hide()
            self.next_button.hide()
        else:
            self.navigation_buttons_layout.setEnabled(True)
            self.prev_button.show()
            self.next_button.show()
            if current_index == 0: self.prev_button.hide()


    def clear_form_and_reset_wizard(self):
        """مسح النموذج وإعادة المعالج إلى المرحلة الأولى"""
        self.clear_form() # الدالة الموجودة لمسح الحقول
        self.stacked_widget.setCurrentIndex(0)
        self.update_navigation_buttons()


    def load_levels_and_sections_new(self):
        """تحميل المستويات والأقسام من قاعدة البيانات بالطريقة الجديدة"""
        try:
            # إعادة تعيين المتغيرات
            self.levels_data = []
            self.sections_by_level = {}
            self.selected_sections = {}
            self.current_level_index = 0
            # لم نعد نستخدم مربعات الاختيار
            pass

            # تحميل المستويات من قاعدة البيانات
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT المستوى FROM البنية_التربوية
                WHERE المستوى IS NOT NULL AND المستوى != ''
                ORDER BY ترتيب_المستويات, المستوى
            """)
            self.levels_data = [row[0] for row in cursor.fetchall()]

            # دالة لترتيب الأقسام
            def extract_section_number(section_name_arg):
                import re
                match = re.search(r'(\d+)[/\-]?(\d*)', section_name_arg)
                if match:
                    if match.group(2):
                        return int(match.group(1)) * 100 + int(match.group(2))
                    return int(match.group(1)) * 100
                return 9999

            # تحميل الأقسام لكل مستوى
            for level in self.levels_data:
                cursor.execute("""
                    SELECT DISTINCT القسم FROM البنية_التربوية
                    WHERE المستوى = ? AND القسم IS NOT NULL AND القسم != ''
                """, (level,))
                sections_data = [row[0] for row in cursor.fetchall()]

                # ترتيب الأقسام
                sections_data.sort(key=extract_section_number)

                # تخزين الأقسام في القاموس
                self.sections_by_level[level] = sections_data

                # تهيئة قاموس الأقسام المختارة
                self.selected_sections[level] = []

            conn.close()

            # تحديث حالة أزرار التنقل
            self.update_navigation_level_buttons()

        except Exception as e:
            print(f"خطأ في تحميل المستويات والأقسام: {e}")
            traceback.print_exc()

    def update_level_display(self):
        """تحديث عرض المستوى الحالي وأقسامه"""
        # التأكد من وجود مستويات
        if not self.levels_data:
            self.level_label.setText("لا توجد مستويات")
            return

        # الحصول على المستوى الحالي
        current_level = self.levels_data[self.current_level_index]

        # تحديث تسمية المستوى
        self.level_label.setText(current_level)

        # مسح جميع الأقسام الحالية
        self.clear_sections_container()

        # إضافة الأقسام للمستوى الحالي
        sections = self.sections_by_level.get(current_level, [])

        # إذا لم تكن هناك أقسام، أظهر رسالة
        if not sections:
            # إضافة تسمية "لا توجد أقسام"
            no_sections_label = QLabel("لا توجد أقسام")
            no_sections_label.setFont(QFont("Calibri", 12, QFont.Bold))
            no_sections_label.setAlignment(Qt.AlignCenter)
            self.sections_grid.addWidget(no_sections_label, 0, 0)
            return

        # إنشاء بطاقات الأقسام وتوزيعها في شبكة
        # تحديد عدد الأعمدة في الشبكة (4 أقسام في كل صف)
        columns_per_row = 4

        # إنشاء بطاقات الأقسام
        for i, section_name in enumerate(sections):
            # حساب الصف والعمود لوضع القسم في الشبكة
            row = i // columns_per_row
            col = i % columns_per_row

            # إنشاء بطاقة القسم
            section_frame = QFrame()
            section_frame.setFixedHeight(30)
            section_frame.setFixedWidth(120)

            # تحديد ما إذا كان القسم محدداً
            is_selected = section_name in self.selected_sections.get(current_level, [])

            # تطبيق التنسيق المناسب
            if is_selected:
                section_frame.setStyleSheet("""
                    QFrame {
                        background: qlineargradient(
                            x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 #ffc107,
                            stop: 1 #ff8f00
                        );
                        border: 2px solid #ff8f00;
                        border-radius: 8px;
                    }
                """)
            else:
                section_frame.setStyleSheet("""
                    QFrame {
                        background: qlineargradient(
                            x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 white,
                            stop: 1 #f8f9fa
                        );
                        border: 2px solid #dee2e6;
                        border-radius: 8px;
                    }
                    QFrame:hover {
                        background: qlineargradient(
                            x1: 0, y1: 0, x2: 0, y2: 1,
                            stop: 0 #e3f2fd,
                            stop: 1 #bbdefb
                        );
                        border: 2px solid #2196f3;
                    }
                """)

            # تخطيط أفقي للقسم
            section_layout = QHBoxLayout(section_frame)
            section_layout.setContentsMargins(5, 0, 5, 0)
            section_layout.setSpacing(3)

            # جعل بطاقة القسم قابلة للنقر
            section_frame.setCursor(Qt.PointingHandCursor)

            # تخزين اسم القسم كخاصية للإطار
            section_frame.setProperty("section_name", section_name)

            # ربط حدث النقر على الإطار بدالة تحديد القسم
            section_frame.mousePressEvent = lambda event, s=section_name: self.section_clicked(event, s)

            # تسمية القسم
            section_label = QLabel(section_name)
            section_label.setFont(QFont("Calibri", 12, QFont.Bold))
            if is_selected:
                section_label.setStyleSheet("color: #0d47a1; border: none; background: transparent;")
            else:
                section_label.setStyleSheet("color: #495057; border: none; background: transparent;")
            section_label.setAlignment(Qt.AlignCenter)

            # إضافة التسمية إلى التخطيط
            section_layout.addWidget(section_label, 1)

            # إضافة علامة مميزة للقسم المختار
            if is_selected:
                check_mark = QLabel("✓")
                check_mark.setFont(QFont("Calibri", 14, QFont.Bold))
                check_mark.setStyleSheet("color: #0d47a1; border: none; background: transparent;")
                check_mark.setAlignment(Qt.AlignTop | Qt.AlignRight)
                section_layout.addWidget(check_mark)

            # إضافة بطاقة القسم إلى الشبكة في الموقع المحدد (الصف، العمود)
            self.sections_grid.addWidget(section_frame, row, col)

        # تحديث حالة أزرار التنقل
        self.update_navigation_level_buttons()

    def clear_sections_container(self):
        """مسح جميع الأقسام من الحاوية"""
        # إزالة جميع العناصر من التخطيط الشبكي
        while self.sections_grid.count():
            item = self.sections_grid.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
            elif item.layout():
                # إزالة العناصر من التخطيط الفرعي
                while item.layout().count():
                    subitem = item.layout().takeAt(0)
                    if subitem.widget():
                        subitem.widget().deleteLater()
                # حذف التخطيط الفرعي
                item.layout().deleteLater()

        # لم نعد نستخدم مربعات الاختيار
        pass

    def section_clicked(self, event, section_name):
        """معالجة النقر على بطاقة القسم"""
        current_level = self.levels_data[self.current_level_index]

        # تبديل حالة تحديد القسم
        if section_name in self.selected_sections[current_level]:
            self.selected_sections[current_level].remove(section_name)
        else:
            self.selected_sections[current_level].append(section_name)

        # تحديث العرض
        self.update_level_display()

    def toggle_section_selection(self, section_name, state):
        """تبديل حالة تحديد القسم (للتوافق مع الكود القديم)"""
        current_level = self.levels_data[self.current_level_index]

        # إضافة أو إزالة القسم من قائمة الأقسام المحددة
        if state:
            if section_name not in self.selected_sections[current_level]:
                self.selected_sections[current_level].append(section_name)
        else:
            if section_name in self.selected_sections[current_level]:
                self.selected_sections[current_level].remove(section_name)

        # تحديث العرض
        self.update_level_display()

    def go_to_next_level(self):
        """الانتقال إلى المستوى التالي"""
        if self.current_level_index < len(self.levels_data) - 1:
            self.current_level_index += 1
            self.update_level_display()

    def go_to_previous_level(self):
        """الانتقال إلى المستوى السابق"""
        if self.current_level_index > 0:
            self.current_level_index -= 1
            self.update_level_display()

    def update_navigation_level_buttons(self):
        """تحديث حالة أزرار التنقل بين المستويات"""
        # تعطيل زر السابق إذا كنا في المستوى الأول
        self.prev_level_button.setEnabled(self.current_level_index > 0)

        # تعطيل زر التالي إذا كنا في المستوى الأخير
        self.next_level_button.setEnabled(self.current_level_index < len(self.levels_data) - 1)

    # الدالة القديمة تبقى للتوافق مع الكود القديم
    def load_levels_and_sections(self):
        """تحميل المستويات والأقسام من قاعدة البيانات (الطريقة القديمة)"""
        # استدعاء الطريقة الجديدة بدلاً من ذلك
        self.load_levels_and_sections_new()

    # تم حذف دالة update_sections_table_from_combo لأنها لم تعد مستخدمة

    def update_level_indicator(self, level_name):
        """تحديث مؤشر المستوى ليظهر إذا كان هناك أقسام محددة داخله"""
        if level_name not in self.level_items:
            return

        level_item = self.level_items[level_name]
        has_selected_sections = False
        selected_sections_list = []

        # التحقق من كل قسم داخل المستوى وجمع الأقسام المحددة
        for section_idx in range(level_item.childCount()):
            section_item = level_item.child(section_idx)
            checkbox = self.levels_tree.itemWidget(section_item, 1)
            if checkbox and checkbox.isChecked():
                has_selected_sections = True
                selected_sections_list.append(section_item.text(0))

        # تحديث نص المستوى ليظهر علامة إذا كان هناك أقسام محددة

        # تحديد ما إذا كان المستوى موسعًا أم مطويًا
        is_expanded = level_item.isExpanded()
        prefix = "[-]" if is_expanded else "[+]"

        # إزالة علامات + و - والعلامة ✓ من النص الحالي للحصول على اسم المستوى فقط
        clean_level_name = level_name

        # إنشاء أو تحديث عنصر التحديد في العمود الثاني
        selection_widget = self.levels_tree.itemWidget(level_item, 1)
        if not selection_widget:
            selection_widget = QLabel()
            selection_widget.setAlignment(Qt.AlignCenter)
            selection_widget.setFont(QFont("Calibri", 13, QFont.Bold))
            selection_widget.setWordWrap(True)  # السماح بالتفاف النص
            self.levels_tree.setItemWidget(level_item, 1, selection_widget)

        if has_selected_sections:
            # إضافة علامة ✓ مع الحفاظ على علامة + أو -
            level_item.setText(0, f"{prefix} {clean_level_name}")

            # إظهار أسماء الأقسام المحددة في عمود التحديد
            sections_text = ", ".join(selected_sections_list)
            if len(sections_text) > 30 and not is_expanded:
                # اختصار النص إذا كان طويلاً جداً والمستوى مطوي
                sections_text = sections_text[:27] + "..."

            selection_widget.setText(sections_text)
            selection_widget.setStyleSheet("""
                color: blue;
                font-weight: bold;
                background-color: #E6F7FF;
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 2px;
            """)

            # تغيير لون النص للإشارة إلى وجود أقسام محددة
            level_item.setForeground(0, Qt.blue)
        else:
            # إعادة النص بدون علامة ✓ مع الحفاظ على علامة + أو -
            level_item.setText(0, f"{prefix} {clean_level_name}")

            # إزالة النص من عمود التحديد
            selection_widget.setText("")
            selection_widget.setStyleSheet("")

            # إعادة لون النص إلى الأسود
            level_item.setForeground(0, Qt.black)

    def update_all_level_indicators(self):
        """تحديث مؤشرات جميع المستويات"""
        for level_name in self.level_items:
            self.update_level_indicator(level_name)

    def get_sections_for_level(self, level_name):
        """الحصول على قائمة بجميع الأقسام التابعة لمستوى معين"""
        if level_name not in self.level_items:
            return []

        level_item = self.level_items[level_name]
        sections = []

        for section_idx in range(level_item.childCount()):
            section_item = level_item.child(section_idx)
            sections.append(section_item.text(0))

        return sections

    def on_item_expanded(self, item):
        """تحديث علامة العنصر عند توسيعه من + إلى -"""
        # التحقق مما إذا كان العنصر هو عنصر مستوى (عنصر رئيسي)
        if item.parent() is None:  # العناصر الرئيسية ليس لها أب
            # البحث عن اسم المستوى في القاموس
            for level_name, level_item in self.level_items.items():
                if level_item == item:
                    # تحديث مؤشر المستوى (سيقوم بتغيير العلامة من + إلى - وإزالة الأقسام المعروضة)
                    self.update_level_indicator(level_name)
                    break

    def on_item_collapsed(self, item):
        """تحديث علامة العنصر عند طيه من - إلى +"""
        # التحقق مما إذا كان العنصر هو عنصر مستوى (عنصر رئيسي)
        if item.parent() is None:  # العناصر الرئيسية ليس لها أب
            # البحث عن اسم المستوى في القاموس
            for level_name, level_item in self.level_items.items():
                if level_item == item:
                    # تحديث مؤشر المستوى (سيقوم بتغيير العلامة من - إلى + وإضافة الأقسام المحددة)
                    self.update_level_indicator(level_name)
                    break

    def clear_form(self):
        """مسح النموذج - تم تعديله ليتناسب مع المعالج"""
        self.date_edit.setDate(QDate.currentDate())
        self.subject_combo.setCurrentIndex(0 if self.subject_combo.count() > 0 else -1)
        # self.teacher_combo.setCurrentIndex(0 if self.teacher_combo.count() > 0 else -1) # Teacher combo is updated by subject

        # إعادة تعيين الأقسام المحددة بالطريقة الجديدة
        for level in self.selected_sections:
            self.selected_sections[level] = []

        # إعادة تعيين المستوى الحالي
        self.current_level_index = 0

        # تحديث عرض المستوى الحالي
        self.update_level_display()

        # إعادة تعيين مربعات اختيار الفروض
        for checkbox in self.exam_checkboxes:
            checkbox.setChecked(False)

        # لا نعيد المعالج هنا، فقط نمسح الحقول

    def ensure_table_exists(self):
        """التأكد من وجود جدول مسك_أوراق_الفروض في قاعدة البيانات"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS مسك_أوراق_الفروض (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ TEXT,
                    المادة TEXT,
                    الأستاذ TEXT,
                    المستوى TEXT,
                    القسم TEXT,
                    نوع_الفرض TEXT,
                    المتغيبون TEXT,
                    تاريخ_التسجيل TEXT,
                    السنة_الدراسية TEXT,
                    الأسدس TEXT
                )
            """)
            conn.commit()
            conn.close()
        except Exception as e:
            error_message = f"حدث خطأ أثناء التحقق من وجود الجدول:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
            traceback.print_exc()

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()

            cursor.execute("""
                SELECT DISTINCT المادة FROM الأساتذة
                WHERE المادة IS NOT NULL AND المادة != ''
                ORDER BY المادة
            """)
            subjects = [row[0] for row in cursor.fetchall()]
            self.subject_combo.clear()
            self.subject_combo.addItem("")
            self.subject_combo.addItems(subjects)
            self.subject_combo.setCurrentIndex(0)

            # تم نقل تحميل المستويات إلى دالة load_levels_and_sections

            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                self.current_year = result[0]
                self.current_semester = result[1]
            else:
                self.current_year = ""
                self.current_semester = ""
            conn.close()
        except Exception as e:
            error_message = f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
            traceback.print_exc()

    def connect_signals(self):
        """ربط إشارات العناصر"""
        self.subject_combo.currentTextChanged.connect(self.update_teachers)
        # تم استبدال levels_combo بـ levels_tree وتم ربط الإشارات في دالة load_levels_and_sections

    def update_teachers(self, subject):
        """تحديث قائمة الأساتذة بناءً على المادة المختارة"""
        try:
            if not subject:
                self.teacher_combo.clear()
                self.teacher_combo.addItem("")
                return

            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT اسم_الأستاذ FROM الأساتذة
                WHERE المادة = ? AND اسم_الأستاذ IS NOT NULL AND اسم_الأستاذ != ''
                ORDER BY اسم_الأستاذ
            """, (subject,))
            teachers = [row[0] for row in cursor.fetchall()]
            self.teacher_combo.clear()
            self.teacher_combo.addItem("")
            self.teacher_combo.addItems(teachers)
            self.teacher_combo.setCurrentIndex(0)
            conn.close()
        except Exception as e:
            print(f"خطأ في تحديث قائمة الأساتذة: {e}")

    def save_all_exams(self):
        """حفظ جميع الفروض في قاعدة البيانات"""
        # التحقق من أننا في المرحلة الأخيرة (للتأكيد، رغم أن الزر لا يظهر إلا هناك)
        if self.stacked_widget.currentIndex() != self.stacked_widget.count() - 1:
            ConfirmationDialogs.show_custom_warning_message(self, "يرجى إكمال جميع مراحل المعالج أولاً.", "خطأ")
            return

        try:
            # التحقق من إدخال البيانات الإلزامية من المراحل السابقة
            if not self.subject_combo.currentText(): # من المرحلة 1
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار المادة (من المرحلة 1).", "تنبيه")
                self.stacked_widget.setCurrentIndex(0) # العودة للمرحلة 1
                self.update_navigation_buttons()
                return

            if not self.teacher_combo.currentText(): # من المرحلة 1
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار الأستاذ (من المرحلة 1).", "تنبيه")
                self.stacked_widget.setCurrentIndex(0)
                self.update_navigation_buttons()
                return

            # جمع الأقسام المحددة من الطريقة الجديدة
            selected_sections = []
            selected_levels_display_names = set() # لتخزين أسماء المستويات

            # جمع الأقسام المحددة من جميع المستويات
            for level in self.selected_sections:
                if self.selected_sections[level]:  # إذا كان هناك أقسام محددة لهذا المستوى
                    selected_levels_display_names.add(level)  # إضافة اسم المستوى
                    selected_sections.extend(self.selected_sections[level])  # إضافة الأقسام المحددة

            # التحقق من اختيار الأقسام
            if not selected_sections:
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار قسم واحد على الأقل (من المرحلة 2).", "تنبيه")




                self.stacked_widget.setCurrentIndex(1)
                self.update_navigation_buttons()
                return


            selected_exam_types = [] # من المرحلة 3
            for i, checkbox in enumerate(self.exam_checkboxes):
                if checkbox.isChecked():
                    exam_type_item = self.exam_types_table.item(i, 1)
                    if exam_type_item:
                        selected_exam_types.append(exam_type_item.text())


            if not selected_exam_types:
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار نوع فرض واحد على الأقل (من المرحلة 3).", "تنبيه")
                # لا حاجة لتغيير المرحلة هنا لأننا بالفعل في المرحلة الأخيرة
                return

            # الحصول على البيانات المشتركة من النموذج
            date = self.date_edit.date().toString("yyyy-MM-dd")
            subject = self.subject_combo.currentText()
            teacher = self.teacher_combo.currentText()
            absent_students = "" # قيمة افتراضية، يمكن تعديلها لاحقًا إذا لزم الأمر

            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            exams_added = 0

            for section in selected_sections:
                # الحصول على المستوى الصحيح للقسم الحالي من قاعدة البيانات
                cursor.execute("SELECT المستوى FROM البنية_التربوية WHERE القسم = ? AND السنة_الدراسية = ? LIMIT 1", (section, self.current_year))
                level_result = cursor.fetchone()
                current_level_for_section = level_result[0] if level_result else "غير محدد"


                for exam_type in selected_exam_types:
                    cursor.execute("""
                        SELECT id FROM مسك_أوراق_الفروض
                        WHERE الأستاذ = ? AND القسم = ? AND نوع_الفرض = ? AND الأسدس = ? AND السنة_الدراسية = ?
                    """, (teacher, section, exam_type, self.current_semester, self.current_year))
                    existing_record = cursor.fetchone()

                    if existing_record:
                        cursor.execute("""
                            UPDATE مسك_أوراق_الفروض
                            SET التاريخ = ?, المادة = ?, المستوى = ?, المتغيبون = ?, تاريخ_التسجيل = ?
                            WHERE id = ?
                        """, (date, subject, current_level_for_section, absent_students, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), existing_record[0]))
                        exams_added += 1
                    else:
                        cursor.execute("""
                            INSERT INTO مسك_أوراق_الفروض (التاريخ, المادة, الأستاذ, المستوى, القسم, نوع_الفرض, المتغيبون, تاريخ_التسجيل, السنة_الدراسية, الأسدس)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (date, subject, teacher, current_level_for_section, section, exam_type, absent_students, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), self.current_year, self.current_semester))
                        exams_added += 1

            conn.commit()

            # إنشاء رسالة نجاح مفصلة
            success_message_parts = [
                f"تم مسك {exams_added} فرض بنجاح.",
                "-----------------------------------",
                "تفاصيل عملية المسك:",
                f"  - المادة: {subject}",
                f"  - الأستاذ: {teacher}",
            ]

            if selected_levels_display_names:
                sorted_levels = sorted(list(selected_levels_display_names))
                levels_str = "، ".join(sorted_levels)
                success_message_parts.append(f"  - المستويات ({len(sorted_levels)}): {levels_str}")

            if selected_sections:
                sorted_sections = sorted(selected_sections)
                sections_str = "، ".join(sorted_sections)
                # التحكم في طول عرض الأقسام إذا كانت كثيرة جداً
                if len(sections_str) > 100:
                    sections_str = sections_str[:97] + "..."
                success_message_parts.append(f"  - الأقسام الممسوكة ({len(sorted_sections)}): {sections_str}")

            if selected_exam_types:
                sorted_exam_types = sorted(selected_exam_types)
                exam_types_str = "، ".join(sorted_exam_types)
                success_message_parts.append(f"  - نوع الفرض الممسوك ({len(sorted_exam_types)}): {exam_types_str}")

            # تجميع المعلومات المطلوبة في رسالة واحدة بتنسيق HTML
            sections_str = "، ".join(sorted(selected_sections)) if selected_sections else "لا يوجد"
            exam_types_str = "، ".join(sorted(selected_exam_types)) if selected_exam_types else "لا يوجد"

            # استخدام تنسيق HTML للرسالة
            success_message = f"""<html>
<body dir="rtl" style="text-align: center;">
<p style="font-size: 14pt; font-weight: bold; color: green;">تم مسك الفروض بنجاح</p>
<hr style="border: 1px solid #ccc; margin: 10px 0;">
<p style="text-align: right; font-size: 13pt;"><b>المادة:</b> {subject}</p>
<p style="text-align: right; font-size: 13pt;"><b>الأستاذ:</b> {teacher}</p>
<p style="text-align: right; font-size: 13pt;"><b>الأقسام الممسوكة:</b> {sections_str}</p>
<p style="text-align: right; font-size: 13pt;"><b>نوع الفرض الممسوك:</b> {exam_types_str}</p>
</body>
</html>"""

            conn.close()

            ConfirmationDialogs.show_custom_success_message(self, success_message, "نجاح مسك الفروض")
            self.clear_form_and_reset_wizard()

        except Exception as e:
            error_message = f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
            traceback.print_exc()

    # تم إزالة دالة show_scoring_sheet ودوالها المساعدة

    def show_teacher_exams_report(self):
        """عرض تقرير الفروض حسب الأستاذ وإنشاء ملف PDF باستخدام print10.py"""
        if not REPORTLAB_AVAILABLE:
            ConfirmationDialogs.show_custom_error_message(self, "مكتبة ReportLab غير متاحة. لا يمكن إنشاء التقرير.", "خطأ")
            return

        try:
            # إنشاء نافذة حوار لاختيار الأستاذ
            teacher_dialog = QDialog(self)
            teacher_dialog.setWindowTitle("اختيار الأستاذ للتقرير")
            teacher_dialog.setMinimumWidth(400)
            teacher_dialog.setLayoutDirection(Qt.RightToLeft)

            dialog_layout = QVBoxLayout(teacher_dialog)

            teacher_label = QLabel("اختر الأستاذ:")
            teacher_label.setFont(QFont("Calibri", 13, QFont.Bold))

            teacher_combo = QComboBox()
            teacher_combo.setFont(QFont("Calibri", 13))

            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT الأستاذ FROM مسك_أوراق_الفروض
                WHERE الأستاذ IS NOT NULL AND الأستاذ != ''
                ORDER BY الأستاذ
            """)
            teachers = [row[0] for row in cursor.fetchall()]
            conn.close()

            if not teachers:
                ConfirmationDialogs.show_custom_info_message(self, "لا توجد بيانات فروض مسجلة للأساتذة لإنشاء تقرير.", "معلومات")
                return

            teacher_combo.addItems(teachers)
            dialog_layout.addWidget(teacher_label)
            dialog_layout.addWidget(teacher_combo)

            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.button(QDialogButtonBox.Ok).setText("إنشاء التقرير")
            button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
            button_box.accepted.connect(teacher_dialog.accept)
            button_box.rejected.connect(teacher_dialog.reject)
            dialog_layout.addWidget(button_box)

            if teacher_dialog.exec_() != QDialog.Accepted:
                return

            selected_teacher = teacher_combo.currentText()
            if not selected_teacher:
                return

            # جلب بيانات الفروض للأستاذ المحدد
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT المادة, المستوى, القسم, نوع_الفرض, التاريخ, الأسدس
                FROM مسك_أوراق_الفروض
                WHERE الأستاذ = ? AND السنة_الدراسية = ?
                ORDER BY التاريخ DESC, المادة, المستوى, القسم, نوع_الفرض
            """, (selected_teacher, self.current_year))
            exams_data = cursor.fetchall()
            conn.close()

            if not exams_data:
                ConfirmationDialogs.show_custom_info_message(self, f"لا توجد فروض مسجلة للأستاذ {selected_teacher} في السنة الدراسية الحالية.", "معلومات")
                return

            # جلب اسم المؤسسة من قاعدة البيانات
            school_name_str = "اسم المؤسسة (يرجى التحقق)" # قيمة افتراضية
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()
                cursor.execute("SELECT اسم_المؤسسة_الرسمي FROM بيانات_المؤسسة LIMIT 1")
                school_name_db_result = cursor.fetchone()
                if school_name_db_result and school_name_db_result[0]:
                    school_name_str = school_name_db_result[0]
                else:
                    try:
                        cursor.execute("SELECT اسم_المؤسسة FROM بيانات_المؤسسة LIMIT 1")
                        school_name_db_result = cursor.fetchone()
                        if school_name_db_result and school_name_db_result[0]:
                            school_name_str = school_name_db_result[0]
                    except sqlite3.Error:
                         pass # تجاهل الخطأ إذا لم يتم العثور على العمود البديل
                conn.close()
            except sqlite3.Error as db_err:
                print(f"خطأ في جلب اسم المؤسسة (sub21_window): {db_err}. استخدام القيمة الافتراضية.")


            # إنشاء مسار لحفظ التقرير على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            reports_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة", "الفروض الممسوكة")

            # التأكد من وجود المجلد وإنشائه إذا لم يكن موجوداً
            if not os.path.exists(reports_folder):
                os.makedirs(reports_folder)

            # تنسيق اسم الملف ليتضمن اسم الأستاذ والمادة والتاريخ
            current_date = datetime.now().strftime("%Y-%m-%d")
            file_name = f"تقرير_{selected_teacher}_{exams_data[0][0]}_{current_date}.pdf"
            pdf_filename = os.path.join(reports_folder, file_name)

            # استدعاء الدالة من print10.py
            report_generated = generate_teacher_pdf_report_code(
                selected_teacher,
                exams_data,
                pdf_filename,
                self.current_year,
                school_name_str,
                self.dark_blue_color # هذا هو self.dark_blue_color من الكلاس
            )

            if report_generated:
                success_message = f"""<html>
<body dir="rtl" style="text-align: center;">
<p style="font-size: 14pt; font-weight: bold; color: green;">تم إنشاء تقرير الأستاذ بنجاح</p>
<hr style="border: 1px solid #ccc; margin: 10px 0;">
<p style="text-align: right; font-size: 13pt;"><b>الأستاذ:</b> {selected_teacher}</p>
<p style="text-align: right; font-size: 13pt;"><b>المادة:</b> {exams_data[0][0]}</p>
<p style="text-align: right; font-size: 13pt;"><b>مسار الملف:</b> {pdf_filename}</p>
</body>
</html>"""

                ConfirmationDialogs.show_custom_success_message(self, success_message, "نجاح إنشاء التقرير")

                # فتح المجلد الذي يحتوي على التقرير
                try:
                    # نظام التشغيل هو Windows
                    os.startfile(reports_folder)
                except Exception as e_open:
                    print(f"لم يتمكن من فتح المجلد: {e_open}")
            else:
                ConfirmationDialogs.show_custom_error_message(self, "فشل إنشاء تقرير PDF. راجع وحدة التحكم للمزيد من التفاصيل.", "خطأ في إنشاء التقرير")

        except Exception as e:
            error_message = f"حدث خطأ أثناء معالجة طلب تقرير الأستاذ:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
            traceback.print_exc()

    def show_section_exams_report(self):
        """عرض تقرير الفروض حسب القسم"""
        try:
            # إنشاء نافذة حوار لاختيار المستوى والقسم
            section_dialog = QDialog(self)
            section_dialog.setWindowTitle("اختيار القسم للتقرير")
            section_dialog.setMinimumWidth(400)
            section_dialog.setLayoutDirection(Qt.RightToLeft)

            dialog_layout = QVBoxLayout(section_dialog)

            # اختيار المستوى
            level_label = QLabel("اختر المستوى:")
            level_label.setFont(QFont("Calibri", 13, QFont.Bold))

            level_combo = QComboBox()
            level_combo.setFont(QFont("Calibri", 13))

            # اختيار القسم
            section_label = QLabel("اختر القسم:")
            section_label.setFont(QFont("Calibri", 13, QFont.Bold))

            section_combo = QComboBox()
            section_combo.setFont(QFont("Calibri", 13))



            # تحميل المستويات من قاعدة البيانات
            conn = sqlite3.connect("data.db")
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT المستوى FROM مسك_أوراق_الفروض
                WHERE المستوى IS NOT NULL AND المستوى != ''
                ORDER BY المستوى
            """)
            levels = [row[0] for row in cursor.fetchall()]

            if not levels:
                ConfirmationDialogs.show_custom_info_message(self, "لا توجد بيانات فروض مسجلة للأقسام لإنشاء تقرير.", "معلومات")
                return

            level_combo.addItems(levels)

            # دالة لتحديث قائمة الأقسام عند تغيير المستوى
            def update_sections():
                selected_level = level_combo.currentText()
                section_combo.clear()

                cursor.execute("""
                    SELECT DISTINCT القسم FROM مسك_أوراق_الفروض
                    WHERE المستوى = ? AND القسم IS NOT NULL AND القسم != ''
                    ORDER BY القسم
                """, (selected_level,))

                sections = [row[0] for row in cursor.fetchall()]
                section_combo.addItems(sections)

            # ربط دالة تحديث الأقسام بتغيير المستوى
            level_combo.currentIndexChanged.connect(update_sections)

            # تحديث قائمة الأقسام للمستوى الأول
            update_sections()

            # إضافة العناصر إلى التخطيط
            dialog_layout.addWidget(level_label)
            dialog_layout.addWidget(level_combo)
            dialog_layout.addWidget(section_label)
            dialog_layout.addWidget(section_combo)

            # أزرار الموافقة والإلغاء
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.button(QDialogButtonBox.Ok).setText("إنشاء التقرير")
            button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
            button_box.accepted.connect(section_dialog.accept)
            button_box.rejected.connect(section_dialog.reject)
            dialog_layout.addWidget(button_box)

            # عرض النافذة وانتظار الرد
            if section_dialog.exec_() != QDialog.Accepted:
                conn.close()
                return

            # الحصول على المستوى والقسم المختارين
            selected_level = level_combo.currentText()
            selected_section = section_combo.currentText()

            if not selected_level or not selected_section:
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى اختيار المستوى والقسم أولاً.", "بيانات غير مكتملة")
                conn.close()
                return

            # جلب بيانات الفروض للقسم المحدد
            cursor.execute("""
                SELECT المادة, الأستاذ, نوع_الفرض, التاريخ, الأسدس
                FROM مسك_أوراق_الفروض
                WHERE المستوى = ? AND القسم = ? AND السنة_الدراسية = ?
                ORDER BY التاريخ DESC, المادة, الأستاذ
            """, (selected_level, selected_section, self.current_year))

            exams_data = cursor.fetchall()
            conn.close()

            if not exams_data:
                ConfirmationDialogs.show_custom_info_message(self, f"لا توجد فروض مسجلة للقسم {selected_section} في المستوى {selected_level} للسنة الدراسية الحالية.", "معلومات")
                return

            # جلب اسم المؤسسة من قاعدة البيانات
            school_name_str = "اسم المؤسسة (يرجى التحقق)" # قيمة افتراضية
            try:
                conn = sqlite3.connect("data.db")
                cursor = conn.cursor()
                cursor.execute("SELECT اسم_المؤسسة_الرسمي FROM بيانات_المؤسسة LIMIT 1")
                school_name_db_result = cursor.fetchone()
                if school_name_db_result and school_name_db_result[0]:
                    school_name_str = school_name_db_result[0]
                else:
                    try:
                        cursor.execute("SELECT اسم_المؤسسة FROM بيانات_المؤسسة LIMIT 1")
                        school_name_db_result = cursor.fetchone()
                        if school_name_db_result and school_name_db_result[0]:
                            school_name_str = school_name_db_result[0]
                    except sqlite3.Error:
                        pass # تجاهل الخطأ إذا لم يتم العثور على العمود البديل
                conn.close()
            except sqlite3.Error as db_err:
                print(f"خطأ في جلب اسم المؤسسة (sub21_window): {db_err}. استخدام القيمة الافتراضية.")

            # إنشاء مسار لحفظ التقرير على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            reports_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة", "الفروض الممسوكة")

            # التأكد من وجود المجلد وإنشائه إذا لم يكن موجوداً
            if not os.path.exists(reports_folder):
                os.makedirs(reports_folder)

            # تنسيق اسم الملف ليتضمن المستوى والقسم والتاريخ
            current_date = datetime.now().strftime("%Y-%m-%d")
            file_name = f"تقرير_قسم_{selected_level}_{selected_section}_{current_date}.pdf"
            pdf_filename = os.path.join(reports_folder, file_name)

            # استخدام نفس الدالة المستخدمة في تقرير الأستاذ ولكن مع تعديلها لتناسب تقرير القسم
            report_generated = generate_section_pdf_report(
                selected_level,
                selected_section,
                exams_data,
                pdf_filename,
                self.current_year,
                school_name_str,
                self.dark_blue_color
            )

            if report_generated:
                # عرض رسالة نجاح
                success_message = f"""<html>
<body dir="rtl" style="text-align: center;">
<p style="font-size: 14pt; font-weight: bold; color: green;">تم إنشاء تقرير القسم بنجاح</p>
<hr style="border: 1px solid #ccc; margin: 10px 0;">
<p style="text-align: right; font-size: 13pt;"><b>المستوى:</b> {selected_level}</p>
<p style="text-align: right; font-size: 13pt;"><b>القسم:</b> {selected_section}</p>
<p style="text-align: right; font-size: 13pt;"><b>عدد الفروض:</b> {len(exams_data)}</p>
<p style="text-align: right; font-size: 13pt;"><b>مسار الملف:</b> {pdf_filename}</p>
</body>
</html>"""

                ConfirmationDialogs.show_custom_success_message(self, success_message, "نجاح إنشاء تقرير القسم")

                # فتح المجلد الذي يحتوي على التقرير
                try:
                    os.startfile(reports_folder)
                except Exception as e_open:
                    print(f"لم يتمكن من فتح المجلد: {e_open}")
            else:
                ConfirmationDialogs.show_custom_error_message(self, "فشل إنشاء تقرير PDF. راجع وحدة التحكم للمزيد من التفاصيل.", "خطأ في إنشاء التقرير")

        except Exception as e:
            error_message = f"حدث خطأ أثناء معالجة طلب تقرير القسم:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ عام")
            traceback.print_exc()

    def show_delete_exam_dialog(self):
        """عرض نافذة اختيار الأستاذ لحذف فروضه"""
        show_delete_exams_dialog(self)

    # تم حذف الدوال التالية ونقلها إلى sub210_window.py:
    # - show_teacher_exams_for_deletion
    # - load_teacher_exams_data  
    # - select_all_teacher_exams
    # - update_teacher_selected_count
    # - delete_selected_teacher_exams

# إضافة نقطة دخول للاختبار المباشر
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CreateAbsenceTableWindow()
    window.show()
    sys.exit(app.exec_())
