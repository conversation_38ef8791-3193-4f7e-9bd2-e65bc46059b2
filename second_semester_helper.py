import os
import sys
import sqlite3
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QMessageBox, QComboBox, QHBoxLayout, QLabel, QLineEdit
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from reportlab.lib.units import cm
from reportlab.lib import colors

# استيراد دعم العربية
import arabic_reshaper
from bidi.algorithm import get_display

from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle

def reshape_ar(text):
    try:
        return get_display(arabic_reshaper.reshape(str(text)))
    except:
        return text

def calculate_week_start_date(month_start_date, week_number):
    """
    حساب تاريخ بداية الأسبوع بناءً على تاريخ بداية الشهر ورقم الأسبوع
    يتم اعتبار يوم الاثنين كبداية للأسبوع
    """
    try:
        if not month_start_date:
            return ""

        # تحويل النص إلى كائن تاريخ
        if isinstance(month_start_date, str):
            try:
                # محاولة تحليل التاريخ بتنسيق "YYYY-MM-DD"
                start_date = datetime.strptime(month_start_date, "%Y-%m-%d")
            except ValueError:
                try:
                    # محاولة تحليل التاريخ بتنسيق "DD/MM/YYYY"
                    start_date = datetime.strptime(month_start_date, "%d/%m/%Y")
                except ValueError:
                    try:
                        # محاولة تحليل التاريخ بتنسيق "DD-MM-YYYY"
                        start_date = datetime.strptime(month_start_date, "%d-%m-%Y")
                    except ValueError:
                        try:
                            # محاولة تحليل التاريخ بتنسيق "DD.MM.YYYY"
                            start_date = datetime.strptime(month_start_date, "%d.%m.%Y")
                        except ValueError:
                            print(f"تعذر تحليل التاريخ: {month_start_date}")
                            return ""
        else:
            start_date = month_start_date

        # تحديد أول يوم اثنين في الشهر
        # إذا كان تاريخ بداية الشهر هو يوم اثنين (weekday() == 0)، نستخدمه كما هو
        # وإلا نبحث عن أول يوم اثنين بعد تاريخ بداية الشهر
        if start_date.weekday() == 0:  # 0 يمثل يوم الاثنين في Python
            first_monday = start_date
        else:
            # حساب عدد الأيام حتى يوم الاثنين التالي
            days_until_monday = (7 - start_date.weekday()) % 7
            if days_until_monday == 0:
                days_until_monday = 7  # إذا كان اليوم اثنين، نذهب إلى الاثنين التالي
            first_monday = start_date + timedelta(days=days_until_monday)

        # حساب تاريخ بداية الأسبوع (الأسبوع الأول يبدأ من أول يوم اثنين)
        week_start = first_monday + (week_number - 1) * timedelta(days=7)

        # إرجاع التاريخ بتنسيق "DD.MM.YYYY"
        return week_start.strftime("%d.%m.%Y")
    except Exception as e:
        print(f"خطأ في حساب تاريخ بداية الأسبوع: {str(e)}")
        return ""

class YearlyAbsenceSummaryWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("جدول الغياب السنوي")
        self.setGeometry(400, 200, 400, 200)

        layout = QVBoxLayout()

        self.section_combo = QComboBox()
        self.section_combo.addItem("كل الأقسام")
        self.populate_sections()
        layout.addWidget(self.section_combo)

        self.create_button = QPushButton("إنشاء جدول الغياب السنوي")
        self.create_button.clicked.connect(self.generate_summary_tables)
        layout.addWidget(self.create_button)

        self.report_button = QPushButton("إنشاء تقرير PDF لكل أسدس")
        self.report_button.clicked.connect(self.generate_all_semester_reports)
        layout.addWidget(self.report_button)

        # ✅ مربع تحرير وسرد للشهور
        self.month_combo = QComboBox()
        self.month_combo.addItems([
            "كل الشهور", "شتنبر", "أكتوبر", "نونبر", "دجنبر", "يناير",
            "فبراير", "مارس", "أبريل", "ماي", "يونيو"
        ])
        layout.addWidget(self.month_combo)

        self.monthly_button = QPushButton("إنشاء تقرير شهري حسب القسم")
        self.monthly_button.clicked.connect(self.generate_monthly_report)
        layout.addWidget(self.monthly_button)

        # إضافة قسم تقرير الغياب حسب تلميذ محدد
        layout.addWidget(QLabel("تقرير الغياب حسب تلميذ محدد:"))

        # إضافة صف أفقي لمربع النص وزر التقرير
        student_layout = QHBoxLayout()

        # إضافة مربع نص لإدخال رمز التلميذ
        self.student_code_label = QLabel("رمز التلميذ:")
        student_layout.addWidget(self.student_code_label)

        self.student_code_input = QLineEdit()
        self.student_code_input.setPlaceholderText("أدخل رمز التلميذ هنا")
        student_layout.addWidget(self.student_code_input)

        # إضافة زر إنشاء التقرير
        self.student_report_button = QPushButton("إنشاء تقرير الغياب للتلميذ")
        self.student_report_button.clicked.connect(self.generate_student_absence_report)
        student_layout.addWidget(self.student_report_button)

        # إضافة التخطيط الأفقي إلى التخطيط الرئيسي
        layout.addLayout(student_layout)

        self.setLayout(layout)

    def populate_sections(self):
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            cur.execute("SELECT DISTINCT القسم FROM اللوائح")
            sections = cur.fetchall()
            for section in sections:
                self.section_combo.addItem(section[0])
            conn.close()
        except Exception as e:
            print(f"خطأ في تحميل الأقسام: {e}")

    def generate_summary_tables(self):
        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            months_first = ["شتنبر", "أكتوبر", "نونبر", "دجنبر", "يناير"]
            months_second = ["فبراير", "مارس", "أبريل", "ماي", "يونيو"]
            all_months = months_first + months_second

            cur.execute("DROP TABLE IF EXISTS غياب_الأسدس_الأول")
            cur.execute("DROP TABLE IF EXISTS غياب_الأسدس_الثاني")

            cur.execute("""
                CREATE TABLE IF NOT EXISTS غياب_الأسدس_الأول (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رمز_التلميذ TEXT,
                    الاسم_والنسب TEXT,
                    رت TEXT,
                    القسم TEXT,
                    شتنبر INTEGER, أكتوبر INTEGER, نونبر INTEGER,
                    دجنبر INTEGER, يناير INTEGER,
                    مجموع_الغياب_الدوري INTEGER,
                    الغياب_المبرر INTEGER,
                    مجموع_الغياب_غير_المبرر INTEGER,
                    المخالفات INTEGER,
                    نقطة_السلوك REAL,
                    نقطة_المواظبة REAL
                )
            """)

            cur.execute("""
                CREATE TABLE IF NOT EXISTS غياب_الأسدس_الثاني (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رمز_التلميذ TEXT,
                    الاسم_والنسب TEXT,
                    رت TEXT,
                    القسم TEXT,
                    فبراير INTEGER, مارس INTEGER, أبريل INTEGER,
                    ماي INTEGER, يونيو INTEGER,
                    مجموع_الغياب_الدوري INTEGER,
                    الغياب_المبرر INTEGER,
                    مجموع_الغياب_غير_المبرر INTEGER,
                    المخالفات INTEGER,
                    نقطة_السلوك REAL,
                    نقطة_المواظبة REAL
                )
            """)

            cur.execute("""
                SELECT m.رمز_التلميذ, m.الشهر, m.مجموع_شهري, m.الغياب_المبرر,
                       l.رت, l.القسم, s.الاسم_والنسب
                FROM مسك_الغياب_الأسبوعي m
                LEFT JOIN اللوائح l ON m.رمز_التلميذ = l.الرمز
                LEFT JOIN السجل_العام s ON m.رمز_التلميذ = s.الرمز
            """)
            records = cur.fetchall()

            students_data = defaultdict(lambda: {
                'رت': '',
                'القسم': '',
                'الاسم_والنسب': '',
                'غياب_المبرر': 0,
                **{month: 0 for month in all_months}
            })

            for code, month, total, justified, rt, section, name in records:
                students_data[code]['رت'] = rt
                students_data[code]['القسم'] = section
                students_data[code]['الاسم_والنسب'] = name
                students_data[code][month] += total if total else 0
                students_data[code]['غياب_المبرر'] += justified if justified else 0

            # التحقق من وجود جدول المخالفات
            try:
                cur.execute("PRAGMA table_info(المخالفات)")
                violations_columns = [col[1] for col in cur.fetchall()]
                has_violations_table = len(violations_columns) > 0
                print(f"هل يوجد جدول المخالفات؟ {has_violations_table}")
                if has_violations_table:
                    print(f"أعمدة جدول المخالفات: {violations_columns}")
            except Exception as e:
                has_violations_table = False
                print(f"خطأ في التحقق من جدول المخالفات: {str(e)}")

            # جلب بيانات المخالفات إذا كان الجدول موجودًا
            violations_data = defaultdict(lambda: {'الأسدس_الأول': 0, 'الأسدس_الثاني': 0})
            if has_violations_table:
                try:
                    # التحقق من وجود عمود الأسدس في جدول المخالفات
                    has_semester_column = 'الأسدس' in violations_columns

                    if has_semester_column:
                        # جلب المخالفات مع تصفية حسب الأسدس
                        cur.execute("""
                            SELECT رمز_التلميذ, الأسدس, COUNT(*) as عدد_المخالفات
                            FROM المخالفات
                            GROUP BY رمز_التلميذ, الأسدس
                        """)
                    else:
                        # إذا لم يكن هناك عمود للأسدس، نفترض أن جميع المخالفات في الأسدس الأول
                        cur.execute("""
                            SELECT رمز_التلميذ, COUNT(*) as عدد_المخالفات
                            FROM المخالفات
                            GROUP BY رمز_التلميذ
                        """)

                    violations_rows = cur.fetchall()

                    for row in violations_rows:
                        if has_semester_column:
                            code, semester, count = row
                            if semester == 1 or semester == '1' or semester == 'الأول':
                                violations_data[code]['الأسدس_الأول'] = count
                            elif semester == 2 or semester == '2' or semester == 'الثاني':
                                violations_data[code]['الأسدس_الثاني'] = count
                        else:
                            code, count = row
                            violations_data[code]['الأسدس_الأول'] = count

                    print(f"تم جلب بيانات المخالفات لـ {len(violations_data)} تلميذ")
                except Exception as e:
                    print(f"خطأ في جلب بيانات المخالفات: {str(e)}")

            # الحصول على إعدادات البرنامج
            try:
                cur.execute("""
                    SELECT نقطة_السلوك, معامل_السلوك, نقطة_المواظبة, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة
                    FROM اعدادات_البرنامج
                    LIMIT 1
                """)
                settings = cur.fetchone()
                if not settings:
                    settings = (10, 0.5, 10, 1, 2, 3)  # قيم افتراضية

                # تحويل القيم إلى أرقام مع التعامل مع القيم الفارغة أو None
                نقطة_السلوك = float(settings[0]) if settings[0] and str(settings[0]).lower() != 'none' else 10
                معامل_السلوك = float(settings[1]) if settings[1] and str(settings[1]).lower() != 'none' else 0.5
                نقطة_المواظبة = float(settings[2]) if settings[2] and str(settings[2]).lower() != 'none' else 10
                # إضافة معامل المواظبة (إذا كان موجودًا في الإعدادات، وإلا استخدم قيمة افتراضية)
                معامل_المواظبة = 0.5  # قيمة افتراضية
                # التحقق من وجود عمود معامل_المواظبة في جدول اعدادات_البرنامج
                try:
                    cur.execute("PRAGMA table_info(اعدادات_البرنامج)")
                    columns = [col[1] for col in cur.fetchall()]
                    if 'معامل_المواظبة' in columns:
                        cur.execute("SELECT معامل_المواظبة FROM اعدادات_البرنامج LIMIT 1")
                        result = cur.fetchone()
                        if result and result[0] is not None:
                            معامل_المواظبة = float(result[0])
                except Exception as e:
                    print(f"خطأ في جلب معامل_المواظبة: {str(e)}")

                المخالفة_الاولى = float(settings[3]) if settings[3] and str(settings[3]).lower() != 'none' else 1
                المخالفة_الثانية = float(settings[4]) if settings[4] and str(settings[4]).lower() != 'none' else 2
                المخالفة_الثالثة = float(settings[5]) if settings[5] and str(settings[5]).lower() != 'none' else 3

                print(f"تم جلب إعدادات البرنامج: نقطة السلوك={نقطة_السلوك}, معامل السلوك={معامل_السلوك}, نقطة المواظبة={نقطة_المواظبة}")
            except Exception as e:
                print(f"خطأ في جلب إعدادات البرنامج: {str(e)}")
                # قيم افتراضية في حالة حدوث خطأ
                نقطة_السلوك, معامل_السلوك, نقطة_المواظبة, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة = 10, 0.5, 10, 1, 2, 3

            # جلب بيانات الغياب المبرر لجميع الطلاب مرة واحدة بطريقة أكثر كفاءة
            justified_absences_data = {}
            try:
                # استخدام استعلام أكثر كفاءة مع تجميع البيانات
                cur.execute("""
                    SELECT رمز_التلميذ, الشهر, SUM(CAST(COALESCE(الغياب_المبرر, '0') AS INTEGER)) as total_justified
                    FROM مسك_الغياب_الأسبوعي
                    GROUP BY رمز_التلميذ, الشهر
                """)

                for student_code, month, justified in cur.fetchall():
                    if student_code not in justified_absences_data:
                        justified_absences_data[student_code] = {}

                    # تحويل القيمة إلى رقم (مع التعامل مع القيم الفارغة)
                    try:
                        justified_value = int(justified) if justified else 0
                    except (ValueError, TypeError):
                        justified_value = 0

                    justified_absences_data[student_code][month] = justified_value

                print(f"تم جلب بيانات الغياب المبرر لـ {len(justified_absences_data)} طالب")
            except Exception as e:
                print(f"خطأ في جلب بيانات الغياب المبرر: {str(e)}")
                justified_absences_data = {}

            # تحضير قائمة بجميع البيانات للإدخال دفعة واحدة
            all_first_semester_values = []
            all_second_semester_values = []

            # تقليل عدد رسائل التشخيص لتحسين الأداء
            debug_count = 0
            max_debug_messages = 3  # عدد رسائل التشخيص المسموح بها

            for code, data in students_data.items():
                # حساب مجموع الغياب الدوري للأسدس الأول
                first_semester_total = sum(data[m] for m in months_first)

                # حساب الغياب المبرر للأسدس الأول بطريقة أكثر كفاءة
                first_semester_justified = sum(justified_absences_data.get(code, {}).get(month, 0) for month in months_first)

                first_semester_unjustified = max(0, first_semester_total - first_semester_justified)
                # الحصول على عدد المخالفات للأسدس الأول
                first_semester_violations = violations_data[code]['الأسدس_الأول']

                # حساب نقطة السلوك للأسدس الأول وفقاً للمعادلة المطلوبة
                try:
                    # طباعة رسائل التشخيص فقط لعدد محدود من الطلاب
                    if debug_count < max_debug_messages:
                        print(f"حساب نقطة السلوك للطالب {data['الاسم_والنسب']} في الأسدس الأول:")
                        print(f"  نقطة_السلوك من اعدادات_البرنامج: {نقطة_السلوك}")
                        print(f"  عدد المخالفات: {first_semester_violations}")
                        print(f"  المخالفة_الاولى: {المخالفة_الاولى}")
                        print(f"  المخالفة_الثانية: {المخالفة_الثانية}")
                        print(f"  المخالفة_الثالثة: {المخالفة_الثالثة}")

                    # حساب نقطة السلوك وفقاً للمعادلة المطلوبة
                    if first_semester_violations == 1:
                        first_semester_behavior_score = نقطة_السلوك - المخالفة_الاولى
                    elif first_semester_violations == 2:
                        first_semester_behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية)
                    elif first_semester_violations >= 3:
                        first_semester_behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية + المخالفة_الثالثة)
                    else:
                        first_semester_behavior_score = نقطة_السلوك  # لا توجد مخالفات

                    first_semester_behavior_score = max(0, round(first_semester_behavior_score, 2))  # لا تقل عن صفر

                    if debug_count < max_debug_messages:
                        print(f"  نقطة السلوك المحسوبة: {first_semester_behavior_score}")
                except (ValueError, TypeError) as e:
                    if debug_count < max_debug_messages:
                        print(f"خطأ في حساب نقطة السلوك: {e}")
                    first_semester_behavior_score = float(نقطة_السلوك)  # قيمة افتراضية في حالة حدوث خطأ

                # حساب نقطة المواظبة للأسدس الأول وفقاً للصيغة المطلوبة
                try:
                    # طباعة رسائل التشخيص فقط لعدد محدود من الطلاب
                    if debug_count < max_debug_messages:
                        print(f"حساب نقطة المواظبة للطالب {data['الاسم_والنسب']} في الأسدس الأول:")
                        print(f"  نقطة_المواظبة من اعدادات_البرنامج: {نقطة_المواظبة}")
                        print(f"  مجموع_الغياب_غير_المبرر: {first_semester_unjustified}")
                        print(f"  معامل_المواظبة من اعدادات_البرنامج: {معامل_المواظبة}")
                        debug_count += 1

                    # حساب نقطة المواظبة وفقاً للصيغة المطلوبة
                    first_semester_attendance_score = float(نقطة_المواظبة) - (float(first_semester_unjustified) * float(معامل_المواظبة))
                    first_semester_attendance_score = max(0, round(first_semester_attendance_score, 2))  # لا تقل عن صفر

                    if debug_count <= max_debug_messages:
                        print(f"  نقطة المواظبة المحسوبة: {first_semester_attendance_score}")
                except (ValueError, TypeError) as e:
                    if debug_count < max_debug_messages:
                        print(f"خطأ في حساب نقطة المواظبة: {e}")
                    first_semester_attendance_score = float(نقطة_المواظبة)  # قيمة افتراضية في حالة حدوث خطأ

                # حساب مجموع الغياب الدوري للأسدس الثاني
                second_semester_total = sum(data[m] for m in months_second)

                # حساب الغياب المبرر للأسدس الثاني بطريقة أكثر كفاءة
                second_semester_justified = sum(justified_absences_data.get(code, {}).get(month, 0) for month in months_second)

                second_semester_unjustified = max(0, second_semester_total - second_semester_justified)
                # الحصول على عدد المخالفات للأسدس الثاني
                second_semester_violations = violations_data[code]['الأسدس_الثاني']

                # حساب نقطة السلوك للأسدس الثاني وفقاً للمعادلة المطلوبة
                try:
                    # حساب نقطة السلوك وفقاً للمعادلة المطلوبة
                    if second_semester_violations == 1:
                        second_semester_behavior_score = نقطة_السلوك - المخالفة_الاولى
                    elif second_semester_violations == 2:
                        second_semester_behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية)
                    elif second_semester_violations >= 3:
                        second_semester_behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية + المخالفة_الثالثة)
                    else:
                        second_semester_behavior_score = نقطة_السلوك  # لا توجد مخالفات

                    second_semester_behavior_score = max(0, round(second_semester_behavior_score, 2))  # لا تقل عن صفر
                except (ValueError, TypeError) as e:
                    second_semester_behavior_score = float(نقطة_السلوك)  # قيمة افتراضية في حالة حدوث خطأ

                # حساب نقطة المواظبة للأسدس الثاني وفقاً للصيغة المطلوبة
                try:
                    # حساب نقطة المواظبة وفقاً للصيغة المطلوبة
                    second_semester_attendance_score = float(نقطة_المواظبة) - (float(second_semester_unjustified) * float(معامل_المواظبة))
                    second_semester_attendance_score = max(0, round(second_semester_attendance_score, 2))  # لا تقل عن صفر
                except (ValueError, TypeError) as e:
                    second_semester_attendance_score = float(نقطة_المواظبة)  # قيمة افتراضية في حالة حدوث خطأ

                # إضافة البيانات إلى القوائم للإدخال الجماعي
                values_first = (
                    code, data['الاسم_والنسب'], data['رت'], data['القسم'],
                    *[data[m] for m in months_first],
                    first_semester_total,
                    first_semester_justified,
                    first_semester_unjustified,
                    first_semester_violations,
                    first_semester_behavior_score,
                    first_semester_attendance_score
                )
                values_second = (
                    code, data['الاسم_والنسب'], data['رت'], data['القسم'],
                    *[data[m] for m in months_second],
                    second_semester_total,
                    second_semester_justified,
                    second_semester_unjustified,
                    second_semester_violations,
                    second_semester_behavior_score,
                    second_semester_attendance_score
                )

                all_first_semester_values.append(values_first)
                all_second_semester_values.append(values_second)

            # إدخال البيانات دفعة واحدة لتحسين الأداء
            if all_first_semester_values:
                cur.executemany("""
                    INSERT INTO غياب_الأسدس_الأول
                    (رمز_التلميذ, الاسم_والنسب, رت, القسم, شتنبر, أكتوبر, نونبر, دجنبر, يناير,
                    مجموع_الغياب_الدوري, الغياب_المبرر, مجموع_الغياب_غير_المبرر, المخالفات,
                    نقطة_السلوك, نقطة_المواظبة)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, all_first_semester_values)
                print(f"تم إدخال {len(all_first_semester_values)} سجل في جدول غياب_الأسدس_الأول")

            if all_second_semester_values:
                cur.executemany("""
                    INSERT INTO غياب_الأسدس_الثاني
                    (رمز_التلميذ, الاسم_والنسب, رت, القسم, فبراير, مارس, أبريل, ماي, يونيو,
                    مجموع_الغياب_الدوري, الغياب_المبرر, مجموع_الغياب_غير_المبرر, المخالفات,
                    نقطة_السلوك, نقطة_المواظبة)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, all_second_semester_values)
                print(f"تم إدخال {len(all_second_semester_values)} سجل في جدول غياب_الأسدس_الثاني")

            conn.commit()
            conn.close()
            QMessageBox.information(self, "نجاح", "تم إنشاء الجداول التجميعية بنجاح")

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء الجداول:\n{str(e)}")

    def generate_all_semester_reports(self):
        try:
            selected_section = self.section_combo.currentText()
            semesters = ["الأول", "الثاني"]

            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))

            cur.execute("SELECT المؤسسة, ImagePath1, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            inst = cur.fetchone()
            inst_name, logo_path, school_year = inst if inst else ("المؤسسة", None, "")

            for semester in semesters:
                table_name = f"غياب_الأسدس_{semester}"
                cur.execute(f"PRAGMA table_info({table_name})")
                all_columns = [c[1] for c in cur.fetchall() if c[1] not in ['id']]
                report_columns = [col for col in all_columns if col != 'رمز_التلميذ']
                ordered_columns = ['رت', 'الاسم_والنسب'] + [col for col in report_columns if col not in ['رت', 'الاسم_والنسب']]

                if selected_section == "كل الأقسام":
                    cur.execute("SELECT DISTINCT القسم FROM اللوائح")
                    sections = [row[0] for row in cur.fetchall()]
                else:
                    sections = [selected_section]

                file_path = f"تقرير_غياب_الأسدس_{semester}.pdf"
                doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
                elements = []

                for section in sections:
                    cur.execute(f"SELECT {', '.join(ordered_columns)} FROM {table_name} WHERE القسم = ? ORDER BY CAST(رت AS INTEGER)", (section,))
                    rows = cur.fetchall()

                    if not rows:
                        continue

                    if logo_path and os.path.exists(logo_path):
                        img = Image(logo_path)
                        img.drawWidth = 250
                        img.drawHeight = 80
                        elements.append(img)

                    elements.append(Spacer(1, 0.3*cm))
                    elements.append(Paragraph(reshape_ar(inst_name), ParagraphStyle('header', fontName='Helvetica-Bold', fontSize=15, alignment=1)))
                    title = f"تقرير الغياب - الأسدس {semester} - قسم {section} - السنة الدراسية {school_year}"
                    elements.append(Spacer(1, 0.3*cm))
                    elements.append(Paragraph(reshape_ar(title), ParagraphStyle('title', fontName='Helvetica-Bold', fontSize=14, alignment=1)))
                    elements.append(Spacer(1, 0.3*cm))

                    headers = [reshape_ar(h) for h in ordered_columns if h not in ['السنة_الدراسية', 'القسم']]
                    data = [headers]
                    for row in rows:
                        data.append([reshape_ar(str(c)) for i, c in enumerate(row) if ordered_columns[i] not in ['السنة_الدراسية', 'القسم']])

                    data = [list(reversed(r)) for r in data]

                    table = Table(data, repeatRows=1, hAlign='RIGHT', rowHeights=[20] + [14]*(len(data)-1))
                    table.setStyle(TableStyle([
                        ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                        ('FONTNAME', (0,0), (-1,-1), 'Helvetica'),
                        ('FONTSIZE', (0,0), (-1,-1), 10),
                        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
                        ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                    ]))
                    elements.append(table)
                    elements.append(PageBreak())

                doc.build(elements)
                os.startfile(doc.filename)
                os.startfile(doc.filename)

            QMessageBox.information(self, "نجاح", "تم إنشاء تقارير PDF لغياب الأسدسين")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")

    def generate_monthly_report(self):
        selected_month = self.month_combo.currentText()
        selected_section = self.section_combo.currentText()

        try:
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            # الحصول على بيانات المؤسسة
            cur.execute("SELECT المؤسسة, ImagePath1, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            inst = cur.fetchone()
            inst_name, logo_path, school_year = inst if inst else ("المؤسسة", None, "")

            # إنشاء جدول مؤقت لتخزين بيانات الغياب
            cur.execute("DROP TABLE IF EXISTS مجموع_الغياب_السنوي")
            cur.execute("""
                CREATE TABLE مجموع_الغياب_السنوي (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رمز_التلميذ TEXT,
                    القسم TEXT,
                    رت TEXT,
                    الاسم_والنسب TEXT,
                    الشهر TEXT,
                    مجموع_شهري INTEGER,
                    الغياب_المبرر INTEGER
                )
            """)

            # جلب قائمة الطلاب من جدول اللوائح مع تصفية حسب السنة الدراسية الحالية
            cur.execute("""
                SELECT l.الرمز, l.القسم, l.رت, s.الاسم_والنسب
                FROM اللوائح l
                LEFT JOIN السجل_العام s ON l.الرمز = s.الرمز
                JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                ORDER BY CAST(l.رت AS INTEGER)
            """)
            students = cur.fetchall()

            # تهيئة متغير absences كقائمة فارغة قبل محاولة جلب البيانات
            absences = []

            # التحقق من هيكل جدول مسك_الغياب_الأسبوعي بطريقة أكثر كفاءة
            try:
                # استعلام واحد لجلب هيكل الجدول
                cur.execute("PRAGMA table_info(مسك_الغياب_الأسبوعي)")
                columns_info = cur.fetchall()
                columns = [col[1] for col in columns_info]

                # التحقق من وجود عمود بداية_الشهر
                has_month_start = 'بداية_الشهر' in columns

                # التحقق من وجود أعمدة الأسابيع
                week_columns = []
                for i in range(1, 6):
                    week_col = f'الأسبوع_{i}'
                    if week_col in columns:
                        week_columns.append(week_col)

                # طباعة معلومات مختصرة للتشخيص
                print(f"تم التحقق من هيكل جدول مسك_الغياب_الأسبوعي: {len(columns)} عمود")
                print(f"عمود بداية_الشهر موجود: {has_month_start}")
                print(f"عدد أعمدة الأسابيع الموجودة: {len(week_columns)}")

                # بناء استعلام SQL ديناميكي بطريقة أكثر كفاءة
                # استخدام استعلام مع تحويل القيم مباشرة وتجميع البيانات
                select_columns = ["رمز_التلميذ", "الشهر",
                                 "CAST(COALESCE(مجموع_شهري, '0') AS INTEGER) as مجموع_شهري",
                                 "CAST(COALESCE(الغياب_المبرر, '0') AS INTEGER) as الغياب_المبرر"]

                if has_month_start:
                    select_columns.append("بداية_الشهر")
                select_columns.extend(week_columns)

                # إضافة فهرس مؤقت لتحسين أداء الاستعلام
                try:
                    cur.execute("CREATE INDEX IF NOT EXISTS idx_temp_student_month ON مسك_الغياب_الأسبوعي(رمز_التلميذ, الشهر)")
                except:
                    pass  # تجاهل الخطأ إذا كان الفهرس موجودًا بالفعل

                query = f"""
                    SELECT {', '.join(select_columns)}
                    FROM مسك_الغياب_الأسبوعي mag
                    JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز AND mag.السنة_الدراسية = l.السنة_الدراسية
                    JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                """

                cur.execute(query)
                absences = cur.fetchall()
                print(f"تم جلب {len(absences)} سجل من جدول مسك_الغياب_الأسبوعي")
            except Exception as e:
                print(f"خطأ في جلب بيانات الغياب: {str(e)}")
                # تأكيد تعيين absences كقائمة فارغة في حالة حدوث خطأ
                absences = []

            # تنظيم بيانات الغياب في قاموس للوصول السريع
            absence_dict = {}
            week_dates = {}  # قاموس لتخزين تواريخ بداية الأسابيع لكل شهر

            # تحديد مواقع الأعمدة في نتائج الاستعلام بطريقة أكثر كفاءة
            # تعيين مواقع الأعمدة الأساسية
            col_indices = {
                'رمز_التلميذ': 0,
                'الشهر': 1,
                'مجموع_شهري': 2,
                'الغياب_المبرر': 3
            }

            # تحديد موقع عمود بداية_الشهر وأعمدة الأسابيع
            next_index = 4

            if has_month_start:
                col_indices['بداية_الشهر'] = next_index
                next_index += 1

            # إضافة مواقع أعمدة الأسابيع
            for i, col_name in enumerate(week_columns):
                col_indices[col_name] = next_index + i

            # إضافة مواقع أعمدة الأسابيع بالأرقام (1, 2, 3, 4, 5) للتوافق مع الكود القديم
            for i in range(1, 6):
                week_col = str(i)
                if week_col in columns:
                    col_indices[week_col] = columns.index(week_col)

            # طباعة معلومات مختصرة للتشخيص
            print(f"تم تحديد مواقع {len(col_indices)} عمود في نتائج الاستعلام")

            # معالجة البيانات بطريقة أكثر كفاءة
            # تهيئة القواميس مسبقًا لتجنب التحقق المتكرر
            for code, _, _, _ in students:
                absence_dict[code] = {}

            # تجميع بيانات الأسابيع حسب الشهر
            week_data_by_month = {}

            # معالجة البيانات دفعة واحدة
            for row in absences:
                try:
                    code = row[col_indices['رمز_التلميذ']]
                    month = row[col_indices['الشهر']]

                    # تخطي السجلات غير المرتبطة بطالب معروف
                    if code not in absence_dict:
                        continue

                    # تهيئة بيانات الشهر إذا لم تكن موجودة
                    if month not in absence_dict[code]:
                        absence_dict[code][month] = [0, 0]  # [مجموع_شهري, الغياب_المبرر]

                    # تحديث بيانات الغياب (مع التحويل الآمن للقيم)
                    total = row[col_indices['مجموع_شهري']]
                    justified = row[col_indices['الغياب_المبرر']]

                    try:
                        total_value = int(total) if total else 0
                    except (ValueError, TypeError):
                        total_value = 0

                    try:
                        justified_value = int(justified) if justified else 0
                    except (ValueError, TypeError):
                        justified_value = 0

                    absence_dict[code][month][0] += total_value
                    absence_dict[code][month][1] += justified_value

                    # تخزين تاريخ بداية الشهر وبيانات الأسابيع (مرة واحدة لكل شهر)
                    if 'بداية_الشهر' in col_indices and month not in week_dates and len(row) > col_indices['بداية_الشهر']:
                        month_start_date = row[col_indices['بداية_الشهر']]

                        # تخزين بيانات الأسابيع
                        if month not in week_data_by_month:
                            week_data = {'بداية_الشهر': month_start_date}

                            # إضافة بيانات الأسابيع
                            for i in range(1, 6):
                                week_col = str(i)
                                if week_col in col_indices and len(row) > col_indices[week_col]:
                                    week_data[f'الأسبوع_{i}'] = row[col_indices[week_col]]
                                else:
                                    week_data[f'الأسبوع_{i}'] = 0

                            week_dates[month] = week_data
                except Exception as e:
                    # تقليل رسائل الخطأ للتحسين
                    if len(week_dates) < 3:  # طباعة عدد محدود من رسائل الخطأ
                        print(f"خطأ في معالجة بيانات الغياب: {str(e)}")

            # طباعة ملخص بيانات الأسابيع للتشخيص
            print(f"تم استخراج بيانات الأسابيع لـ {len(week_dates)} شهر")

            # إدخال البيانات في الجدول المؤقت بطريقة أكثر كفاءة
            months = ["شتنبر", "أكتوبر", "نونبر", "دجنبر", "يناير", "فبراير", "مارس", "أبريل", "ماي", "يونيو"]

            # تجميع البيانات في قائمة واحدة للإدخال الجماعي
            all_absence_data = []

            # الحصول على السنة الدراسية الحالية
            cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            current_school_year = cur.fetchone()[0]
            print(f"السنة الدراسية الحالية: {current_school_year}")

            for code, section, rt, name in students:
                # التحقق من أن الطالب ينتمي إلى السنة الدراسية الحالية
                cur.execute("""
                    SELECT السنة_الدراسية FROM اللوائح
                    WHERE الرمز = ? AND السنة_الدراسية = ?
                """, (code, current_school_year))
                student_year = cur.fetchone()

                if not student_year:
                    print(f"تخطي الطالب {code} لأنه لا ينتمي إلى السنة الدراسية الحالية")
                    continue

                for month in months:
                    # الحصول على بيانات الغياب للطالب والشهر
                    total = 0
                    justified = 0
                    if code in absence_dict and month in absence_dict[code]:
                        total = absence_dict[code][month][0]
                        justified = absence_dict[code][month][1]

                    # إضافة البيانات إلى القائمة
                    all_absence_data.append((code, section, rt, name, month, total, justified))

            # إدخال البيانات دفعة واحدة
            if all_absence_data:
                cur.executemany("""
                    INSERT INTO مجموع_الغياب_السنوي
                    (رمز_التلميذ, القسم, رت, الاسم_والنسب, الشهر, مجموع_شهري, الغياب_المبرر)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, all_absence_data)
                print(f"تم إدخال {len(all_absence_data)} سجل في جدول مجموع_الغياب_السنوي")

            conn.commit()

            # طباعة عدد السجلات في الجدول المؤقت للتأكد من وجود بيانات
            cur.execute("SELECT COUNT(*) FROM مجموع_الغياب_السنوي")
            record_count = cur.fetchone()[0]
            print(f"عدد السجلات في جدول مجموع_الغياب_السنوي: {record_count}")

            # تحديد الأقسام المطلوبة بناءً على التصفية
            if selected_section == "كل الأقسام":
                cur.execute("SELECT DISTINCT القسم FROM مجموع_الغياب_السنوي WHERE القسم IS NOT NULL")
                sections = [row[0] for row in cur.fetchall() if row[0]]
            else:
                sections = [selected_section]

            # طباعة الأقسام المحددة للتأكد
            print(f"الأقسام المحددة: {sections}")

            # تحديد الشهور المطلوبة بناءً على التصفية
            if selected_month == "كل الشهور":
                months = ["شتنبر", "أكتوبر", "نونبر", "دجنبر", "يناير", "فبراير", "مارس", "أبريل", "ماي", "يونيو"]
            else:
                months = [selected_month]

            # طباعة الشهور المحددة للتأكد
            print(f"الشهور المحددة: {months}")

            # طباعة إجمالي عدد الطلاب في كل قسم
            for section in sections:
                try:
                    cur.execute("SELECT COUNT(DISTINCT رمز_التلميذ) FROM مجموع_الغياب_السنوي WHERE القسم = ?", (section,))
                    student_count = cur.fetchone()[0] // len(months)  # قسمة على عدد الشهور لأن كل طالب له سجل لكل شهر
                    print(f"عدد الطلاب في القسم {section}: {student_count}")
                except Exception as e:
                    print(f"خطأ في حساب عدد الطلاب للقسم {section}: {str(e)}")

            pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))

            # إنشاء ملف PDF واحد لكل التقارير
            file_path = f"تقرير_الغياب_الشهري.pdf"
            if selected_section != "كل الأقسام":
                file_path = f"تقرير_الغياب_الشهري_{selected_section}.pdf"
            if selected_month != "كل الشهور":
                file_path = file_path.replace(".pdf", f"_{selected_month}.pdf")

            doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
            elements = []

            # إضافة شعار المؤسسة والعنوان
            if logo_path and os.path.exists(logo_path):
                img = Image(logo_path)
                img.drawWidth = 250
                img.drawHeight = 80
                elements.append(img)

            elements.append(Spacer(1, 0.3*cm))
            elements.append(Paragraph(reshape_ar(inst_name), ParagraphStyle('header', fontName='Arabic', fontSize=15, alignment=1)))
            elements.append(Spacer(1, 0.3*cm))

            # متغير للتحقق من وجود بيانات على الأقل
            has_data = False

            # إنشاء تقرير لكل قسم
            for section in sections:
                for month in months:
                    # استعلام لجلب البيانات حسب القسم والشهر
                    try:
                        query = """
                            SELECT a.رت, a.الاسم_والنسب, a.الشهر, a.مجموع_شهري, a.الغياب_المبرر
                            FROM مجموع_الغياب_السنوي a
                            JOIN اللوائح l ON a.رمز_التلميذ = l.الرمز AND a.القسم = l.القسم
                            JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                            WHERE a.القسم = ? AND a.الشهر = ? AND l.السنة_الدراسية = inst.السنة_الدراسية
                            ORDER BY CAST(a.رت AS INTEGER)
                        """
                        cur.execute(query, (section, month))
                        data = cur.fetchall()

                        # طباعة عدد السجلات المسترجعة للتأكد
                        print(f"عدد السجلات للقسم {section} والشهر {month}: {len(data)}")

                        if not data:
                            print(f"لا توجد بيانات للقسم {section} والشهر {month}")
                            continue
                    except Exception as e:
                        print(f"خطأ في جلب البيانات للقسم {section} والشهر {month}: {str(e)}")
                        continue

                    has_data = True

                    # عنوان التقرير
                    title = f"تقرير الغياب الشهري - قسم {section} - شهر {month} - السنة الدراسية {school_year}"
                    elements.append(Paragraph(reshape_ar(title), ParagraphStyle('title', fontName='Arabic', fontSize=14, alignment=1)))
                    elements.append(Spacer(1, 0.3*cm))

                    # طباعة معلومات الأسابيع للتشخيص
                    print(f"معلومات الأسابيع للشهر {month}:")
                    if month in week_dates:
                        print(f"  تاريخ بداية الشهر: {week_dates[month]['بداية_الشهر']}")
                        for i in range(1, 6):
                            week_key = f'الأسبوع_{i}'
                            print(f"  {week_key}: {week_dates[month][week_key]}")
                    else:
                        print(f"  لا توجد معلومات أسابيع للشهر {month}")

                    # إضافة معلومات الأسابيع
                    week_info = []
                    if month in week_dates and week_dates[month]['بداية_الشهر']:
                        month_start = week_dates[month]['بداية_الشهر']

                        for i in range(1, 6):
                            week_key = f'الأسبوع_{i}'
                            if week_key in week_dates[month] and week_dates[month][week_key]:
                                week_start = calculate_week_start_date(month_start, i)
                                week_info.append(f"الأسبوع {i}: {week_start}")

                    if week_info:
                        week_info_text = " | ".join(week_info)
                        elements.append(Paragraph(reshape_ar(week_info_text), ParagraphStyle('week_info', fontName='Arabic', fontSize=10, alignment=1)))
                        elements.append(Spacer(1, 0.3*cm))

                    # إنشاء جدول البيانات مع تواريخ بداية الأسابيع
                    # تحضير تواريخ بداية الأسابيع
                    week_dates_headers = []

                    # استعلام مباشر للحصول على تواريخ بداية الأسابيع
                    try:
                        db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data.db')
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()

                        # استعلام للحصول على تواريخ بداية الأسابيع
                        cursor.execute("""
                            SELECT mag."1", mag."2", mag."3", mag."4", mag."5", mag."بداية_الشهر"
                            FROM "مسك_الغياب_الأسبوعي" mag
                            JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز AND mag.السنة_الدراسية = l.السنة_الدراسية
                            JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                            WHERE mag."الشهر" = ?
                            LIMIT 1
                        """, (month,))

                        week_dates_row = cursor.fetchone()

                        if week_dates_row and len(week_dates_row) >= 6:
                            # الحصول على تاريخ بداية الشهر
                            month_start_date = week_dates_row[5] if len(week_dates_row) > 5 else None

                            if month_start_date:
                                # حساب تواريخ بداية الأسابيع بناءً على تاريخ بداية الشهر
                                try:
                                    # حساب تواريخ بداية الأسابيع
                                    for i in range(1, 6):
                                        week_start = calculate_week_start_date(month_start_date, i)
                                        if week_start:
                                            # التحقق مما إذا كان الأسبوع الخامس خارج الشهر
                                            if i == 5:
                                                # تحويل تاريخ بداية الشهر إلى كائن تاريخ
                                                month_date = None
                                                if isinstance(month_start_date, str):
                                                    try:
                                                        month_date = datetime.strptime(month_start_date, "%Y-%m-%d")
                                                    except ValueError:
                                                        try:
                                                            month_date = datetime.strptime(month_start_date, "%d/%m/%Y")
                                                        except ValueError:
                                                            try:
                                                                month_date = datetime.strptime(month_start_date, "%d-%m-%Y")
                                                            except ValueError:
                                                                try:
                                                                    month_date = datetime.strptime(month_start_date, "%d.%m.%Y")
                                                                except ValueError:
                                                                    month_date = None

                                                # تحويل تاريخ بداية الأسبوع الخامس إلى كائن تاريخ
                                                week_date = None
                                                try:
                                                    week_date = datetime.strptime(week_start, "%d.%m.%Y")
                                                except ValueError:
                                                    try:
                                                        week_date = datetime.strptime(week_start, "%d/%m/%Y")
                                                    except ValueError:
                                                        week_date = None

                                                # التحقق مما إذا كان الأسبوع الخامس في نفس الشهر
                                                if month_date and week_date:
                                                    if month_date.month != week_date.month:
                                                        # الأسبوع الخامس خارج الشهر، لا نضيفه
                                                        continue

                                            week_dates_headers.append(week_start)
                                        else:
                                            # إذا فشل حساب تاريخ بداية الأسبوع، نضيف اسم الأسبوع فقط
                                            week_dates_headers.append(f"الأسبوع {i}")
                                except Exception as e:
                                    print(f"خطأ في حساب تواريخ بداية الأسابيع: {str(e)}")
                                    # استخدام القيم المخزنة في قاعدة البيانات كاحتياط
                                    for i in range(5):
                                        date_value = week_dates_row[i]
                                        if date_value:
                                            try:
                                                # محاولة تحويل التاريخ إلى كائن تاريخ
                                                date_obj = None
                                                if isinstance(date_value, str):
                                                    try:
                                                        date_obj = datetime.strptime(date_value, "%Y-%m-%d")
                                                    except ValueError:
                                                        try:
                                                            date_obj = datetime.strptime(date_value, "%d/%m/%Y")
                                                        except ValueError:
                                                            try:
                                                                date_obj = datetime.strptime(date_value, "%d-%m-%Y")
                                                            except ValueError:
                                                                try:
                                                                    date_obj = datetime.strptime(date_value, "%d.%m.%Y")
                                                                except ValueError:
                                                                    date_obj = None

                                                if date_obj:
                                                    # حساب تاريخ أول يوم اثنين
                                                    if date_obj.weekday() == 0:  # 0 يمثل يوم الاثنين في Python
                                                        monday_date = date_obj
                                                    else:
                                                        # حساب عدد الأيام حتى يوم الاثنين التالي
                                                        days_until_monday = (7 - date_obj.weekday()) % 7
                                                        if days_until_monday == 0:
                                                            days_until_monday = 7
                                                        monday_date = date_obj + timedelta(days=days_until_monday)

                                                    # تنسيق التاريخ بالشكل المطلوب
                                                    formatted_date = monday_date.strftime('%d.%m.%Y')
                                                    week_dates_headers.append(formatted_date)
                                                else:
                                                    # إذا فشل التحويل، استخدم القيمة كما هي
                                                    week_dates_headers.append(date_value)
                                            except Exception as e:
                                                print(f"خطأ في تنسيق التاريخ {date_value}: {str(e)}")
                                                # إذا فشل التحويل، استخدم القيمة كما هي
                                                week_dates_headers.append(date_value)
                                        else:
                                            # إذا كان التاريخ فارغًا، نضيف اسم الأسبوع فقط
                                            week_dates_headers.append(f"الأسبوع {i+1}")
                            else:
                                # إذا لم يكن هناك تاريخ بداية للشهر، نستخدم القيم المخزنة في قاعدة البيانات
                                for i in range(5):
                                    date_value = week_dates_row[i] if i < len(week_dates_row) else None
                                    if date_value:
                                        week_dates_headers.append(date_value)
                                    else:
                                        week_dates_headers.append(f"الأسبوع {i+1}")
                        else:
                            # استخدام الطريقة القديمة كاحتياط
                            if month in week_dates and 'بداية_الشهر' in week_dates[month]:
                                month_start = week_dates[month]['بداية_الشهر']
                                for i in range(1, 6):
                                    try:
                                        week_start = calculate_week_start_date(month_start, i)
                                        week_dates_headers.append(week_start)
                                    except Exception as e:
                                        print(f"خطأ في حساب تاريخ بداية الأسبوع {i}: {str(e)}")
                                        week_dates_headers.append(f"الأسبوع {i}")

                        cursor.close()
                        conn.close()
                    except Exception as e:
                        print(f"خطأ في استعلام تواريخ بداية الأسابيع: {str(e)}")
                        # استخدام الطريقة القديمة كاحتياط
                        if month in week_dates and 'بداية_الشهر' in week_dates[month]:
                            month_start = week_dates[month]['بداية_الشهر']
                            for i in range(1, 6):
                                try:
                                    week_start = calculate_week_start_date(month_start, i)
                                    week_dates_headers.append(week_start)
                                except Exception as e:
                                    print(f"خطأ في حساب تاريخ بداية الأسبوع {i}: {str(e)}")
                                    week_dates_headers.append(f"الأسبوع {i}")

                    # إنشاء رأس الجدول
                    if week_dates_headers:
                        # تصفية تواريخ الأسابيع لإزالة الأسابيع الفارغة أو الأسابيع التي تقع خارج الشهر
                        filtered_week_dates = []
                        for i, date in enumerate(week_dates_headers):
                            if date and (i < 4 or (i == 4 and date)):  # تضمين الأسبوع الخامس فقط إذا كان له تاريخ صالح
                                # التحقق مما إذا كان الأسبوع الخامس في نفس الشهر
                                if i == 4:
                                    try:
                                        # تحويل تاريخ بداية الشهر إلى كائن تاريخ
                                        month_date = None
                                        month_start_date = week_dates[month]['بداية_الشهر'] if month in week_dates and 'بداية_الشهر' in week_dates[month] else None
                                        if isinstance(month_start_date, str):
                                            try:
                                                month_date = datetime.strptime(month_start_date, "%Y-%m-%d")
                                            except ValueError:
                                                try:
                                                    month_date = datetime.strptime(month_start_date, "%d/%m/%Y")
                                                except ValueError:
                                                    try:
                                                        month_date = datetime.strptime(month_start_date, "%d-%m-%Y")
                                                    except ValueError:
                                                        try:
                                                            month_date = datetime.strptime(month_start_date, "%d.%m.%Y")
                                                        except ValueError:
                                                            month_date = None

                                        # تحويل تاريخ بداية الأسبوع الخامس إلى كائن تاريخ
                                        week_date = None
                                        try:
                                            week_date = datetime.strptime(date, "%d.%m.%Y")
                                        except ValueError:
                                            try:
                                                week_date = datetime.strptime(date, "%d/%m/%Y")
                                            except ValueError:
                                                week_date = None

                                        # إذا كان الأسبوع الخامس في شهر مختلف، لا نضيفه
                                        if month_date and week_date and month_date.month != week_date.month:
                                            continue
                                    except Exception as e:
                                        print(f"خطأ في التحقق من الأسبوع الخامس: {str(e)}")
                                        # في حالة حدوث خطأ، نتخطى الأسبوع الخامس
                                        continue

                                filtered_week_dates.append(date)

                        headers = ['رت', 'الاسم والنسب'] + filtered_week_dates + ['المجموع', 'الغياب_المبرر', 'ملاحظات']
                    else:
                        headers = ['رت', 'الاسم والنسب', 'الشهر', 'مجموع الغياب', 'مبرر', 'ملاحظات']

                    reshaped_headers = [reshape_ar(h) for h in headers]
                    table_data = [reshaped_headers]

                    # إضافة بيانات الطلاب
                    for row in data:
                        student_row = [reshape_ar(str(row[0])), reshape_ar(str(row[1]))]

                        # إضافة بيانات الغياب لكل أسبوع إذا كانت متوفرة
                        if week_dates_headers and month in week_dates:
                            # الحصول على رقم الطالب من البيانات
                            student_code = None
                            try:
                                # البحث عن رقم الطالب في البيانات
                                for i, cell in enumerate(row):
                                    if isinstance(cell, str) and cell.startswith('A') and len(cell) >= 8:
                                        student_code = cell
                                        print(f"تم العثور على رقم الطالب: {student_code}")
                                        break

                                # إذا لم يتم العثور على رقم الطالب، نحاول الحصول عليه من قاعدة البيانات
                                if not student_code:
                                    # استخدام رقم الترتيب للبحث عن رمز الطالب
                                    rt = row[0]  # رقم الترتيب
                                    section_name = section  # اسم القسم

                                    # استعلام للحصول على رمز الطالب من جدول اللوائح
                                    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data.db')
                                    conn_lookup = sqlite3.connect(db_path)
                                    cursor_lookup = conn_lookup.cursor()
                                    # الحصول على السنة الدراسية الحالية
                                    cursor_lookup.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                                    current_school_year = cursor_lookup.fetchone()[0]

                                    cursor_lookup.execute("""
                                        SELECT الرمز FROM اللوائح
                                        WHERE رت = ? AND القسم = ? AND السنة_الدراسية = ?
                                    """, (rt, section_name, current_school_year))

                                    result = cursor_lookup.fetchone()
                                    if result:
                                        student_code = result[0]
                                        print(f"تم العثور على رمز الطالب من قاعدة البيانات: {student_code}")

                                    cursor_lookup.close()
                                    conn_lookup.close()
                            except Exception as e:
                                print(f"خطأ في البحث عن رقم الطالب: {str(e)}")

                            # استعلام للحصول على بيانات الغياب الأسبوعية للطالب
                            weekly_absences = {}
                            if student_code:
                                try:
                                    # استخدام اتصال قاعدة البيانات الرئيسية
                                    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data.db')
                                    conn = sqlite3.connect(db_path)
                                    cursor = conn.cursor()
                                    # استعلام للحصول على بيانات الغياب الأسبوعية
                                    # تصفية البيانات حسب السنة الدراسية أيضًا
                                    # الربط بين جدول اللوائح وجدول مسك_الغياب_الأسبوعي باستخدام عمود السنة_الدراسية
                                    cursor.execute("""
                                        SELECT mag."1", mag."2", mag."3", mag."4", mag."5"
                                        FROM مسك_الغياب_الأسبوعي mag
                                        JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز AND mag.السنة_الدراسية = l.السنة_الدراسية
                                        JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                                        WHERE mag.الشهر = ? AND mag.رمز_التلميذ = ?
                                    """, (month, student_code))
                                    weekly_data = cursor.fetchone()

                                    if weekly_data:
                                        for i in range(1, 6):
                                            if i-1 < len(weekly_data):
                                                # تحويل القيمة إلى عدد صحيح إذا كانت غير فارغة
                                                value = weekly_data[i-1]
                                                if value is not None and value != '':
                                                    try:
                                                        weekly_absences[str(i)] = int(value)
                                                    except (ValueError, TypeError):
                                                        weekly_absences[str(i)] = 0
                                                else:
                                                    weekly_absences[str(i)] = 0

                                        # طباعة بيانات الغياب الأسبوعية للتشخيص
                                        print(f"بيانات الغياب الأسبوعية للطالب {student_code}: {weekly_absences}")

                                    # إغلاق الاتصال بقاعدة البيانات
                                    cursor.close()
                                    conn.close()
                                except Exception as e:
                                    print(f"خطأ في استعلام بيانات الغياب الأسبوعية: {str(e)}")
                                    # محاولة إغلاق الاتصال في حالة حدوث خطأ
                                    try:
                                        if cursor:
                                            cursor.close()
                                        if conn:
                                            conn.close()
                                    except:
                                        pass

                            # إضافة بيانات الغياب لكل أسبوع
                            for i, week_date in enumerate(filtered_week_dates):
                                week_col = str(i+1)
                                if week_col in weekly_absences:
                                    # استخدام قيمة الغياب من البيانات المستخرجة
                                    absence_value = weekly_absences[week_col]
                                    student_row.append(reshape_ar(str(absence_value)))
                                    print(f"إضافة قيمة الغياب للأسبوع {week_col}: {absence_value}")
                                else:
                                    student_row.append(reshape_ar("0"))
                                    print(f"لا توجد قيمة غياب للأسبوع {week_col}, تمت إضافة 0")

                            # إضافة المجموع بعد الأسابيع
                            total_absences = sum(weekly_absences.values()) if weekly_absences else 0
                            student_row.append(reshape_ar(str(total_absences)))

                            # إضافة الغياب المبرر والملاحظات
                            justified_absence = "0"
                            notes = ""
                            try:
                                if student_code:
                                    conn = sqlite3.connect("data.db")
                                    cur = conn.cursor()

                                    # التحقق من وجود عمود ملاحظات في الجدول
                                    cur.execute("PRAGMA table_info(مسك_الغياب_الأسبوعي)")
                                    columns = [col[1] for col in cur.fetchall()]

                                    if "ملاحظات" in columns:
                                        # إذا كان عمود ملاحظات موجودًا، نسترجع الغياب المبرر والملاحظات
                                        # تصفية البيانات حسب السنة الدراسية أيضًا
                                        # الربط بين جدول اللوائح وجدول مسك_الغياب_الأسبوعي باستخدام عمود السنة_الدراسية
                                        cur.execute("""
                                            SELECT mag.الغياب_المبرر, mag.ملاحظات
                                            FROM مسك_الغياب_الأسبوعي mag
                                            JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز AND mag.السنة_الدراسية = l.السنة_الدراسية
                                            JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                                            WHERE mag.رمز_التلميذ = ? AND mag.الشهر = ?
                                        """, (student_code, month))
                                        result = cur.fetchone()
                                        if result:
                                            if result[0]:
                                                justified_absence = str(result[0])
                                            if result[1]:
                                                notes = str(result[1])
                                    else:
                                        # إذا لم يكن عمود ملاحظات موجودًا، نسترجع الغياب المبرر فقط
                                        # تصفية البيانات حسب السنة الدراسية أيضًا
                                        # الربط بين جدول اللوائح وجدول مسك_الغياب_الأسبوعي باستخدام عمود السنة_الدراسية
                                        cur.execute("""
                                            SELECT mag.الغياب_المبرر
                                            FROM مسك_الغياب_الأسبوعي mag
                                            JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز AND mag.السنة_الدراسية = l.السنة_الدراسية
                                            JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                                            WHERE mag.رمز_التلميذ = ? AND mag.الشهر = ?
                                        """, (student_code, month))
                                        result = cur.fetchone()
                                        if result and result[0]:
                                            justified_absence = str(result[0])

                                        # إضافة عمود ملاحظات إلى الجدول إذا لم يكن موجودًا
                                        try:
                                            cur.execute("ALTER TABLE مسك_الغياب_الأسبوعي ADD COLUMN ملاحظات TEXT")
                                            conn.commit()
                                            print("تم إضافة عمود ملاحظات إلى جدول مسك_الغياب_الأسبوعي")
                                        except sqlite3.OperationalError:
                                            # العمود موجود بالفعل أو حدث خطأ آخر
                                            pass

                                    conn.close()
                            except Exception as e:
                                print(f"خطأ في استرجاع الغياب المبرر والملاحظات: {str(e)}")

                            student_row.append(reshape_ar(justified_absence))

                            # إضافة عمود ملاحظات
                            student_row.append(reshape_ar(notes))
                        else:
                            # إضافة بيانات الغياب العادية
                            student_row.append(reshape_ar(str(row[2])))  # الشهر
                            student_row.append(reshape_ar(str(row[3])))  # مجموع الغياب
                            student_row.append(reshape_ar(str(row[4])))  # مبرر

                            # محاولة استرجاع الملاحظات من قاعدة البيانات
                            notes = ""
                            try:
                                student_code = None
                                # استخدام رقم الترتيب للبحث عن رمز الطالب
                                rt = row[0]  # رقم الترتيب
                                section_name = section  # اسم القسم

                                # استعلام للحصول على رمز الطالب من جدول اللوائح
                                db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data.db')
                                conn_lookup = sqlite3.connect(db_path)
                                cursor_lookup = conn_lookup.cursor()
                                # الحصول على السنة الدراسية الحالية
                                cursor_lookup.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                                current_school_year = cursor_lookup.fetchone()[0]

                                cursor_lookup.execute("""
                                    SELECT الرمز FROM اللوائح
                                    WHERE رت = ? AND القسم = ? AND السنة_الدراسية = ?
                                """, (rt, section_name, current_school_year))

                                result = cursor_lookup.fetchone()
                                if result:
                                    student_code = result[0]

                                    # التحقق من وجود عمود ملاحظات في الجدول
                                    cursor_lookup.execute("PRAGMA table_info(مسك_الغياب_الأسبوعي)")
                                    columns = [col[1] for col in cursor_lookup.fetchall()]

                                    if "ملاحظات" in columns:
                                        # استرجاع الملاحظات
                                        # تصفية البيانات حسب السنة الدراسية أيضًا
                                        # الربط بين جدول اللوائح وجدول مسك_الغياب_الأسبوعي باستخدام عمود السنة_الدراسية
                                        cursor_lookup.execute("""
                                            SELECT mag.ملاحظات
                                            FROM مسك_الغياب_الأسبوعي mag
                                            JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز AND mag.السنة_الدراسية = l.السنة_الدراسية
                                            JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
                                            WHERE mag.رمز_التلميذ = ? AND mag.الشهر = ?
                                        """, (student_code, month))
                                        notes_result = cursor_lookup.fetchone()
                                        if notes_result and notes_result[0]:
                                            notes = str(notes_result[0])

                                cursor_lookup.close()
                                conn_lookup.close()
                            except Exception as e:
                                print(f"خطأ في استرجاع الملاحظات: {str(e)}")

                            student_row.append(reshape_ar(notes))  # ملاحظات

                        table_data.append(student_row)

                    # عكس ترتيب البيانات للعرض من اليمين إلى اليسار
                    table_data = [list(reversed(row)) for row in table_data]

                    # إنشاء الجدول
                    table = Table(table_data, repeatRows=1, hAlign='RIGHT', rowHeights=[20] + [14]*(len(table_data)-1))
                    table.setStyle(TableStyle([
                        ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                        ('FONTNAME', (0,0), (-1,-1), 'Arabic'),
                        ('FONTSIZE', (0,0), (-1,-1), 10),
                        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
                        ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                    ]))
                    elements.append(table)
                    elements.append(Spacer(1, 0.5*cm))

                    # إضافة ملخص إحصائي للغياب
                    total_absences = sum(row[3] for row in data if row[3] is not None)
                    total_justified = sum(row[4] for row in data if row[4] is not None)
                    students_with_absence = sum(1 for row in data if row[3] is not None and row[3] > 0)
                    total_students = len(data)

                    # إنشاء ملخص إحصائي مفصل
                    summary_style = ParagraphStyle('summary', fontName='Arabic', fontSize=12, alignment=1)

                    summary1 = f"إجمالي التلاميذ: {total_students} | عدد التلاميذ المتغيبين: {students_with_absence} | نسبة الغياب: {round(students_with_absence/total_students*100 if total_students > 0 else 0, 2)}%"
                    elements.append(Paragraph(reshape_ar(summary1), summary_style))
                    elements.append(Spacer(1, 0.2*cm))

                    summary2 = f"إجمالي الغياب: {total_absences} | الغياب المبرر: {total_justified} | الغياب غير المبرر: {total_absences - total_justified}"
                    elements.append(Paragraph(reshape_ar(summary2), summary_style))

                    # إضافة تاريخ إنشاء التقرير
                    current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
                    date_text = f"تاريخ إنشاء التقرير: {current_date}"
                    elements.append(Spacer(1, 0.5*cm))
                    elements.append(Paragraph(reshape_ar(date_text), ParagraphStyle('date', fontName='Arabic', fontSize=10, alignment=1)))

                    # تم إزالة جدول تفصيلي للأسابيع بناءً على طلب المستخدم

                    elements.append(PageBreak())

            # التحقق من وجود بيانات قبل بناء ملف PDF
            if not has_data:
                QMessageBox.warning(self, "تنبيه", "لا توجد بيانات للعرض بناءً على المعايير المحددة")
                return

            # بناء ملف PDF
            doc.build(elements)
            os.startfile(doc.filename)

            QMessageBox.information(self, "نجاح", "تم إنشاء تقرير الغياب الشهري بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء توليد التقرير:\n{str(e)}")
            # طباعة تفاصيل الخطأ للتصحيح
            import traceback
            print(traceback.format_exc())

    def generate_student_absence_report(self):
        """إنشاء تقرير الغياب السنوي للتلميذ المحدد"""
        try:
            student_code = self.student_code_input.text().strip()

            if not student_code:
                QMessageBox.warning(self, "تنبيه", "الرجاء إدخال رمز التلميذ أولاً")
                return

            # التحقق من وجود التلميذ في قاعدة البيانات
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()

            # استعلام للتحقق من وجود التلميذ
            cur.execute("""
                SELECT sg.الاسم_والنسب, l.القسم
                FROM السجل_العام sg
                JOIN اللوائح l ON sg.الرمز = l.الرمز
                WHERE sg.الرمز = ?
            """, (student_code,))

            student_info = cur.fetchone()

            if not student_info:
                QMessageBox.warning(self, "تنبيه", "لم يتم العثور على تلميذ بهذا الرمز")
                conn.close()
                return

            student_name = student_info[0]
            student_section = student_info[1]

            # استدعاء دالة إنشاء تقرير الغياب للتلميذ
            self.print_student_absence_report(student_code, student_name, student_section)

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الغياب للتلميذ: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def print_student_absence_report(self, student_code, student_name, student_section):
        """طباعة تقرير الغياب السنوي للتلميذ المحدد"""
        try:
            # التحقق من وجود مكتبات إنشاء التقارير
            if not 'arabic_reshaper' in sys.modules:
                QMessageBox.critical(self, "خطأ المكتبات",
                                    "لم يتم العثور على المكتبات المطلوبة للطباعة.\n\n"
                                    "الرجاء تثبيت المكتبات بأمر: pip install reportlab arabic-reshaper python-bidi")
                return

            # تسجيل الخط العربي
            pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))

            # الحصول على بيانات المؤسسة
            conn = sqlite3.connect("data.db")
            cur = conn.cursor()
            cur.execute("SELECT المؤسسة, ImagePath1, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            inst = cur.fetchone()
            inst_name, logo_path, school_year = inst if inst else ("المؤسسة", None, "")

            # إنشاء ملف PDF
            file_path = f"تقرير_الغياب_السنوي_{student_name}_{student_code}.pdf"
            doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.5*cm, bottomMargin=0.5*cm)
            elements = []

            # إضافة شعار المؤسسة والعنوان
            if logo_path and os.path.exists(logo_path):
                img = Image(logo_path)
                img.drawWidth = 250
                img.drawHeight = 80
                elements.append(img)

            elements.append(Spacer(1, 0.3*cm))
            elements.append(Paragraph(reshape_ar(inst_name), ParagraphStyle('header', fontName='Arabic', fontSize=15, alignment=1)))
            elements.append(Spacer(1, 0.3*cm))

            # عنوان التقرير
            title = f"تقرير الغياب السنوي للتلميذ: {student_name} - القسم: {student_section} - السنة الدراسية: {school_year}"
            elements.append(Paragraph(reshape_ar(title), ParagraphStyle('title', fontName='Arabic', fontSize=14, alignment=1)))
            elements.append(Spacer(1, 0.5*cm))

            # إضافة معلومات التلميذ
            student_info = f"رمز التلميذ: {student_code}"
            elements.append(Paragraph(reshape_ar(student_info), ParagraphStyle('info', fontName='Arabic', fontSize=12, alignment=1)))
            elements.append(Spacer(1, 0.5*cm))

            # جلب بيانات الغياب للتلميذ
            months = ["شتنبر", "أكتوبر", "نونبر", "دجنبر", "يناير", "فبراير", "مارس", "أبريل", "ماي", "يونيو"]

            # استعلام لجلب بيانات الغياب الشهرية
            cur.execute("""
                SELECT الشهر, مجموع_شهري, الغياب_المبرر, "1", "2", "3", "4", "5", ملاحظات
                FROM مسك_الغياب_الأسبوعي
                WHERE رمز_التلميذ = ?
                ORDER BY CASE الشهر
                    WHEN 'شتنبر' THEN 1
                    WHEN 'أكتوبر' THEN 2
                    WHEN 'نونبر' THEN 3
                    WHEN 'دجنبر' THEN 4
                    WHEN 'يناير' THEN 5
                    WHEN 'فبراير' THEN 6
                    WHEN 'مارس' THEN 7
                    WHEN 'أبريل' THEN 8
                    WHEN 'ماي' THEN 9
                    WHEN 'يونيو' THEN 10
                    ELSE 11
                END
            """, (student_code,))

            absence_data = cur.fetchall()

            # التحقق من وجود بيانات
            if not absence_data:
                elements.append(Paragraph(reshape_ar("لا توجد بيانات غياب لهذا التلميذ"), ParagraphStyle('no_data', fontName='Arabic', fontSize=12, alignment=1)))
            else:
                # الحصول على تواريخ بداية الأسابيع
                week_dates = {}
                try:
                    # استعلام للحصول على تواريخ بداية الأسابيع لكل شهر
                    for month in set(row[0] for row in absence_data):
                        cur.execute("""
                            SELECT "1", "2", "3", "4", "5", "بداية_الشهر"
                            FROM "مسك_الغياب_الأسبوعي"
                            WHERE "الشهر" = ?
                            LIMIT 1
                        """, (month,))

                        week_dates_row = cur.fetchone()
                        if week_dates_row and len(week_dates_row) >= 6:
                            month_start_date = week_dates_row[5]
                            if month_start_date:
                                week_dates[month] = {}
                                week_dates[month]['بداية_الشهر'] = month_start_date

                                # حساب تواريخ بداية الأسابيع
                                for i in range(1, 6):
                                    week_start = calculate_week_start_date(month_start_date, i)
                                    if week_start:
                                        week_dates[month][f'الأسبوع_{i}'] = week_start
                except Exception as e:
                    print(f"خطأ في استرجاع تواريخ بداية الأسابيع: {str(e)}")

                # إنشاء عناوين الأسابيع بدون تواريخ
                week_headers = [f"الأسبوع {i}" for i in range(1, 6)]

                # إنشاء جدول البيانات
                headers = [
                    reshape_ar("الشهر"),
                    reshape_ar(week_headers[0]),
                    reshape_ar(week_headers[1]),
                    reshape_ar(week_headers[2]),
                    reshape_ar(week_headers[3]),
                    reshape_ar(week_headers[4]),
                    reshape_ar("المجموع"),
                    reshape_ar("الغياب المبرر"),
                    reshape_ar("ملاحظات")
                ]

                table_data = [headers]

                # إضافة بيانات الغياب
                for row in absence_data:
                    month = row[0]
                    total = row[1] if row[1] is not None else 0
                    justified = row[2] if row[2] is not None else 0
                    week1 = row[3] if row[3] is not None else 0
                    week2 = row[4] if row[4] is not None else 0
                    week3 = row[5] if row[5] is not None else 0
                    week4 = row[6] if row[6] is not None else 0
                    week5 = row[7] if row[7] is not None else 0
                    notes = row[8] if row[8] is not None else ""

                    # تحديد الأسدس بناءً على الشهر (نحتفظ به للتقسيم لاحقًا)
                    semester = "الأول" if month in ["شتنبر", "أكتوبر", "نونبر", "دجنبر", "يناير"] else "الثاني"

                    # إضافة البيانات بدون عمود الأسدس
                    row_data = [
                        reshape_ar(month),
                        reshape_ar(str(week1)),
                        reshape_ar(str(week2)),
                        reshape_ar(str(week3)),
                        reshape_ar(str(week4)),
                        reshape_ar(str(week5)),
                        reshape_ar(str(total)),
                        reshape_ar(str(justified)),
                        reshape_ar(notes)
                    ]

                    # إضافة البيانات مع معلومات الأسدس كخاصية إضافية
                    row_data.append(semester)  # نضيف الأسدس كعنصر إضافي لاستخدامه في التقسيم
                    table_data.append(row_data)

                # تقسيم البيانات إلى أسدسين باستخدام العنصر الإضافي (الأسدس)
                first_semester_data = [row[:-1] for row in table_data[1:] if row[-1] == "الأول"]  # نحذف العنصر الأخير (الأسدس)
                second_semester_data = [row[:-1] for row in table_data[1:] if row[-1] == "الثاني"]  # نحذف العنصر الأخير (الأسدس)

                # إضافة الصف الأول (العناوين) إلى كل جدول إذا كان هناك بيانات
                if first_semester_data:
                    first_semester_data.insert(0, table_data[0])
                if second_semester_data:
                    second_semester_data.insert(0, table_data[0])

                # عكس ترتيب البيانات للعرض من اليمين إلى اليسار
                first_semester_data = [list(reversed(row)) for row in first_semester_data]
                second_semester_data = [list(reversed(row)) for row in second_semester_data]

                # تعيين أبعاد الأعمدة (بعد إزالة عمود الأسدس)
                col_widths = [80, 60, 60, 60, 60, 60, 60, 60, 80]  # عرض كل عمود
                col_widths = list(reversed(col_widths))  # عكس ترتيب الأعمدة

                # إضافة عنوان الأسدس الأول
                if first_semester_data and len(first_semester_data) > 1:
                    elements.append(Paragraph(reshape_ar("الأسدس الأول:"), ParagraphStyle('semester_title', fontName='Arabic', fontSize=14, alignment=1, textColor=colors.darkblue)))
                    elements.append(Spacer(1, 0.3*cm))

                    # إنشاء جدول الأسدس الأول
                    first_semester_table = Table(first_semester_data, colWidths=col_widths, repeatRows=1, hAlign='RIGHT')
                    first_semester_table.setStyle(TableStyle([
                        ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                        ('FONTNAME', (0,0), (-1,-1), 'Arabic'),
                        ('FONTSIZE', (0,0), (-1,-1), 10),
                        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
                        ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                    ]))

                    elements.append(first_semester_table)

                    # إضافة إحصاءات الغياب للأسدس الأول
                    # بعد عكس ترتيب البيانات، تغيرت مواقع الأعمدة
                    # المجموع الشهري في العمود 2 (بعد العكس)
                    first_semester_total = sum(int(row[2]) for row in first_semester_data[1:] if row[2].strip())
                    # الغياب المبرر في العمود 1 (بعد العكس)
                    first_semester_justified = sum(int(row[1]) for row in first_semester_data[1:] if row[1].strip())
                    # التأكد من أن الغياب غير المبرر لا يكون سالباً
                    first_semester_unjustified = max(0, first_semester_total - first_semester_justified)

                    # إنشاء جدول إحصاءات الغياب للأسدس الأول
                    stats_data = [
                        [reshape_ar("نوع الغياب"), reshape_ar("العدد"), reshape_ar("النسبة المئوية")],
                        [reshape_ar("الغياب الإجمالي"), reshape_ar(str(first_semester_total)), reshape_ar(f"{100}%")],
                        [reshape_ar("الغياب المبرر"), reshape_ar(str(first_semester_justified)),
                         reshape_ar(f"{round(first_semester_justified/first_semester_total*100 if first_semester_total else 0)}%")],
                        [reshape_ar("الغياب غير المبرر"), reshape_ar(str(first_semester_unjustified)),
                         reshape_ar(f"{round(first_semester_unjustified/first_semester_total*100 if first_semester_total else 0)}%")]
                    ]

                    stats_table = Table(stats_data, colWidths=[150, 80, 80], hAlign='RIGHT')
                    stats_table.setStyle(TableStyle([
                        ('GRID', (0,0), (-1,-1), 0.5, colors.grey),
                        ('FONTNAME', (0,0), (-1,-1), 'Arabic'),
                        ('FONTSIZE', (0,0), (-1,-1), 10),
                        ('BACKGROUND', (0,0), (-1,0), colors.lightblue),
                        ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                    ]))

                    elements.append(Paragraph(reshape_ar("إحصاءات الغياب للأسدس الأول:"),
                                             ParagraphStyle('stats_title', fontName='Arabic', fontSize=12, alignment=2)))
                    elements.append(Spacer(1, 0.2*cm))
                    elements.append(stats_table)

                    elements.append(Spacer(1, 0.5*cm))

                # إضافة عنوان الأسدس الثاني
                if second_semester_data and len(second_semester_data) > 1:
                    elements.append(Paragraph(reshape_ar("الأسدس الثاني:"), ParagraphStyle('semester_title', fontName='Arabic', fontSize=14, alignment=1, textColor=colors.darkblue)))
                    elements.append(Spacer(1, 0.3*cm))

                    # إنشاء جدول الأسدس الثاني
                    second_semester_table = Table(second_semester_data, colWidths=col_widths, repeatRows=1, hAlign='RIGHT')
                    second_semester_table.setStyle(TableStyle([
                        ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                        ('FONTNAME', (0,0), (-1,-1), 'Arabic'),
                        ('FONTSIZE', (0,0), (-1,-1), 10),
                        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
                        ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                    ]))

                    elements.append(second_semester_table)

                    # إضافة إحصاءات الغياب للأسدس الثاني
                    # بعد عكس ترتيب البيانات، تغيرت مواقع الأعمدة
                    # المجموع الشهري في العمود 2 (بعد العكس)
                    second_semester_total = sum(int(row[2]) for row in second_semester_data[1:] if row[2].strip())
                    # الغياب المبرر في العمود 1 (بعد العكس)
                    second_semester_justified = sum(int(row[1]) for row in second_semester_data[1:] if row[1].strip())
                    # التأكد من أن الغياب غير المبرر لا يكون سالباً
                    second_semester_unjustified = max(0, second_semester_total - second_semester_justified)

                    # إنشاء جدول إحصاءات الغياب للأسدس الثاني
                    stats_data = [
                        [reshape_ar("نوع الغياب"), reshape_ar("العدد"), reshape_ar("النسبة المئوية")],
                        [reshape_ar("الغياب الإجمالي"), reshape_ar(str(second_semester_total)), reshape_ar(f"{100}%")],
                        [reshape_ar("الغياب المبرر"), reshape_ar(str(second_semester_justified)),
                         reshape_ar(f"{round(second_semester_justified/second_semester_total*100 if second_semester_total else 0)}%")],
                        [reshape_ar("الغياب غير المبرر"), reshape_ar(str(second_semester_unjustified)),
                         reshape_ar(f"{round(second_semester_unjustified/second_semester_total*100 if second_semester_total else 0)}%")]
                    ]

                    stats_table = Table(stats_data, colWidths=[150, 80, 80], hAlign='RIGHT')
                    stats_table.setStyle(TableStyle([
                        ('GRID', (0,0), (-1,-1), 0.5, colors.grey),
                        ('FONTNAME', (0,0), (-1,-1), 'Arabic'),
                        ('FONTSIZE', (0,0), (-1,-1), 10),
                        ('BACKGROUND', (0,0), (-1,0), colors.lightblue),
                        ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                    ]))

                    elements.append(Paragraph(reshape_ar("إحصاءات الغياب للأسدس الثاني:"),
                                             ParagraphStyle('stats_title', fontName='Arabic', fontSize=12, alignment=2)))
                    elements.append(Spacer(1, 0.2*cm))
                    elements.append(stats_table)

                    elements.append(Spacer(1, 0.5*cm))

                # إضافة ملخص إحصائي للغياب
                # جمع إحصائيات الأسدسين
                total_absences = 0
                total_justified = 0

                # إضافة إحصائيات الأسدس الأول إذا كانت متوفرة
                if first_semester_data and len(first_semester_data) > 1:
                    total_absences += first_semester_total
                    total_justified += first_semester_justified

                # إضافة إحصائيات الأسدس الثاني إذا كانت متوفرة
                if second_semester_data and len(second_semester_data) > 1:
                    total_absences += second_semester_total
                    total_justified += second_semester_justified

                # التأكد من أن الغياب غير المبرر لا يكون سالباً
                total_unjustified = max(0, total_absences - total_justified)

                summary_style = ParagraphStyle('summary', fontName='Arabic', fontSize=12, alignment=1)

                summary = f"إجمالي الغياب السنوي: {total_absences} | الغياب المبرر: {total_justified} | الغياب غير المبرر: {total_unjustified}"
                elements.append(Paragraph(reshape_ar(summary), summary_style))

                elements.append(Spacer(1, 0.5*cm))

                # تم إزالة المبيان الإحصائي بناءً على طلب المستخدم

                # إضافة تاريخ إنشاء التقرير
                elements.append(Spacer(1, 0.5*cm))
                current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
                date_text = f"تاريخ إنشاء التقرير: {current_date}"
                elements.append(Paragraph(reshape_ar(date_text), ParagraphStyle('date', fontName='Arabic', fontSize=10, alignment=1)))

            # بناء ملف PDF
            doc.build(elements)

            # فتح الملف
            os.startfile(doc.filename)

            # عرض رسالة النجاح
            QMessageBox.information(self, "نجاح", f"تم إنشاء تقرير الغياب السنوي للتلميذ {student_name} بنجاح")

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء تقرير الغياب للتلميذ:\n{str(e)}")
            import traceback
            print(traceback.format_exc())

if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    window = YearlyAbsenceSummaryWindow()
    window.show()
    sys.exit(app.exec_())
