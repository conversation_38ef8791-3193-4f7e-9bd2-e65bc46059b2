#!/usr/bin/env python3
# -*- coding: utf-8 -*-
#  """نافذة الاستعلام المركزي للربط بين السجل العام واللوائح"""

import os
import sqlite3
import sys
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtSql import *

class CentralQueryWindow(QMainWindow):
    """نافذة الاستعلام المركزي للربط بين السجل العام واللوائح"""
    
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.current_school_year = None
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
        # تنفيذ الاستعلام المركزي
        self.execute_central_query()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔍 الاستعلام المركزي - ربط السجل العام باللوائح")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين حجم النافذة
        screen = QApplication.desktop().screenGeometry()
        width = int(screen.width() * 0.95)
        height = int(screen.height() * 0.9)
        self.resize(width, height)
        
        # توسيط النافذة
        self.move(
            (screen.width() - width) // 2,
            (screen.height() - height) // 2
        )
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # شريط الأدوات العلوي
        self.create_toolbar(main_layout)
        
        # منطقة المعلومات
        self.create_info_section(main_layout)
        
        # جدول البيانات
        self.create_data_table(main_layout)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #2c3e50;
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 8px;
                border-top: 3px solid #3498db;
            }
        """)
        self.status_bar.showMessage("جاهز للاستعلام...")

    def create_toolbar(self, layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setMaximumHeight(100)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2c3e50,
                    stop: 1 #3498db
                );
                border-radius: 12px;
                padding: 15px;
                margin: 5px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(20, 15, 20, 15)
        toolbar_layout.setSpacing(20)
        
        # عنوان النافذة
        title_label = QLabel("🔍 الاستعلام المركزي")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setStyleSheet("color: white; margin: 0;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # زر تحديث البيانات
        self.refresh_button = QPushButton("🔄 تحديث البيانات")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60,
                    stop: 1 #219a52
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2ecc71,
                    stop: 1 #27ae60
                );
                transform: translateY(-2px);
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(self.refresh_button)
        
        # زر تصدير البيانات
        self.export_button = QPushButton("📊 تصدير Excel")
        self.export_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e67e22,
                    stop: 1 #d35400
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f39c12,
                    stop: 1 #e67e22
                );
                transform: translateY(-2px);
            }
        """)
        self.export_button.clicked.connect(self.export_to_excel)
        toolbar_layout.addWidget(self.export_button)
        
        # زر طباعة
        self.print_button = QPushButton("🖨️ طباعة")
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #8e44ad,
                    stop: 1 #7d3c98
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #9b59b6,
                    stop: 1 #8e44ad
                );
                transform: translateY(-2px);
            }
        """)
        self.print_button.clicked.connect(self.print_data)
        toolbar_layout.addWidget(self.print_button)
        
        layout.addWidget(toolbar_frame)

    def create_info_section(self, layout):
        """إنشاء منطقة المعلومات"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.StyledPanel)
        info_frame.setMaximumHeight(80)
        info_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #ecf0f1,
                    stop: 1 #bdc3c7
                );
                border-radius: 10px;
                border: 2px solid #95a5a6;
                margin: 5px;
            }
        """)
        
        info_layout = QHBoxLayout(info_frame)
        info_layout.setContentsMargins(20, 15, 20, 15)
        info_layout.setSpacing(30)
        
        # معلومات السنة الدراسية
        self.school_year_label = QLabel("السنة الدراسية: جاري التحميل...")
        self.school_year_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.school_year_label.setStyleSheet("color: #2c3e50;")
        info_layout.addWidget(self.school_year_label)
        
        # عدد السجلات
        self.records_count_label = QLabel("عدد السجلات: 0")
        self.records_count_label.setFont(QFont("Calibri", 14, QFont.Bold))
        self.records_count_label.setStyleSheet("color: #27ae60;")
        info_layout.addWidget(self.records_count_label)
        
        # تاريخ آخر تحديث
        self.last_update_label = QLabel(f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.last_update_label.setFont(QFont("Calibri", 12))
        self.last_update_label.setStyleSheet("color: #7f8c8d;")
        info_layout.addWidget(self.last_update_label)
        
        info_layout.addStretch()
        
        layout.addWidget(info_frame)

    def create_data_table(self, layout):
        """إنشاء جدول البيانات"""
        self.table_widget = QTableWidget()
        self.table_widget.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد أعمدة الجدول
        columns = [
            "الرمز",
            "الاسم والنسب", 
            "النوع",
            "تاريخ الازدياد",
            "مكان الازدياد",
            "السنة الدراسية",
            "القسم",
            "المستوى", 
            "الرقم الترتيبي"
        ]
        
        self.table_widget.setColumnCount(len(columns))
        self.table_widget.setHorizontalHeaderLabels(columns)
        
        # تنسيق الجدول
        self.table_widget.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #3498db;
                border-radius: 10px;
                gridline-color: #bdc3c7;
                font-family: 'Calibri';
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #e8f4fd;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 1 #2980b9
                );
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 12px;
                border: none;
                border-right: 1px solid #2980b9;
            }
        """)
        
        # إعداد خصائص الجدول
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_widget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table_widget.setSortingEnabled(True)
        self.table_widget.horizontalHeader().setStretchLastSection(True)
        
        # تعديل عرض الأعمدة
        header = self.table_widget.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # الرمز
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # الاسم والنسب
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # تاريخ الازدياد
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # مكان الازدياد
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # السنة الدراسية
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # القسم
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # المستوى
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # الرقم الترتيبي
        
        layout.addWidget(self.table_widget)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            print("🔍 تحميل البيانات الأولية...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحميل السنة الدراسية الحالية
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            
            if result:
                self.current_school_year = str(result[0])
                print(f"✅ تم تحميل السنة الدراسية: {self.current_school_year}")
            else:
                self.current_school_year = "2024/2025"
                print(f"⚠️ لم يتم العثور على السنة الدراسية، استخدام القيمة الافتراضية: {self.current_school_year}")
            
            # تحديث واجهة المستخدم
            self.school_year_label.setText(f"السنة الدراسية: {self.current_school_year}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات الأولية: {e}")
            self.current_school_year = "2024/2025"
            self.school_year_label.setText(f"السنة الدراسية: {self.current_school_year} (افتراضي)")

    def execute_central_query(self):
        """تنفيذ الاستعلام المركزي"""
        try:
            print("🔍 بدء تنفيذ الاستعلام المركزي...")
            self.status_bar.showMessage("جاري تنفيذ الاستعلام المركزي...")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تشخيص شامل لقاعدة البيانات
            print("🔧 تشخيص قاعدة البيانات...")
            
            # فحص الجداول المتاحة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            available_tables = [table[0] for table in tables]
            print(f"📋 الجداول المتاحة: {available_tables}")
            
            # فحص جدول السجل العام
            if 'السجل_العام' in available_tables:
                cursor.execute("SELECT COUNT(*) FROM السجل_العام")
                general_count = cursor.fetchone()[0]
                print(f"📊 عدد السجلات في السجل العام: {general_count}")
                
                # عرض عينة من البيانات
                cursor.execute("SELECT * FROM السجل_العام LIMIT 3")
                sample_general = cursor.fetchall()
                print(f"🔍 عينة من السجل العام: {sample_general}")
                
                # فحص أعمدة السجل العام
                cursor.execute("PRAGMA table_info(السجل_العام)")
                general_columns = cursor.fetchall()
                print(f"📝 أعمدة السجل العام: {[col[1] for col in general_columns]}")
            else:
                print("❌ جدول السجل العام غير موجود!")
                self.status_bar.showMessage("خطأ: جدول السجل العام غير موجود")
                return
            
            # فحص جدول اللوائح
            if 'اللوائح' in available_tables:
                cursor.execute("SELECT COUNT(*) FROM اللوائح")
                lists_count = cursor.fetchone()[0]
                print(f"📊 عدد السجلات في اللوائح: {lists_count}")
                
                # فحص السنوات الدراسية المتاحة
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح")
                available_years = cursor.fetchall()
                print(f"📅 السنوات الدراسية المتاحة: {[year[0] for year in available_years]}")
                
                # عدد السجلات للسنة الحالية
                cursor.execute("SELECT COUNT(*) FROM اللوائح WHERE السنة_الدراسية = ?", (self.current_school_year,))
                current_year_count = cursor.fetchone()[0]
                print(f"📊 عدد السجلات في اللوائح للسنة {self.current_school_year}: {current_year_count}")
                
                # عرض عينة من البيانات
                cursor.execute("SELECT * FROM اللوائح WHERE السنة_الدراسية = ? LIMIT 3", (self.current_school_year,))
                sample_lists = cursor.fetchall()
                print(f"🔍 عينة من اللوائح للسنة الحالية: {sample_lists}")
                
                # فحص أعمدة اللوائح
                cursor.execute("PRAGMA table_info(اللوائح)")
                lists_columns = cursor.fetchall()
                print(f"📝 أعمدة اللوائح: {[col[1] for col in lists_columns]}")
            else:
                print("❌ جدول اللوائح غير موجود!")
                self.status_bar.showMessage("خطأ: جدول اللوائح غير موجود")
                return
            
            # جرب استعلامات مختلفة
            print("🔍 تجربة استعلامات مختلفة...")
            
            # الاستعلام الأول: الاستعلام الأصلي
            central_query_1 = """
            SELECT 
                s.الرمز,
                s.الاسم_والنسب,
                s.النوع,
                s.تاريخ_الازدياد,
                s.مكان_الازدياد,
                l.السنة_الدراسية,
                l.القسم,
                l.المستوى,
                l.رت as الرقم_الترتيبي
            FROM السجل_العام s
            LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
            WHERE l.السنة_الدراسية = ?
            ORDER BY l.المستوى, l.القسم, l.رت
            """
            
            print(f"📋 تنفيذ الاستعلام الأول للسنة الدراسية: {self.current_school_year}")
            cursor.execute(central_query_1, (self.current_school_year,))
            results_1 = cursor.fetchall()
            print(f"📊 الاستعلام الأول: {len(results_1)} سجل")
            
            # إذا لم نحصل على نتائج، جرب استعلام مختلف
            if len(results_1) == 0:
                print("⚠️ الاستعلام الأول لم يعطي نتائج، تجربة استعلام بديل...")
                
                # الاستعلام الثاني: بدون شرط السنة الدراسية
                central_query_2 = """
                SELECT 
                    s.الرمز,
                    s.الاسم_والنسب,
                    s.النوع,
                    s.تاريخ_الازدياد,
                    s.مكان_الازدياد,
                    l.السنة_الدراسية,
                    l.القسم,
                    l.المستوى,
                    l.رت as الرقم_الترتيبي
                FROM السجل_العام s
                LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE l.السنة_الدراسية IS NOT NULL
                ORDER BY l.المستوى, l.القسم, l.رت
                LIMIT 1000
                """
                
                print("📋 تنفيذ الاستعلام الثاني (بدون شرط السنة)")
                cursor.execute(central_query_2)
                results_2 = cursor.fetchall()
                print(f"📊 الاستعلام الثاني: {len(results_2)} سجل")
                
                if len(results_2) > 0:
                    results = results_2
                    print("✅ استخدام نتائج الاستعلام الثاني")
                else:
                    print("⚠️ الاستعلام الثاني لم يعطي نتائج، تجربة استعلام أبسط...")
                    
                    # الاستعلام الثالث: فقط من جدول اللوائح
                    central_query_3 = """
                    SELECT 
                        l.الرمز,
                        'غير محدد' as الاسم_والنسب,
                        'غير محدد' as النوع,
                        'غير محدد' as تاريخ_الازدياد,
                        'غير محدد' as مكان_الازدياد,
                        l.السنة_الدراسية,
                        l.القسم,
                        l.المستوى,
                        l.رت as الرقم_الترتيبي
                    FROM اللوائح l
                    WHERE l.السنة_الدراسية = ?
                    ORDER BY l.المستوى, l.القسم, l.رت
                    LIMIT 100
                    """
                    
                    print(f"📋 تنفيذ الاستعلام الثالث (من اللوائح فقط) للسنة: {self.current_school_year}")
                    cursor.execute(central_query_3, (self.current_school_year,))
                    results_3 = cursor.fetchall()
                    print(f"📊 الاستعلام الثالث: {len(results_3)} سجل")
                    
                    if len(results_3) > 0:
                        results = results_3
                        print("✅ استخدام نتائج الاستعلام الثالث")
                    else:
                        print("❌ جميع الاستعلامات فشلت في إعطاء نتائج")
                        
                        # الاستعلام الرابع: فقط من السجل العام
                        central_query_4 = """
                        SELECT 
                            s.الرمز,
                            s.الاسم_والنسب,
                            s.النوع,
                            s.تاريخ_الازدياد,
                            s.مكان_الازدياد,
                            'غير محدد' as السنة_الدراسية,
                            'غير محدد' as القسم,
                            'غير محدد' as المستوى,
                            'غير محدد' as الرقم_الترتيبي
                        FROM السجل_العام s
                        LIMIT 100
                        """
                        
                        print("📋 تنفيذ الاستعلام الرابع (من السجل العام فقط)")
                        cursor.execute(central_query_4)
                        results_4 = cursor.fetchall()
                        print(f"📊 الاستعلام الرابع: {len(results_4)} سجل")
                        
                        if len(results_4) > 0:
                            results = results_4
                            print("✅ استخدام نتائج الاستعلام الرابع")
                        else:
                            print("❌ لا توجد بيانات في أي من الجداول")
                            self.status_bar.showMessage("لا توجد بيانات للعرض")
                            conn.close()
                            return
            else:
                results = results_1
                print("✅ استخدام نتائج الاستعلام الأول")
            
            print(f"📊 إجمالي النتائج النهائية: {len(results)} سجل")
            
            # مسح الجدول وإضافة البيانات الجديدة
            self.table_widget.setRowCount(0)
            self.table_widget.setRowCount(len(results))
            
            # ملء الجدول بالبيانات
            for row_index, row_data in enumerate(results):
                for col_index, value in enumerate(row_data):
                    # تنسيق القيم
                    display_value = str(value) if value is not None else "غير محدد"
                    
                    # تنسيق خاص لبعض الأعمدة
                    if col_index == 2 and value:  # النوع
                        if str(value).lower() in ['م', 'ذكر', 'male', '1']:
                            display_value = "ذكر"
                        elif str(value).lower() in ['أ', 'أنثى', 'female', '2']:
                            display_value = "أنثى"
                        else:
                            display_value = str(value)
                    elif col_index == 3 and value:  # تاريخ الازدياد
                        display_value = str(value)
                    
                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)
                    
                    # تلوين الصفوف بالتناوب
                    if row_index % 2 == 0:
                        item.setBackground(QColor(248, 249, 250))
                    
                    # تمييز بعض الأعمدة
                    if col_index == 0:  # الرمز
                        item.setBackground(QColor(230, 240, 255))
                        item.setFont(QFont("Calibri", 12, QFont.Bold))
                    elif col_index == 1:  # الاسم والنسب
                        item.setFont(QFont("Calibri", 12, QFont.Bold))
                    
                    self.table_widget.setItem(row_index, col_index, item)
            
            # تحديث معلومات الواجهة
            self.records_count_label.setText(f"عدد السجلات: {len(results)}")
            self.last_update_label.setText(f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            conn.close()
            
            if len(results) > 0:
                self.status_bar.showMessage(f"تم تحميل {len(results)} سجل بنجاح ✅")
                print("✅ تم تنفيذ الاستعلام المركزي بنجاح")
            else:
                self.status_bar.showMessage("تم تنفيذ الاستعلام لكن لا توجد نتائج ⚠️")
                print("⚠️ تم تنفيذ الاستعلام لكن لا توجد نتائج")
            
        except Exception as e:
            print(f"❌ خطأ في تنفيذ الاستعلام المركزي: {e}")
            import traceback
            traceback.print_exc()
            self.status_bar.showMessage(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تنفيذ الاستعلام المركزي:\n{str(e)}")

    def refresh_data(self):
        """تحديث البيانات"""
        print("🔄 تحديث البيانات...")
        self.load_initial_data()
        self.execute_central_query()

    def export_to_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill
            
            print("📊 بدء تصدير البيانات إلى Excel...")
            
            # جمع البيانات من الجدول
            data = []
            headers = []
            
            # الحصول على رؤوس الأعمدة
            for col in range(self.table_widget.columnCount()):
                headers.append(self.table_widget.horizontalHeaderItem(col).text())
            
            # الحصول على البيانات
            for row in range(self.table_widget.rowCount()):
                row_data = []
                for col in range(self.table_widget.columnCount()):
                    item = self.table_widget.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)
            
            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=headers)
            
            # اختيار مسار الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", 
                f"الاستعلام_المركزي_{self.current_school_year}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx)"
            )
            
            if file_path:
                # حفظ الملف مع تنسيق
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='الاستعلام المركزي', index=False)
                    
                    # تنسيق الملف
                    worksheet = writer.sheets['الاستعلام المركزي']
                    
                    # تنسيق رؤوس الأعمدة
                    for col_num, header in enumerate(headers, 1):
                        cell = worksheet.cell(row=1, column=col_num)
                        cell.font = Font(bold=True, color="FFFFFF")
                        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                        cell.alignment = Alignment(horizontal="center")
                    
                    # تعديل عرض الأعمدة
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
                
                QMessageBox.information(self, "نجح التصدير", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
                print(f"✅ تم تصدير البيانات إلى: {file_path}")
                
        except ImportError:
            QMessageBox.warning(self, "خطأ", "يتطلب تثبيت مكتبات pandas و openpyxl لتصدير Excel")
        except Exception as e:
            print(f"❌ خطأ في تصدير Excel: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات:\n{str(e)}")

    def print_data(self):
        """طباعة البيانات"""
        try:
            print("🖨️ بدء عملية الطباعة...")
            
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Landscape)
            
            print_dialog = QPrintDialog(printer, self)
            print_dialog.setWindowTitle("طباعة الاستعلام المركزي")
            
            if print_dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)
                
                # تنسيق الطباعة
                font = QFont("Arial", 10)
                painter.setFont(font)
                
                # طباعة العنوان
                title_font = QFont("Arial", 16, QFont.Bold)
                painter.setFont(title_font)
                painter.drawText(100, 100, f"الاستعلام المركزي - السنة الدراسية: {self.current_school_year}")
                
                # طباعة الجدول (مبسط)
                painter.setFont(font)
                y_position = 200
                
                # طباعة رؤوس الأعمدة
                x_positions = [50, 200, 350, 500, 650, 800, 950, 1100, 1250]
                headers = ["الرمز", "الاسم", "النوع", "تاريخ الازدياد", "مكان الازدياد", "السنة", "القسم", "المستوى", "ر.ت"]
                
                for i, header in enumerate(headers):
                    if i < len(x_positions):
                        painter.drawText(x_positions[i], y_position, header)
                
                y_position += 30
                
                # طباعة البيانات (أول 50 سجل)
                max_rows = min(50, self.table_widget.rowCount())
                for row in range(max_rows):
                    for col in range(min(len(x_positions), self.table_widget.columnCount())):
                        item = self.table_widget.item(row, col)
                        text = item.text()[:15] if item and item.text() else ""  # تقصير النص
                        painter.drawText(x_positions[col], y_position, text)
                    y_position += 25
                    
                    if y_position > 2800:  # نهاية الصفحة
                        break
                
                painter.end()
                QMessageBox.information(self, "تمت الطباعة", "تم إرسال البيانات إلى الطابعة بنجاح")
                print("✅ تمت الطباعة بنجاح")
                
        except Exception as e:
            print(f"❌ خطأ في الطباعة: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في الطباعة:\n{str(e)}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        print("🔚 إغلاق نافذة الاستعلام المركزي")
        event.accept()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق نمط عام للتطبيق
    app.setStyleSheet("""
        QApplication {
            font-family: 'Calibri';
            font-size: 12px;
        }
    """)
    
    window = CentralQueryWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
