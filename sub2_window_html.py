"""
نافذة بيانات المؤسسة - Python + HTML
تم إعادة تصميم النافذة لتستخدم منهجية Python + HTML الحديثة

الميزات:
- واجهة HTML جميلة ومتجاوبة
- تكامل كامل مع قاعدة البيانات
- إدارة الشعار والبيانات
- تصميم عصري ومرن
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFileDialog, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon
import base64


class InstitutionEngine(QObject):
    """محرك إدارة بيانات المؤسسة"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    dataUpdated = pyqtSignal(str)  # institution data JSON
    logoUpdated = pyqtSignal(str)  # logo base64 or path

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.setup_database()

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    def setup_database(self):
        """إعداد قاعدة البيانات وإنشاء الجداول المطلوبة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول بيانات المؤسسة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    الأكاديمية TEXT,
                    المديرية TEXT,
                    الجماعة TEXT,
                    المؤسسة TEXT,
                    السنة_الدراسية TEXT,
                    البلدة TEXT,
                    المدير TEXT,
                    الحارس_العام TEXT,
                    السلك TEXT,
                    رقم_الحراسة TEXT,
                    رقم_التسجيل TEXT,
                    الأسدس TEXT,
                    الشعار TEXT,
                    ImagePath1 TEXT,
                    تاريخ_التحديث TEXT
                )
            ''')
            
            # إنشاء جدول البنية التربوية إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS البنية_التربوية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    السنة_الدراسية TEXT
                )
            ''')

            # التحقق من وجود عمود ImagePath1 وإضافته إذا لم يكن موجوداً
            cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'ImagePath1' not in columns:
                cursor.execute("ALTER TABLE بيانات_المؤسسة ADD COLUMN ImagePath1 TEXT")
                self.emit_log("✅ تم إضافة عمود ImagePath1", "success")
                
            if 'تاريخ_التحديث' not in columns:
                cursor.execute("ALTER TABLE بيانات_المؤسسة ADD COLUMN تاريخ_التحديث TEXT")
                self.emit_log("✅ تم إضافة عمود تاريخ_التحديث", "success")

            conn.commit()
            conn.close()
            self.emit_log("✅ تم إعداد قاعدة البيانات بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getInstitutionData(self):
        """الحصول على بيانات المؤسسة - مبسط مثل الملف الأصلي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على بيانات المؤسسة (مثل الملف الأصلي)
            cursor.execute("SELECT * FROM بيانات_المؤسسة WHERE rowid=1")
            row = cursor.fetchone()
            
            if row:
                # الحصول على أسماء الأعمدة
                cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                columns = [info[1] for info in cursor.fetchall()]
                
                # تحويل البيانات إلى قاموس
                data = dict(zip(columns, row))
                self.emit_log("✅ تم تحميل بيانات المؤسسة بنجاح", "success")
            else:
                # بيانات افتراضية
                data = {
                    "الأكاديمية": "",
                    "المديرية": "",
                    "الجماعة": "",
                    "المؤسسة": "",
                    "السنة_الدراسية": "",
                    "البلدة": "",
                    "المدير": "من مدير",
                    "الحارس_العام": "من حارس عام",
                    "السلك": "التعليم الابتدائي",
                    "رقم_الحراسة": "حراسة رقم 1",
                    "رقم_التسجيل": "",
                    "الأسدس": "الأول",
                    "الشعار": ""
                }
                self.emit_log("⚠️ لا توجد بيانات محفوظة، تم تحميل البيانات الافتراضية", "info")
            
            conn.close()
            return json.dumps(data, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"خطأ في جلب بيانات المؤسسة: {str(e)}"
            self.emit_log(f"❌ {error_msg}", "error")
            return json.dumps({"error": error_msg}, ensure_ascii=False)

    @pyqtSlot(result=str)
    def getAcademicYears(self):
        """الحصول على السنوات الدراسية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جلب السنوات الدراسية من جدول البنية التربوية
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية ORDER BY السنة_الدراسية DESC")
            years = [row[0] for row in cursor.fetchall() if row[0]]
            
            # إذا لم توجد سنوات، جلب من جدول اللوائح
            if not years:
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC")
                years = [row[0] for row in cursor.fetchall() if row[0]]
            
            # إضافة سنوات افتراضية إذا لم توجد
            if not years:
                years = ["2024-2025", "2023-2024", "2022-2023"]
            
            conn.close()
            return json.dumps(years, ensure_ascii=False)            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب السنوات الدراسية: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)
    
    @pyqtSlot(str)
    def saveInstitutionData(self, data_json):
        """حفظ بيانات المؤسسة مع التحقق من رقم التسجيل - نفس منطق الملف الأصلي"""
        try:
            data = json.loads(data_json)
            self.emit_log("🔄 جاري حفظ بيانات المؤسسة...", "info")
            
            # التحقق من اختيار السنة الدراسية قبل الحفظ
            academic_year = data.get("السنة_الدراسية", "").strip()
            if not academic_year or academic_year == "اختر السنة الدراسية" or academic_year == "":
                error_msg = "يرجى تحديد السنة الدراسية من القائمة المنسدلة قبل الحفظ"
                self.emit_log(f"⚠️ {error_msg}", "error")
                self.dataUpdated.emit(f"error:{error_msg}")
                return
            
            # التحقق من وجود الشعار قبل الحفظ
            logo_check = self.check_logo_exists()
            if not logo_check:
                error_msg = "يرجى تحميل شعار المؤسسة أولاً قبل حفظ البيانات"
                self.emit_log(f"🖼️ {error_msg}", "error")
                self.dataUpdated.emit(f"error:{error_msg}")
                return
            else:
                self.emit_log("✅ تم التحقق من وجود الشعار", "success")
            
            # التحقق من صحة رقم التسجيل قبل الحفظ (مثل الملف الأصلي)
            registration_number = data.get("رقم_التسجيل", "").strip()
            if registration_number:
                # التحقق من رقم التسجيل باستخدام رمز المؤسسة
                verification_result = json.loads(self.verifyActivationCode(registration_number))
                
                if not verification_result.get("valid", False):
                    error_msg = verification_result.get("message", "رقم التسجيل غير صحيح")
                    self.emit_log(f"❌ {error_msg}", "error")
                    self.dataUpdated.emit(f"error:{error_msg}")
                    return
                else:
                    self.emit_log("✅ تم التحقق من رقم التسجيل بنجاح", "success")
            
            # تحويل أسماء الحقول إلى أسماء الأعمدة (مثل الملف الأصلي)
            column_mapping = {
                "الأكاديمية": "الأكاديمية",
                "المديرية": "المديرية",
                "الجماعة": "الجماعة",
                "المؤسسة": "المؤسسة",
                "السنة_الدراسية": "السنة_الدراسية",
                "البلدة": "البلدة",
                "المدير": "المدير",
                "الحارس_العام": "الحارس_العام",
                "السلك": "السلك",
                "رقم_الحراسة": "رقم_الحراسة",
                "رقم_التسجيل": "رقم_التسجيل",
                "الأسدس": "الأسدس"
            }

            # إعداد بيانات الاستعلام
            update_columns = []
            update_values = []

            # بناء قائمة الأعمدة والقيم للتحديث
            for field, value in data.items():
                if field in column_mapping:
                    update_columns.append(f"{column_mapping[field]} = ?")
                    update_values.append(value)

            # التحقق من وجود بيانات للتحديث
            if not update_columns:
                self.emit_log("⚠️ لا توجد بيانات للتحديث!", "error")
                self.dataUpdated.emit("error:لا توجد بيانات للتحديث")
                return
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود سجل (مثل الملف الأصلي)
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            count = cursor.fetchone()[0]
            
            if count == 0:
                # إذا كان الجدول فارغاً، نقوم بإنشاء سجل جديد أولاً
                cursor.execute("INSERT INTO بيانات_المؤسسة DEFAULT VALUES")
                conn.commit()
                self.emit_log("✅ تم إنشاء سجل جديد", "success")
            
            # بناء استعلام التحديث (مثل الملف الأصلي بالضبط)
            update_query = f"UPDATE بيانات_المؤسسة SET {', '.join(update_columns)} WHERE rowid=1"
            
            # تنفيذ الاستعلام
            cursor.execute(update_query, update_values)
            conn.commit()
            conn.close()
            
            # إرسال إشارة النجاح إلى JavaScript
            self.dataUpdated.emit("success")
            self.emit_log("✅ تم حفظ بيانات المؤسسة بنجاح", "success")
            
        except json.JSONDecodeError as e:
            error_msg = f"خطأ في تحليل البيانات: {str(e)}"
            self.emit_log(f"❌ {error_msg}", "error")
            self.dataUpdated.emit(f"error:{error_msg}")
            
        except sqlite3.Error as e:
            error_msg = f"خطأ في قاعدة البيانات: {str(e)}"
            self.emit_log(f"❌ {error_msg}", "error")
            self.dataUpdated.emit(f"error:{error_msg}")
            
        except Exception as e:
            error_msg = f"خطأ غير متوقع: {str(e)}"
            self.emit_log(f"❌ {error_msg}", "error")
            self.dataUpdated.emit(f"error:{error_msg}")

    @pyqtSlot()
    def uploadLogo(self):
        """رفع شعار المؤسسة - مبسط مثل الملف الأصلي"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QWidget
            
            # إنشاء widget مؤقت للحوار
            parent_widget = QWidget()
            
            file_path, _ = QFileDialog.getOpenFileName(
                parent_widget,
                "اختر صورة الشعار",
                "",
                "Images (*.png *.jpg *.jpeg)"
            )
            
            if not file_path:
                self.emit_log("⚠️ لم يتم اختيار أي صورة", "info")
                return
                
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                self.emit_log("❌ ملف الشعار غير موجود", "error")
                return
                
            self.emit_log(f"📁 تم اختيار الملف: {os.path.basename(file_path)}", "info")
            
            try:
                # حفظ مسار الملف في قاعدة البيانات (مثل الملف الأصلي بالضبط)
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # التحقق من وجود سجل أولاً
                cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
                count = cursor.fetchone()[0]
                
                if count == 0:
                    # إذا لم يوجد سجل، إنشاء سجل جديد
                    cursor.execute("INSERT INTO بيانات_المؤسسة DEFAULT VALUES")
                    conn.commit()
                
                # تحديث مسار الشعار (نفس طريقة الملف الأصلي)
                cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath1=? WHERE rowid=1", (file_path,))
                conn.commit()
                conn.close()
                
                # قراءة الملف وتحويله إلى base64 للعرض فقط
                with open(file_path, 'rb') as image_file:
                    image_data = image_file.read()
                    base64_string = base64.b64encode(image_data).decode('utf-8')
                    
                    # إرسال الشعار للواجهة
                    self.logoUpdated.emit(base64_string)
                    
                self.emit_log("✅ تم حفظ الشعار بنجاح", "success")
                    
            except Exception as e:
                self.emit_log(f"❌ حدث خطأ أثناء حفظ الشعار: {str(e)}", "error")
                
        except Exception as e:
            self.emit_log(f"❌ خطأ في رفع الشعار: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getLogo(self):
        """الحصول على شعار المؤسسة - مبسط مثل الملف الأصلي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة WHERE rowid=1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0] and os.path.exists(result[0]):
                logo_path = result[0]
                self.emit_log(f"🔍 تم العثور على الشعار: {os.path.basename(logo_path)}", "info")
                
                try:
                    # قراءة الملف وتحويله إلى base64 للعرض
                    with open(logo_path, 'rb') as image_file:
                        image_data = image_file.read()
                        base64_logo = base64.b64encode(image_data).decode('utf-8')
                        self.emit_log("✅ تم تحميل الشعار بنجاح", "success")
                        return base64_logo
                        
                except Exception as e:
                    self.emit_log(f"❌ خطأ في قراءة ملف الشعار: {str(e)}", "error")
                    return ""
            else:
                if result and result[0]:
                    self.emit_log(f"❌ ملف الشعار غير موجود: {result[0]}", "error")
                    # إزالة المسار الخاطئ (مثل الملف الأصلي)
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath1=NULL WHERE rowid=1")
                        conn.commit()
                        conn.close()
                        self.emit_log("🗑️ تم مسح مسار الشعار الخاطئ", "info")
                    except Exception:
                        pass
                else:
                    self.emit_log("⚠️ لا يوجد شعار محفوظ", "info")
                return ""
                
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب الشعار: {str(e)}", "error")
            return ""

    @pyqtSlot(result=str)
    def getLogoPath(self):
        """الحصول على مسار الشعار المحفوظ"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة WHERE ImagePath1 IS NOT NULL AND ImagePath1 != '' LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            else:
                return "لا يوجد مسار محفوظ في عمود ImagePath1"

        except Exception as e:
            return f"خطأ: {str(e)}"

    def check_logo_exists(self):
        """التحقق من وجود الشعار في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود مسار الشعار في قاعدة البيانات
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة WHERE rowid=1 AND ImagePath1 IS NOT NULL AND ImagePath1 != ''")
            result = cursor.fetchone()
            conn.close()
            
            if result and result[0]:
                logo_path = result[0]
                # التحقق من وجود الملف فعلياً على النظام
                if os.path.exists(logo_path):
                    self.emit_log(f"✅ تم العثور على الشعار: {os.path.basename(logo_path)}", "info")
                    return True
                else:
                    self.emit_log(f"⚠️ مسار الشعار محفوظ في قاعدة البيانات لكن الملف غير موجود: {logo_path}", "warning")
                    # إزالة المسار الخاطئ من قاعدة البيانات
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        cursor.execute("UPDATE بيانات_المؤسسة SET ImagePath1=NULL WHERE rowid=1")
                        conn.commit()
                        conn.close()
                        self.emit_log("🧹 تم مسح مسار الشعار غير الصحيح من قاعدة البيانات", "info")
                    except Exception as cleanup_error:
                        self.emit_log(f"❌ خطأ في تنظيف مسار الشعار: {str(cleanup_error)}", "error")
                    return False
            else:
                self.emit_log("⚠️ لم يتم العثور على شعار محفوظ في قاعدة البيانات", "warning")
                return False
                
        except Exception as e:
            self.emit_log(f"❌ خطأ في التحقق من وجود الشعار: {str(e)}", "error")
            return False

    @pyqtSlot(result=str)
    def getInstitutionCode(self):
        """استخراج رمز المؤسسة الرقمي - نفس منطق الملف الأصلي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # استخراج البيانات من جدول بيانات_المؤسسة (نفس طريقة الملف الأصلي)
            cursor.execute("SELECT الأكاديمية, المديرية, المؤسسة FROM بيانات_المؤسسة WHERE rowid=1")
            data = cursor.fetchone()
            conn.close()

            if data:
                academy, directorate, school = data

                # التحقق من وجود البيانات قبل معالجتها
                academy = academy or "غير متوفر"
                directorate = directorate or "غير متوفر"
                school = school or "غير متوفر"

                # توليد رقم فريد 10 أرقام بناءً على بيانات المؤسسة (نفس خوارزمية الملف الأصلي)
                combined_text = f"{academy}-{directorate}-{school}"

                # حساب قيمة هاش ثابتة باستخدام خوارزمية بسيطة (نفس الملف الأصلي بالضبط)
                hash_value = 0
                for char in combined_text:
                    hash_value = (hash_value * 31 + ord(char)) & 0xFFFFFFFF

                # تأكد من أن الرقم يكون دائمًا 10 أرقام (نفس المنطق)
                numeric_code = hash_value % 10000000000
                if numeric_code < 1000000000:  # إذا كان أقل من 10 أرقام
                    numeric_code += 1000000000  # أضف 1 في أول خانة

                # تنسيق الرقم كنص
                numeric_code_str = f"{numeric_code:010d}"

                # إرجاع البيانات كـ JSON
                result = {
                    "code": numeric_code_str,
                    "academy": academy,
                    "directorate": directorate,
                    "school": school,
                    "tooltip": f"الأكاديمية: {academy}\nالمديرية: {directorate}\nالمؤسسة: {school}"
                }
                
                self.emit_log(f"✅ تم توليد رمز المؤسسة: {numeric_code_str}", "success")
                return json.dumps(result, ensure_ascii=False)
            else:
                # بيانات فارغة
                result = {
                    "code": "غير متوفر",
                    "academy": "",
                    "directorate": "",
                    "school": "",
                    "tooltip": "لم يتم العثور على بيانات المؤسسة"
                }
                self.emit_log("⚠️ لا توجد بيانات مؤسسة لتوليد الرمز", "info")
                return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            error_msg = f"خطأ في استخراج رمز المؤسسة: {str(e)}"
            self.emit_log(f"❌ {error_msg}", "error")
            result = {
                "code": "خطأ",
                "academy": "",
                "directorate": "",
                "school": "",
                "tooltip": error_msg
            }
            return json.dumps(result, ensure_ascii=False)

    @pyqtSlot(str, result=str)
    def verifyActivationCode(self, registration_number):
        """التحقق من كود التفعيل باستخدام المعادلة المحددة - نفس منطق الملف الأصلي"""
        try:
            # إزالة المسافات الزائدة
            registration_number = registration_number.strip()
            
            # التأكد من أن رقم التسجيل عبارة عن رقم
            if not registration_number.isdigit():
                result = {
                    "valid": False,
                    "message": "رقم التسجيل يجب أن يكون رقماً صحيحاً",
                    "code": "INVALID_FORMAT"
                }
                self.emit_log("❌ رقم التسجيل غير صحيح - يجب أن يكون رقماً", "error")
                return json.dumps(result, ensure_ascii=False)

            # الحصول على رمز المؤسسة الحالي
            institution_code_data = json.loads(self.getInstitutionCode())
            school_code = institution_code_data.get("code", "")
            
            # التأكد من أن رمز المؤسسة متوفر وصحيح
            if school_code == "غير متوفر" or school_code == "خطأ" or not school_code.isdigit():
                result = {
                    "valid": False,
                    "message": "لا يمكن التحقق من رقم التسجيل. يرجى ملء بيانات المؤسسة أولاً",
                    "code": "NO_INSTITUTION_CODE"
                }
                self.emit_log("❌ رمز المؤسسة غير متوفر للتحقق", "error")
                return json.dumps(result, ensure_ascii=False)

            # تطبيق المعادلة: (رمز المؤسسة * 98) + (أول 3 أرقام من رمز المؤسسة * 71)
            school_code_int = int(school_code)
            first_three_digits = int(school_code[:3])

            expected_registration = (school_code_int * 98) + (first_three_digits * 71)

            # التحقق مما إذا كان رقم التسجيل المدخل يطابق النتيجة المتوقعة
            is_valid = int(registration_number) == expected_registration
            
            if is_valid:
                result = {
                    "valid": True,
                    "message": "رقم التسجيل صحيح ومتطابق مع رمز المؤسسة",
                    "code": "VALID"
                }
                self.emit_log("✅ تم التحقق من رقم التسجيل بنجاح", "success")
            else:
                result = {
                    "valid": False,
                    "message": "رقم التسجيل غير صحيح",
                    "code": "INVALID_CODE"
                }
                self.emit_log(f"❌ رقم التسجيل غير صحيح", "error")
            
            return json.dumps(result, ensure_ascii=False)

        except ValueError as e:
            result = {
                "valid": False,
                "message": "خطأ في تحويل الأرقام. تأكد من صحة البيانات المدخلة",
                "code": "CONVERSION_ERROR"
            }
            self.emit_log(f"❌ خطأ في تحويل البيانات: {str(e)}", "error")
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            result = {
                "valid": False,
                "message": f"خطأ غير متوقع في التحقق: {str(e)}",
                "code": "UNKNOWN_ERROR"
            }
            self.emit_log(f"❌ خطأ في التحقق من كود التفعيل: {str(e)}", "error")
            return json.dumps(result, ensure_ascii=False)
            
class InstitutionWindow(QMainWindow):
    """نافذة إدارة بيانات المؤسسة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🏢 إدارة بيانات المؤسسة")
        
        # التحقق من وضع النافذة (مدمجة أم مستقلة)
        if parent is not None:
            # وضع النافذة المدمجة - إزالة إعدادات النافذة المستقلة
            from PyQt5.QtCore import Qt
            from PyQt5.QtWidgets import QSizePolicy
            self.setWindowFlags(Qt.Widget)
            self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            print("INFO: تم تكوين نافذة بيانات المؤسسة في الوضع المدمج")
        else:
            # وضع النافذة المستقلة - الإعدادات الأصلية
            from PyQt5.QtCore import Qt
            self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
            # تحديد الحد الأدنى لحجم النافذة
            self.setMinimumSize(1200, 650)
            print("INFO: تم تكوين نافذة بيانات المؤسسة في الوضع المستقل")

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك المؤسسة
        self.institution_engine = InstitutionEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        
        # تسجيل الكائن مباشرة في القناة
        self.channel.registerObject("institutionEngine", self.institution_engine)
        
        # ربط القناة بعد تحميل الصفحة
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # تعيين القناة بعد تحميل الصفحة
        self.web_view.page().setWebChannel(self.channel)

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>إدارة بيانات المؤسسة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow: hidden; /* إخفاء أشرطة التمرير العمودية والأفقية */
        }

        /* إخفاء أشرطة التمرير في جميع العناصر */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }
        
        *::-webkit-scrollbar {
            display: none; /* WebKit */
        }        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
            height: 100vh;
            overflow: hidden; /* إخفاء أشرطة التمرير */
        }.logo-header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }        .logo-header .logo-display {
            width: 300px;
            height: 150px;
            margin: 0;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .logo-buttons {
            display: flex;
            align-items: center;
        }        .main-content {
            display: block;
            margin-bottom: 20px;
        }        .form-section {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-height: calc(100vh - 180px);
            overflow: hidden; /* إخفاء أشرطة التمرير */
        }.form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px 20px;
            margin-bottom: 20px;
        }.form-group {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;
        }        .form-group label {
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #1e3a8a;
            min-width: 120px;
            text-align: right;
            margin: 0;
        }        .form-group input,
        .form-group select {
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            color: #1a1a1a;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            transition: border-color 0.3s ease;
            flex: 1;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }

        /* تنسيق للحقول المطلوبة */
        .form-group label span[style*="color: red"] {
            font-size: 18px;
            font-weight: bold;
        }
        
        .form-group input[required] {
            border-left: 4px solid #e74c3c;
        }
        
        .form-group input[required]:focus {
            border-left: 4px solid #27ae60;
        }        .logo-display {
            width: 100%;
            height: 150px;
            border: 3px dashed #ddd;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            background: #f9f9f9;
            overflow: hidden;
        }

        .logo-display img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 6px;
            transition: transform 0.3s ease;
        }

        .logo-display:hover {
            transform: scale(1.02);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .logo-display:hover img {
            transform: scale(1.05);
        }        .logo-placeholder {
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            color: #1a1a1a;
            text-align: center;
        }.button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }        .btn {
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1a1a1a; /* أبيض غامق */
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            min-width: 160px;
            justify-content: center;
        }        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #1a1a1a; /* أبيض غامق */
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: #1a1a1a; /* أبيض غامق */
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
            color: #1a1a1a; /* أبيض غامق */
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
            color: #1a1a1a; /* أبيض غامق */
        }.btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }@media (max-width: 1024px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }        @media (max-width: 768px) {
            .logo-header {
                flex-direction: column;
                gap: 15px;
            }
              .logo-header .logo-display {
                width: 280px;
                height: 140px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .form-group label {
                min-width: auto;
                text-align: right;
                margin-bottom: 5px;
            }
            
            .button-group {
                grid-template-columns: 1fr;
            }
        }.section-title {
            font-family: 'Calibri', sans-serif;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1e3a8a;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }        .loading {
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            display: none;
            text-align: center;
            color: #667eea;
        }        .loading.show {
            display: block;
        }

        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 15px;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease-out;
        }

        .message-box.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }

        .message-box.info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .message-box.show {
            display: block;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">        <!-- المحتوى الرئيسي -->
        <div class="main-content">            <!-- قسم الشعار في الأعلى -->
            <div class="logo-header">
                <div class="logo-display" id="logoDisplay">
                    <div class="logo-placeholder">
                        🏢<br>
                        انقر لتحميل الشعار
                    </div>
                </div>
            </div>

            <!-- نموذج البيانات -->
            <div class="form-section">
                <h2 class="section-title">📋 بيانات المؤسسة</h2>
                
                <div class="loading" id="loadingIndicator">
                    🔄 جاري تحميل البيانات...
                </div>

                <form id="institutionForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="academy">الأكاديمية:</label>
                            <input type="text" id="academy" name="الأكاديمية" readonly disabled
                                   style="background-color: #f0f8ff; color: #1e3a8a; font-weight: bold; border: 2px solid #4169e1;"
                                   placeholder="سيتم ملؤه تلقائياً" title="هذا الحقل محمي من التعديل">
                        </div>

                        <div class="form-group">
                            <label for="directorate">المديرية:</label>
                            <input type="text" id="directorate" name="المديرية" readonly disabled
                                   style="background-color: #f0f8ff; color: #1e3a8a; font-weight: bold; border: 2px solid #4169e1;"
                                   placeholder="سيتم ملؤه تلقائياً" title="هذا الحقل محمي من التعديل">
                        </div>

                        <div class="form-group">
                            <label for="community">الجماعة:</label>
                            <input type="text" id="community" name="الجماعة" readonly disabled
                                   style="background-color: #f0f8ff; color: #1e3a8a; font-weight: bold; border: 2px solid #4169e1;"
                                   placeholder="سيتم ملؤه تلقائياً" title="هذا الحقل محمي من التعديل">
                        </div>

                        <div class="form-group">
                            <label for="institution">المؤسسة:</label>
                            <input type="text" id="institution" name="المؤسسة" readonly disabled
                                   style="background-color: #f0f8ff; color: #1e3a8a; font-weight: bold; border: 2px solid #4169e1;"
                                   placeholder="سيتم ملؤه تلقائياً" title="هذا الحقل محمي من التعديل">
                        </div>

                        <div class="form-group">
                            <label for="academicYear">السنة الدراسية:</label>
                            <select id="academicYear" name="السنة_الدراسية">
                                <option value="">اختر السنة الدراسية</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="institutionCode">رمز المؤسسة:</label>
                            <input type="text" id="institutionCode" name="رمز_المؤسسة" readonly disabled
                                   style="background-color: #f0f8ff; color: #1e3a8a; font-weight: bold; border: 2px solid #4169e1;"
                                   placeholder="سيتم توليده تلقائياً" title="يتم توليد هذا الرمز تلقائياً من بيانات المؤسسة">
                        </div>

                        <div class="form-group">
                            <label for="city">البلدة: <span style="color: red;">*</span></label>
                            <input type="text" id="city" name="البلدة" placeholder="أدخل اسم البلدة (مطلوب)" required>
                        </div>

                        <div class="form-group">
                            <label for="director">المدير:</label>
                            <select id="director" name="المدير">
                                <option value="من مدير">من مدير</option>
                                <option value="من مديرة">من مديرة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="guard">الحراسة العامة:</label>
                            <select id="guard" name="الحارس_العام">
                                <option value="من حارس عام">من حارس عام</option>
                                <option value="من حارسة عامة">من حارسة عامة</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="level">السلك:</label>
                            <select id="level" name="السلك">
                                <option value="التعليم الابتدائي">التعليم الابتدائي</option>
                                <option value="الثانوي الإعدادي">الثانوي الإعدادي</option>
                                <option value="الثانوي التأهيلي">الثانوي التأهيلي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="guardNumber">رقم الحراسة:</label>
                            <select id="guardNumber" name="رقم_الحراسة">
                                <option value="حراسة رقم 1">حراسة رقم 1</option>
                                <option value="حراسة رقم 2">حراسة رقم 2</option>
                                <option value="حراسة رقم 3">حراسة رقم 3</option>
                                <option value="حراسة رقم 4">حراسة رقم 4</option>
                                <option value="حراسة رقم 5">حراسة رقم 5</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="registrationNumber">رقم التسجيل: <span style="color: red;">*</span></label>
                            <input type="text" id="registrationNumber" name="رقم_التسجيل" placeholder="أدخل رقم التسجيل (مطلوب)" required>
                        </div>

                        <div class="form-group">
                            <label for="semester">الأسدس:</label>
                            <select id="semester" name="الأسدس">
                                <option value="الأول">الأول</option>
                                <option value="الثاني">الثاني</option>
                            </select>
                        </div>
                    </div>                    <div class="button-group">
                        <button type="button" class="btn btn-primary" onclick="uploadLogo()">
                            📤 تحميل الشعار
                        </button>
                        <button type="button" class="btn btn-success" onclick="saveInstitutionData()">
                            💾 حفظ البيانات
                        </button>
                        <button type="button" class="btn btn-info" onclick="loadInstitutionData()">
                            🔄 تحديث البيانات
                        </button>
                        <button type="button" class="btn btn-info" onclick="updateInstitutionCode()">
                            🔢 تحديث رمز المؤسسة
                        </button>
                        <button type="button" class="btn btn-info" onclick="showLogoPath()">
                            📁 مسار الشعار
                        </button>
                    </div></form>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <div class="message-box" id="messageBox"></div>
    </div>

    <script>
        let institutionEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    institutionEngine = channel.objects.institutionEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');                    // ربط الإشارات
                    if (institutionEngine) {
                        institutionEngine.logoUpdated.connect(updateLogoDisplay);
                        institutionEngine.dataUpdated.connect(handleDataUpdate);
                        institutionEngine.logUpdated.connect(function(message, status, timestamp) {
                            console.log(`[${timestamp}] ${status.toUpperCase()}: ${message}`);
                            showMessage(message, status);
                        });

                        // تحميل البيانات الأولية
                        loadInstitutionData();
                        loadAcademicYears();

                        // تأخير تحميل الشعار قليلاً للتأكد من جاهزية النظام
                        setTimeout(() => {
                            loadLogo();
                        }, 500);

                        console.log('✅ تم تهيئة نظام إدارة المؤسسة بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل بيانات المؤسسة
        function loadInstitutionData() {
            if (institutionEngine) {
                showLoading(true);
                showMessage("🔄 جاري تحميل البيانات...", "info");
                
                institutionEngine.getInstitutionData(function(result) {
                    try {
                        let data;
                        if (typeof result === 'string') {
                            data = JSON.parse(result);
                        } else {
                            data = result;
                        }
                        
                        if (data.error) {
                            console.error('خطأ في تحميل البيانات:', data.error);
                            showMessage('❌ خطأ في تحميل البيانات: ' + data.error, 'error');
                            return;
                        }

                        fillInstitutionForm(data);
                        showMessage("✅ تم تحميل البيانات بنجاح", "success");
                        showLoading(false);
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات المؤسسة:', error);
                        showMessage('❌ خطأ في تحليل البيانات', 'error');
                        showLoading(false);
                    }
                });
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // تحميل السنوات الدراسية
        function loadAcademicYears() {
            if (institutionEngine) {
                institutionEngine.getAcademicYears(function(result) {
                    try {
                        let years;
                        if (typeof result === 'string') {
                            years = JSON.parse(result);
                        } else {
                            years = result;
                        }
                        
                        const select = document.getElementById('academicYear');
                        select.innerHTML = '<option value="">اختر السنة الدراسية</option>';
                        
                        years.forEach(year => {
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            select.appendChild(option);
                        });
                    } catch (error) {
                        console.error('خطأ في تحميل السنوات الدراسية:', error);
                    }
                });
            }
        }        // تحميل الشعار
        function loadLogo() {
            if (institutionEngine) {
                console.log("🔄 جاري تحميل الشعار...");
                showMessage("🔄 جاري تحميل الشعار...", "info");
                
                institutionEngine.getLogo(function(logoData) {
                    console.log("📥 تم استلام رد الشعار:", logoData ? `بيانات موجودة (${logoData.length} حرف)` : "لا توجد بيانات");
                    
                    if (logoData && logoData.trim() !== '') {
                        console.log("✅ عرض الشعار في الواجهة");
                        updateLogoDisplay(logoData);
                        showMessage("✅ تم تحميل الشعار بنجاح", "success");
                    } else {
                        console.log("⚠️ لا يوجد شعار للعرض");
                        updateLogoDisplay("");
                        showMessage("⚠️ لا يوجد شعار محفوظ", "info");
                    }
                });
            } else {
                console.log("❌ محرك المؤسسة غير متاح");
                showMessage("❌ النظام غير جاهز بعد", "error");
            }
        }

        // ملء نموذج البيانات
        function fillInstitutionForm(data) {
            // تمكين الحقول المحمية مؤقتاً لتحديث القيم
            const academyField = document.getElementById('academy');
            const directorateField = document.getElementById('directorate');
            const communityField = document.getElementById('community');
            const institutionField = document.getElementById('institution');
            
            academyField.disabled = false;
            directorateField.disabled = false;
            communityField.disabled = false;
            institutionField.disabled = false;
            
            academyField.value = data.الأكاديمية || '';
            directorateField.value = data.المديرية || '';
            communityField.value = data.الجماعة || '';
            institutionField.value = data.المؤسسة || '';
            
            // إعادة تعطيل الحقول المحمية
            academyField.disabled = true;
            directorateField.disabled = true;
            communityField.disabled = true;
            institutionField.disabled = true;
            
            document.getElementById('academicYear').value = data.السنة_الدراسية || '';
            document.getElementById('city').value = data.البلدة || '';
            document.getElementById('director').value = data.المدير || 'من مدير';
            document.getElementById('guard').value = data.الحارس_العام || 'من حارس عام';
            document.getElementById('level').value = data.السلك || 'التعليم الابتدائي';
            document.getElementById('guardNumber').value = data.رقم_الحراسة || 'حراسة رقم 1';
            document.getElementById('registrationNumber').value = data.رقم_التسجيل || '';
            document.getElementById('semester').value = data.الأسدس || 'الأول';
            
            // تحديث رمز المؤسسة
            updateInstitutionCode();
        }

        // تحديث رمز المؤسسة
        function updateInstitutionCode() {
            if (institutionEngine) {
                institutionEngine.getInstitutionCode(function(result) {
                    try {
                        const codeData = JSON.parse(result);
                        const codeInput = document.getElementById('institutionCode');
                        
                        if (codeData.code && codeData.code !== 'غير متوفر' && codeData.code !== 'خطأ') {
                            codeInput.value = codeData.code;
                            codeInput.title = codeData.tooltip || '';
                            codeInput.style.color = '#1e3a8a';
                            console.log(`✅ تم تحديث رمز المؤسسة: ${codeData.code}`);
                        } else {
                            codeInput.value = 'غير متوفر';
                            codeInput.title = codeData.tooltip || 'لا توجد بيانات كافية';
                            codeInput.style.color = '#999';
                            console.log('⚠️ رمز المؤسسة غير متوفر');
                        }
                    } catch (error) {
                        console.error('خطأ في تحليل رمز المؤسسة:', error);
                        document.getElementById('institutionCode').value = 'خطأ';
                    }
                });
            }
        }        // التحقق من صحة رمز المؤسسة وتحديثه إذا لزم الأمر
        function ensureInstitutionCodeIsValid() {
            return new Promise((resolve, reject) => {
                if (institutionEngine) {
                    // محاولة تحديث رمز المؤسسة أولاً
                    institutionEngine.getInstitutionCode(function(result) {
                        try {
                            const codeData = JSON.parse(result);
                            const codeInput = document.getElementById('institutionCode');
                            
                            if (codeData.code && codeData.code !== 'غير متوفر' && codeData.code !== 'خطأ') {
                                codeInput.value = codeData.code;
                                codeInput.title = codeData.tooltip || '';
                                codeInput.style.color = '#1e3a8a';
                                console.log(`✅ رمز المؤسسة صالح: ${codeData.code}`);
                                resolve(true);
                            } else {
                                codeInput.value = 'غير متوفر';
                                codeInput.title = codeData.tooltip || 'لا توجد بيانات كافية';
                                codeInput.style.color = '#999';
                                console.log('❌ رمز المؤسسة غير صالح');
                                resolve(false);
                            }
                        } catch (error) {
                            console.error('خطأ في تحليل رمز المؤسسة:', error);
                            document.getElementById('institutionCode').value = 'خطأ';
                            resolve(false);
                        }
                    });
                } else {
                    resolve(false);
                }
            });
        }

        // حفظ بيانات المؤسسة (مع التحقق الإجباري من رقم التسجيل)
        async function saveInstitutionData() {
            if (institutionEngine) {
                showMessage("🔄 جاري التحقق من البيانات...", "info");
                
                // التحقق من صحة رمز المؤسسة أولاً
                const codeIsValid = await ensureInstitutionCodeIsValid();
                if (!codeIsValid) {
                    showMessage('❌ لا يمكن توليد رمز المؤسسة. تأكد من ملء بيانات الأكاديمية والمديرية والمؤسسة بشكل صحيح', 'error');
                    return;
                }
                
                showMessage("🔄 جاري حفظ البيانات...", "info");
                
                // تمكين الحقول المحمية مؤقتاً لقراءة القيم
                const academyField = document.getElementById('academy');
                const directorateField = document.getElementById('directorate');
                const communityField = document.getElementById('community');
                const institutionField = document.getElementById('institution');
                
                academyField.disabled = false;
                directorateField.disabled = false;
                communityField.disabled = false;
                institutionField.disabled = false;
                
                const formData = {
                    الأكاديمية: academyField.value.trim(),
                    المديرية: directorateField.value.trim(),
                    الجماعة: communityField.value.trim(),
                    المؤسسة: institutionField.value.trim(),
                    السنة_الدراسية: document.getElementById('academicYear').value,
                    البلدة: document.getElementById('city').value.trim(),
                    المدير: document.getElementById('director').value,
                    الحارس_العام: document.getElementById('guard').value,
                    السلك: document.getElementById('level').value,
                    رقم_الحراسة: document.getElementById('guardNumber').value,
                    رقم_التسجيل: document.getElementById('registrationNumber').value.trim(),
                    الأسدس: document.getElementById('semester').value
                };
                
                // التحقق من اختيار السنة الدراسية قبل الحفظ
                if (!formData.السنة_الدراسية || formData.السنة_الدراسية === "اختر السنة الدراسية") {
                    showMessage('⚠️ يرجى تحديد السنة الدراسية من القائمة المنسدلة قبل الحفظ', 'error');
                    
                    // تمييز حقل السنة الدراسية بلون أحمر
                    const academicYearField = document.getElementById('academicYear');
                    academicYearField.style.border = '2px solid #e74c3c';
                    academicYearField.style.backgroundColor = '#ffeaa7';
                    
                    // إزالة التمييز بعد اختيار سنة صحيحة
                    academicYearField.onchange = function() {
                        if (this.value && this.value !== "اختر السنة الدراسية") {
                            this.style.border = '';
                            this.style.backgroundColor = '';
                            this.onchange = null; // إزالة الحدث بعد الإصلاح
                        }
                    };
                    
                    // إعادة تعطيل الحقول المحمية
                    academyField.disabled = true;
                    directorateField.disabled = true;
                    communityField.disabled = true;
                    institutionField.disabled = true;
                    return;
                }
                
                // إعادة تعطيل الحقول المحمية
                academyField.disabled = true;
                directorateField.disabled = true;
                communityField.disabled = true;
                institutionField.disabled = true;

                // التحقق من البيانات الأساسية
                if (!formData.الأكاديمية) {
                    showMessage('❌ يرجى إدخال اسم الأكاديمية', 'error');
                    return;
                }
                if (!formData.المديرية) {
                    showMessage('❌ يرجى إدخال اسم المديرية', 'error');
                    return;
                }
                if (!formData.الجماعة) {
                    showMessage('❌ يرجى إدخال اسم الجماعة', 'error');
                    return;
                }
                if (!formData.المؤسسة) {
                    showMessage('❌ يرجى إدخال اسم المؤسسة', 'error');
                    return;
                }
                if (!formData.البلدة) {
                    showMessage('❌ يرجى إدخال اسم البلدة. هذا الحقل مطلوب', 'error');
                    
                    // تمييز حقل البلدة
                    const cityField = document.getElementById('city');
                    cityField.style.borderColor = '#e74c3c';
                    cityField.style.backgroundColor = '#fdf2f2';
                    
                    setTimeout(() => {
                        cityField.style.borderColor = '';
                        cityField.style.backgroundColor = '';
                    }, 3000);
                    
                    return;
                }

                // التحقق من وجود رمز المؤسسة
                const institutionCodeField = document.getElementById('institutionCode');
                const institutionCode = institutionCodeField.value.trim();
                if (!institutionCode || institutionCode === 'غير متوفر' || institutionCode === 'خطأ') {
                    showMessage('❌ رمز المؤسسة غير متوفر. يرجى التأكد من ملء بيانات الأكاديمية والمديرية والمؤسسة أولاً', 'error');
                    
                    // تمييز حقل رمز المؤسسة
                    institutionCodeField.style.borderColor = '#e74c3c';
                    institutionCodeField.style.backgroundColor = '#fdf2f2';
                    
                    setTimeout(() => {
                        institutionCodeField.style.borderColor = '';
                        institutionCodeField.style.backgroundColor = '';
                    }, 3000);
                    
                    return;
                }

                // التحقق من وجود رقم التسجيل (إجباري - لا يمكن الحفظ بدونه)
                const registrationNumber = formData.رقم_التسجيل;
                if (!registrationNumber || registrationNumber.trim() === '') {
                    showMessage('❌ يرجى إدخال رقم التسجيل. هذا الحقل إجباري', 'error');
                    
                    // تمييز حقل رقم التسجيل
                    const regField = document.getElementById('registrationNumber');
                    regField.style.borderColor = '#e74c3c';
                    regField.style.backgroundColor = '#fdf2f2';
                    
                    setTimeout(() => {
                        regField.style.borderColor = '';
                        regField.style.backgroundColor = '';
                    }, 3000);
                    
                    return;
                }

                // التحقق من صحة رقم التسجيل (بعد التأكد من وجوده أعلاه)
                // إجراء التحقق من رقم التسجيل أولاً
                    institutionEngine.verifyActivationCode(registrationNumber, function(result) {
                        try {
                            let verification;
                            if (typeof result === 'string') {
                                verification = JSON.parse(result);
                            } else {
                                verification = result;
                            }
                            
                            if (!verification.valid) {
                                // رقم التسجيل غير صحيح
                                showMessage(`❌ رقم التسجيل غير صحيح. ${verification.message || 'يرجى التأكد من رقم التسجيل والمحاولة مرة أخرى'}`, 'error');
                                
                                // تمييز حقل رقم التسجيل
                                const regField = document.getElementById('registrationNumber');
                                regField.style.borderColor = '#e74c3c';
                                regField.style.backgroundColor = '#fdf2f2';
                                
                                // إعادة تعيين الألوان بعد فترة
                                setTimeout(() => {
                                    regField.style.borderColor = '';
                                    regField.style.backgroundColor = '';
                                }, 3000);
                                
                                return; // منع الحفظ
                            } else {
                                // رقم التسجيل صحيح، متابعة الحفظ
                                showMessage('✅ تم التحقق من رقم التسجيل', 'success');
                                
                                // تمييز حقل رقم التسجيل بالأخضر
                                const regField = document.getElementById('registrationNumber');
                                regField.style.borderColor = '#27ae60';
                                regField.style.backgroundColor = '#f0f9f4';
                                
                                setTimeout(() => {
                                    regField.style.borderColor = '';
                                    regField.style.backgroundColor = '';
                                }, 2000);
                                
                                // الآن حفظ البيانات
                                proceedWithSave(formData);
                            }
                        } catch (error) {
                            console.error('خطأ في تحليل نتيجة التحقق:', error);
                            showMessage('❌ خطأ في التحقق من رقم التسجيل. يرجى المحاولة مرة أخرى', 'error');
                        }
                    });
                // رقم التسجيل مطلوب دائماً - تم التحقق من وجوده أعلاه
                // إذا وصلنا هنا فهذا يعني أن رقم التسجيل موجود وصحيح
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }
        
        // دالة مساعدة لإتمام عملية الحفظ
        function proceedWithSave(formData) {
            try {
                institutionEngine.saveInstitutionData(JSON.stringify(formData));
            } catch (error) {
                console.error('خطأ في إرسال البيانات:', error);
                showMessage('❌ خطأ في إرسال البيانات', 'error');
            }
        }
        
        // دالة التحقق من رقم التسجيل عند إدخاله
        function validateRegistrationNumber() {
            const regField = document.getElementById('registrationNumber');
            const regNumber = regField.value.trim();
            
            if (!regNumber || !institutionEngine) {
                return;
            }
            
            // عرض مؤشر التحقق
            regField.style.borderColor = '#3498db';
            regField.style.backgroundColor = '#f8fafc';
            
            institutionEngine.verifyActivationCode(regNumber, function(result) {
                try {
                    let verification;
                    if (typeof result === 'string') {
                        verification = JSON.parse(result);
                    } else {
                        verification = result;
                    }
                    
                    if (verification.valid) {
                        // رقم صحيح
                        regField.style.borderColor = '#27ae60';
                        regField.style.backgroundColor = '#f0f9f4';
                        regField.title = 'رقم التسجيل صحيح ✅';
                    } else {
                        // رقم غير صحيح
                        regField.style.borderColor = '#e74c3c';
                        regField.style.backgroundColor = '#fdf2f2';
                        regField.title = verification.message || 'رقم التسجيل غير صحيح ❌';
                    }
                } catch (error) {
                    console.error('خطأ في تحليل نتيجة التحقق:', error);
                    regField.style.borderColor = '#f39c12';
                    regField.style.backgroundColor = '#fef9e7';
                    regField.title = 'خطأ في التحقق ⚠️';
                }
            });
        }        // تحميل الشعار
        function uploadLogo() {
            if (institutionEngine) {
                showMessage("🔄 فتح نافذة تحديد الملف...", "info");
                console.log("🔄 بدء عملية رفع الشعار...");
                
                try {
                    institutionEngine.uploadLogo();
                    console.log("✅ تم إرسال طلب رفع الشعار");
                } catch (error) {
                    console.error('خطأ في رفع الشعار:', error);
                    showMessage('❌ خطأ في رفع الشعار: ' + error.message, 'error');
                }
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
                console.log("❌ محرك المؤسسة غير متاح");
            }
        }

        // عرض مسار الشعار
        function showLogoPath() {
            if (institutionEngine) {
                institutionEngine.getLogoPath(function(path) {
                    showMessage(`📁 مسار الشعار: ${path}`, "info");
                    console.log("مسار الشعار المحفوظ:", path);
                });
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }        // تحديث عرض الشعار
        function updateLogoDisplay(logoData) {
            const logoDisplay = document.getElementById('logoDisplay');

            if (!logoDisplay) {
                console.error("❌ عنصر عرض الشعار غير موجود في الصفحة");
                return;
            }

            if (logoData && logoData.trim() !== '') {
                console.log("🖼️ عرض الشعار في الواجهة");

                // تحديد نوع الصورة بناءً على البيانات
                let imageType = 'jpeg';
                if (logoData.startsWith('/9j/')) imageType = 'jpeg';
                else if (logoData.startsWith('iVBORw0KGgo')) imageType = 'png';
                else if (logoData.startsWith('R0lGOD')) imageType = 'gif';

                logoDisplay.innerHTML = `<img src="data:image/${imageType};base64,${logoData}" alt="شعار المؤسسة">`;
                logoDisplay.style.border = '2px solid #667eea';
                logoDisplay.style.background = '#fff';
                console.log(`✅ تم تحديث الشعار بنجاح (نوع: ${imageType})`);
            } else {
                console.log("📝 عرض النص البديل للشعار");                logoDisplay.innerHTML = `
                    <div class="logo-placeholder">
                        🏢<br>
                        انقر لتحميل الشعار
                    </div>
                `;
                logoDisplay.style.border = '2px dashed #ddd';
                logoDisplay.style.background = '#f9f9f9';
                console.log("⚠️ تم إخفاء الشعار - لا توجد بيانات");
            }
        }

        // معالجة تحديث البيانات
        function handleDataUpdate(result) {
            if (result === "success") {
                showMessage("✅ تم حفظ البيانات بنجاح", "success");
            } else if (result.startsWith("error:")) {
                const errorMsg = result.substring(6);
                showMessage("❌ خطأ في حفظ البيانات: " + errorMsg, "error");
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            // إخفاء الرسالة بعد 3 ثوان
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 3000);
        }

        // إظهار/إخفاء مؤشر التحميل
        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (show) {
                loadingIndicator.classList.add('show');
            } else {
                loadingIndicator.classList.remove('show');
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
            
            // إضافة مستمع حدث لحقل رقم التسجيل للتحقق الفوري
            const registrationField = document.getElementById('registrationNumber');
            if (registrationField) {
                registrationField.addEventListener('input', function() {
                    // إعادة تعيين الألوان عند الكتابة
                    registrationField.style.borderColor = '';
                    registrationField.style.backgroundColor = '';
                    registrationField.title = '';
                });
                
                registrationField.addEventListener('blur', function() {
                    // التحقق عند مغادرة الحقل
                    setTimeout(validateRegistrationNumber, 100);
                });
            }
            
            // إضافة مراقبة لحقل البلدة لإزالة التمييز عند الكتابة
            const cityField = document.getElementById('city');
            if (cityField) {
                cityField.addEventListener('input', function() {
                    // إعادة تعيين الألوان عند الكتابة
                    cityField.style.borderColor = '';
                    cityField.style.backgroundColor = '';
                });
            }
            
            // إضافة تحديث تلقائي لرمز المؤسسة عند تحميل البيانات
            // سيتم تحديث الرمز تلقائياً في دالة fillInstitutionForm
        });

        // إضافة حدث النقر على عرض الشعار لرفع شعار جديد
        document.getElementById('logoDisplay').addEventListener('click', function() {
            uploadLogo();
        });
    </script>
</body>
</html>"""


def main():
    """تشغيل نافذة إدارة بيانات المؤسسة"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("إدارة بيانات المؤسسة")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة
    window = InstitutionWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == '__main__':
    print("🌐 بدء تشغيل نظام إدارة بيانات المؤسسة...")
    print("=" * 60)
    print("📋 الميزات:")
    print("   🔹 واجهة HTML جميلة ومتجاوبة")
    print("   🔹 إدارة شاملة لبيانات المؤسسة")
    print("   🔹 رفع وإدارة شعار المؤسسة")
    print("   🔹 تكامل كامل مع قاعدة البيانات")
    print("   🔹 تصميم عصري ومرن")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
