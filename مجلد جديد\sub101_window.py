import sys
import sqlite3
import os
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QFrame, QLabel, QLineEdit,
                            QPushButton, QTabWidget, QVBoxLayout, QHBoxLayout,
                            QGridLayout, QWidget, QTextEdit, QGraphicsDropShadowEffect, QMessageBox, QTableView, QAbstractItemView, QStackedWidget, QScrollArea, QSizePolicy,
                            QDialog, QListWidget, QListWidgetItem, QStyle)
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QTextDocument, QPixmap
from PyQt5.QtCore import Qt, QRect, QAbstractTableModel, QDateTime, QSize, QTimer
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from PyQt5.QtSql import QSqlQueryModel, QSqlQuery, QSqlDatabase # Ensure QSqlDatabase is imported
import traceback # Ensure traceback is imported

# Importar las clases y módulos personalizados necesarios
try:
    # استيراد نافذة تدبير البطائق المدرسية
    from student_card_ui import StudentCardUI

    # استيراد نافذة تدبير المخالفات والسلوك - نستورد الفئة مباشرة
    from sub11_window import ViolationsWidget, ViolationsWindow
    VIOLATIONS_WINDOW_AVAILABLE = True
except ImportError as e:
    print(f"خطأ في استيراد واجهة البطائق المدرسية أو المخالفات: {e}")
    VIOLATIONS_WINDOW_AVAILABLE = False

# تعريف متغيرات صلاحيات المستخدم - يمكن تعديلها حسب نظام الصلاحيات الخاص بالتطبيق
USER_PERMISSIONS = {
    "VIEW_STUDENT_DATA": True,        # عرض بيانات الطلاب
    "EDIT_STUDENT_DATA": True,        # تعديل بيانات الطلاب
    "MANAGE_VIOLATIONS": True,        # إدارة المخالفات
    "MANAGE_ATTENDANCE": True,        # إدارة الحضور
    "MANAGE_ABSENCE": True,           # إدارة تبرير الغياب
    "MANAGE_PARENT_VISITS": True,     # إدارة زيارات أولياء الأمور
}

# التحقق من وجود الملفات المطلوبة
def check_file_availability(filepath):
    """التحقق من وجود ملف معين"""
    return os.path.exists(filepath)

# التحقق من صلاحيات المستخدم
def check_user_permission(permission_key):
    """التحقق من صلاحيات المستخدم للوصول إلى ميزة معينة"""
    return USER_PERMISSIONS.get(permission_key, False)

# Define all window availability flags at the start
ABSENCE_WINDOW_AVAILABLE = False
ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = False
VIOLATIONS_MANAGEMENT_WINDOW_AVAILABLE = False
VIOLATIONS_WINDOW_AVAILABLE = False  # Added flag for ViolationsWindow
PARENT_VISIT_WINDOW_AVAILABLE = False
PARENT_VISIT_MANAGEMENT_WINDOW_AVAILABLE = False

# Define fallback classes to prevent crashes when real classes aren't available
class DummyViolationsWindow:
    """Dummy class used when the real ViolationsWindow is not available"""
    def __init__(self, parent=None):
        self.parent = parent
        QMessageBox.warning(parent, "Feature Unavailable",
                          "نافذة مسك المخالفات غير متوفرة")

    def set_student_info(self, code=None, name=None, id_num=None, level=None, class_name=None):
        pass

    def show(self):
        pass

class DummyViolationsManagementWindow:
    """Dummy class used when the real ViolationsManagementWindow is not available"""
    def __init__(self, student_code=None, parent=None):
        self.parent = parent
        QMessageBox.warning(parent, "Feature Unavailable",
                          "نافذة معالجة المخالفات غير متوفرة")

    def show(self):
        pass

# إضافة صفوف وهمية أخرى لباقي النوافذ
class DummyAbsenceJustificationWindow:
    """صف وهمي للاستخدام عند عدم توفر نافذة تبرير الغياب"""
    def __init__(self, parent=None):
        self.parent = parent
        QMessageBox.warning(parent, "ميزة غير متوفرة",
                          "نافذة مسك تبرير الغياب غير متوفرة")

    def set_student_info(self, code=None, name=None, id_num=None, level=None, class_name=None):
        pass

    def show(self):
        pass

class DummyAbsenceManagementWindow:
    """صف وهمي للاستخدام عند عدم توفر نافذة معالجة تبرير الغياب"""
    def __init__(self, student_code=None, parent=None, db=None, academic_year=None):
        self.parent = parent
        QMessageBox.warning(parent, "ميزة غير متوفرة",
                          "نافذة معالجة تبرير الغياب غير متوفرة")

    def show(self):
        pass

# تعديل طريقة استيراد الملفات لجعلها تعمل من أي مكان
import os
import sys
import importlib.util

def import_module_by_name(module_name):
    """استيراد وحدة برمجية من نفس المجلد الذي يتواجد فيه البرنامج الرئيسي"""
    # الحصول على المجلد الذي يتواجد فيه البرنامج الحالي
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # بناء المسار الكامل للملف المطلوب
    module_path = os.path.join(script_dir, f"{module_name}.py")

    if os.path.exists(module_path):
        try:
            # استيراد الوحدة البرمجية ديناميكيًا
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            if spec:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"تم استيراد وحدة {module_name} بنجاح.")
                return module, True
        except Exception as e:
            print(f"خطأ في استيراد وحدة {module_name}: {e}")

    print(f"لم يتم العثور على وحدة {module_name}")
    return None, False

# استبدال استيراد الوحدات الثابتة بالاستيراد الديناميكي
ABSENCE_WINDOW_AVAILABLE = False
ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = False
VIOLATIONS_MANAGEMENT_WINDOW_AVAILABLE = False
VIOLATIONS_WINDOW_AVAILABLE = False
PARENT_VISIT_WINDOW_AVAILABLE = False
PARENT_VISIT_MANAGEMENT_WINDOW_AVAILABLE = False

# استيراد وتهيئة النوافذ المطلوبة
sub11_module, _ = import_module_by_name("sub11_window")
if sub11_module:
    EntryPermissionWindow = getattr(sub11_module, "EntryPermissionWindow", None)
else:
    # تعريف صف EntryPermissionWindow وهمي في حال عدم توفر الملف
    class EntryPermissionWindow:
        def __init__(self, student_code=None, permission_type=None, parent=None):
            self.parent = parent
            QMessageBox.warning(parent, "ميزة غير متوفرة",
                             "نافذة ورقة السماح بالدخول غير متوفرة")

        def show(self):
            pass

# استيراد باقي النوافذ بنفس الطريقة
sub14_module, is_available = import_module_by_name("sub14_window")
if (is_available and hasattr(sub14_module, "AbsenceManagementWindow")):
    AbsenceManagementWindow = sub14_module.AbsenceManagementWindow
    ABSENCE_MANAGEMENT_WINDOW_AVAILABLE = True
    print("تم استيراد نافذة معالجة تبرير الغياب بنجاح")
else:
    AbsenceManagementWindow = DummyAbsenceManagementWindow
    print("نافذة معالجة تبرير الغياب غير متوفرة")

sub13_module, is_available = import_module_by_name("sub13_window")
if (is_available and hasattr(sub13_module, "AbsenceJustificationWindow")):
    AbsenceJustificationWindow = sub13_module.AbsenceJustificationWindow
    ABSENCE_WINDOW_AVAILABLE = True
    print("تم استيراد نافذة تبرير الغياب بنجاح")
else:
    AbsenceJustificationWindow = DummyAbsenceJustificationWindow
    print("نافذة تبرير الغياب غير متوفرة")

sub16_module, is_available = import_module_by_name("sub16_window")
if (is_available and hasattr(sub16_module, "ParentVisitWindow")):
    ParentVisitWindow = sub16_module.ParentVisitWindow
    PARENT_VISIT_WINDOW_AVAILABLE = True
    print("تم استيراد نافذة توثيق زيارة أولياء الأمور بنجاح")
else:
    # تعريف صف ParentVisitWindow وهمي
    class ParentVisitWindow:
        def __init__(self, parent=None):
            self.parent = parent
            QMessageBox.warning(parent, "ميزة غير متوفرة",
                             "نافذة توثيق زيارة أولياء الأمور غير متوفرة")

        def set_student_info(self, code=None, name=None, id_num=None, level=None, class_name=None):
            pass

        def show(self):
            pass
    print("نافذة توثيق زيارة أولياء الأمور غير متوفرة")

# استيراد باقي الوحدات بنفس الطريقة
sub17_module, is_available = import_module_by_name("sub17_window")
if (is_available and hasattr(sub17_module, "ParentVisitManagementWindow")):
    ParentVisitManagementWindow = sub17_module.ParentVisitManagementWindow
    PARENT_VISIT_MANAGEMENT_WINDOW_AVAILABLE = True
    print("تم استيراد نافذة معالجة توثيق زيارة أولياء الأمور بنجاح")
else:
    # تعريف صف ParentVisitManagementWindow وهمي
    class ParentVisitManagementWindow:
        def __init__(self, parent=None):
            self.parent = parent
            QMessageBox.warning(parent, "ميزة غير متوفرة",
                             "نافذة معالجة توثيق زيارة أولياء الأمور غير متوفرة")

        def show(self):
            pass
    print("نافذة معالجة توثيق زيارة أولياء الأمور غير متوفرة")

# Fix for ViolationsManagementWindow import - with permission check
sub12_module, is_available = import_module_by_name("sub12_window")
if (is_available and hasattr(sub12_module, "ViolationsManagementWindow")):
    ViolationsManagementWindow = sub12_module.ViolationsManagementWindow
    VIOLATIONS_MANAGEMENT_WINDOW_AVAILABLE = True
    print("تم استيراد نافذة معالجة المخالفات بنجاح")
else:
    ViolationsManagementWindow = DummyViolationsManagementWindow
    print("نافذة معالجة المخالفات غير متوفرة")

# Fix for ViolationsWindow import - with permission check
sub11_module, is_available = import_module_by_name("sub11_window")
if (is_available and hasattr(sub11_module, "ViolationsWindow")):
    ViolationsWindow = sub11_module.ViolationsWindow
    VIOLATIONS_WINDOW_AVAILABLE = True
    print("تم استيراد نافذة مسك المخالفات بنجاح")
else:
    ViolationsWindow = DummyViolationsWindow
    print("نافذة مسك المخالفات غير متوفرة")

# Import the printing function for violations
try:
    from print5_test import print_student_violations_record, print_violations_by_academic_year
    PRINT5_AVAILABLE = True
    print("تم استيراد دوال إنشاء HTML من print5_test.py بنجاح.")
except ImportError:
    PRINT5_AVAILABLE = False
    print("تحذير: ملف print5_test.py غير موجود أو يحتوي على أخطاء.")
    # Define dummy functions to avoid errors
    def print_student_violations_record(*args, **kwargs): pass
    def print_violations_by_academic_year(*args, **kwargs): pass


# فحص توفر قاعدة البيانات قبل تشغيل البرنامج
def check_database_availability():
    """التحقق من وجود قاعدة البيانات ومدى إمكانية الوصول إليها"""
    db_path = "data.db"
    if not os.path.exists(db_path):
        QMessageBox.critical(None, "خطأ", f"لم يتم العثور على قاعدة البيانات في المسار: {db_path}")
        return False
    return True

# --- >> Add fix_arabic_text function here for consistency << ---
# --- >> Add ARABIC_SUPPORT check and bidi import << ---
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
# --- >> End ARABIC_SUPPORT check and bidi import << ---

def fix_arabic_text(text):
    """معالجة النص العربي متعدد الأسطر بحيث يظهر بنفس ترتيب الأسطر الأصلي"""
    if not text:
        return ""
    text = str(text)
    try:
        if ARABIC_SUPPORT:
            lines = text.split('\n')  # فصل الأسطر
            reshaped_lines = []
            for line in lines:
                line = line.strip()
                if line:
                    reshaped = arabic_reshaper.reshape(line)
                    bidi_text = get_display(reshaped)
                    reshaped_lines.append(bidi_text)
            # لا نعكس ترتيب الأسطر، بل نجمعها كما هي:
            # --- >> تصحيح الخطأ الإملائي هنا << ---
            return '\n'.join(reshaped_lines) # Corrected line
        else:
            return text
    except Exception as e:
        print(f"خطأ في معالجة النص العربي '{text[:20]}...': {e}")
        return text
# --- >> End fix_arabic_text function << ---

class StudentCardWindow(QMainWindow):
    def showEvent(self, event):
        """معالجة حدث عرض النافذة"""
        super().showEvent(event)
        # تعيين مؤشر اليد لجميع التبويبات بعد عرض النافذة
        QTimer.singleShot(500, self.set_hand_cursor_for_all_tabs)

    def __init__(self, parent=None, db=None, academic_year=None):
        super().__init__(parent)
        # If external db connection provided, use it
        if db and hasattr(db, 'isOpen') and db.isOpen():
            print("استخدام اتصال قاعدة البيانات الخارجي")
            self.db = db  # Store QSqlDatabase object
            self.conn = None  # Don't create SQLite connection
            self.cursor = None
        else:
            # Create new SQLite connection if no external db provided
            self.db = None
            try:
                self.conn = sqlite3.connect("data.db")
                self.cursor = self.conn.cursor()
            except Exception as e:
                print(f"Error connecting to database: {e}")
                self.conn = None
                self.cursor = None

        self.student_code = None
        self.current_record_id = None  # Inicialización explícita del current_record_id
        self.entry_data = {}
        self.institution_data = {}

        # تخزين قاعدة البيانات والسنة الدراسية إذا تم تمريرهما
        self.external_db = db
        self.external_academic_year = academic_year

        # إضافة متغير لتخزين النافذة النشطة الحالية
        self.current_active_window = None

        # إضافة منطقة محتوى مركزية للنوافذ الفرعية
        self.stacked_content = QStackedWidget()

        # تعديل خصائص النافذة ليناسب الاستخدام داخل واجهة أخرى
        if parent:
            # إذا كانت النافذة جزءًا من واجهة أخرى، نزيل خصائص النافذة المستقلة
            self.setWindowFlags(Qt.Widget)
        else:
            # إذا كانت النافذة مستقلة، نستخدم خصائص مربع الحوار بدون زر تكبير أو تصغير
            self.setWindowFlags(Qt.Dialog | Qt.MSWindowsFixedSizeDialogHint | Qt.FramelessWindowHint)

        # Configure the main window
        self.setWindowTitle("بطاقة تلميذ")

        # تعيين الحجم المحدد 600×600
        self.setFixedSize(600, 600)

        # تعيين لون خلفية النافذة لتكون زرقاء فاتحة وغير شفافة
        light_blue_palette = QPalette()
        light_blue_palette.setColor(QPalette.Window, QColor(220, 240, 255, 255))  # لون أزرق فاتح غير شفاف
        self.setPalette(light_blue_palette)
        self.setAutoFillBackground(True)

        # تعيين ويدجت مركزي - بدون تخطيط رئيسي
        self.central_widget = QWidget()
        # إزالة الشفافية من الويدجت المركزي أيضاً
        self.central_widget.setAttribute(Qt.WA_TranslucentBackground, False)
        self.central_widget.setAttribute(Qt.WA_NoSystemBackground, False)
        self.central_widget.setAutoFillBackground(True)

        # تعيين خلفية الويدجت المركزي
        central_palette = QPalette()
        central_palette.setColor(QPalette.Window, QColor(220, 240, 255, 255))
        self.central_widget.setPalette(central_palette)

        self.setCentralWidget(self.central_widget)

        # إنشاء تخطيط عمودي مبسط للنافذة (مباشرة على ويدجت مركزي)
        layout = QVBoxLayout(self.central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # إنشاء إطار العنوان
        header_layout = QHBoxLayout()

        # زر الإغلاق
        self.close_button = QPushButton("×")
        self.close_button.setFont(QFont("Arial", 16, QFont.Bold))
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 17px; /* Adjusted for new size */
                min-width: 35px;
                min-height: 35px;
                max-width: 35px;
                max-height: 35px;
                padding: 0px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.close_button.clicked.connect(self.close)

        # عنوان النافذة
        self.title_label = QLabel("بطاقة تلميذ(ة)")
        self.title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        self.title_label.setAlignment(Qt.AlignCenter)

        header_layout.addWidget(self.close_button, 0, Qt.AlignLeft)
        header_layout.addWidget(self.title_label, 1)

        layout.addLayout(header_layout)

        # إنشاء شريط المعلومات مع اسم الطالب ورمزه - تنسيق أكثر تنظيماً
        info_layout = QHBoxLayout()
        info_layout.setAlignment(Qt.AlignRight)

        # حقل اسم الطالب - تقليص الحجم
        self.name_field = QLineEdit("الاسم والنسب: ")
        self.name_field.setFont(QFont("Calibri", 18))
        self.name_field.setStyleSheet("color: white; background-color: #3498db; border-radius: 5px;")
        self.name_field.setMinimumWidth(300)
        self.name_field.setReadOnly(True)

        # حقل رمز الطالب - تقليص الحجم
        self.code_field = QLineEdit("الرمز: ")
        self.code_field.setFont(QFont("Calibri", 18))
        self.code_field.setStyleSheet("color: white; background-color: #3498db; border-radius: 5px;")
        self.code_field.setMinimumWidth(150)
        self.code_field.setReadOnly(True)

        info_layout.addWidget(self.name_field)
        info_layout.addSpacing(4)
        info_layout.addWidget(self.code_field)

        layout.addLayout(info_layout)

        # إنشاء تبويب البيانات مباشرة (بدون إطار إضافي)
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 13, QFont.Bold))
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet('''
            QTabWidget::pane { border: 1px solid #ddd; }
            QTabBar::tab {
                background-color: #c0d8e8;
                padding: 6px 12px;
                margin-right: 2px;
                min-width: 150px;
                max-width: 150px;
                width: 150px;
                color: black;
                font-weight: bold;
                cursor: pointer;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        ''')
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)

        # تعيين مؤشر اليد لجميع التبويبات
        for i in range(self.tab_widget.count()):
            self.tab_widget.tabBar().setCursor(Qt.PointingHandCursor)

        # Create the tab_student_data widget first before using it
        self.tab_student_data = QWidget()

        self.tab_student_layout = QVBoxLayout(self.tab_student_data)

        # Create a scroll area for the combined data
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        scroll_layout.setSpacing(10)

        # Personal data section
        self.personal_data_frame = QFrame()
        self.personal_data_frame.setStyleSheet("background-color: rgba(255, 255, 255, 0.7); border-radius: 5px; border: 1px solid #3498db;")
        self.personal_data_layout = QGridLayout(self.personal_data_frame)
        self.personal_data_layout.setContentsMargins(5, 5, 5, 5)
        self.personal_data_layout.setSpacing(2) # مسافة صغيرة بين العناصر

        # Create personal data labels and fields
        personal_fields = [
            ("الرمز:", "code_display", 1),
            ("الاسم والنسب:", "name_display", 2),
            ("النوع:", "gender_display", 3),
            ("تاريخ الازدياد:", "birth_date_display", 4),
            ("مكان الازدياد:", "birth_place_display", 5),
        ]

        for i, (label_text, field_name, row) in enumerate(personal_fields):
            label = QLabel(label_text)
            label.setFont(QFont("Calibri", 13, QFont.Bold))
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            label.setFixedWidth(100) # تحديد العرض بـ 100 بكسل
            label.setStyleSheet("border: 1px solid #3498db; border-radius: 3px; padding: 2px; background-color: #EBF5FB;")

            field = QLineEdit()
            field.setFont(QFont("Calibri", 13, QFont.Bold))
            field.setReadOnly(True)
            field.setFixedWidth(200) # التأكد من العرض بـ 200 بكسل
            field.setAlignment(Qt.AlignRight) # محاذاة النص داخل الحقل إلى اليمين
            field.setStyleSheet("border: 1px solid #3498db; border-radius: 4px; padding: 2px; background-color: #f8f9fa;") # إضافة حدود زرقاء
            setattr(self, field_name, field)

            self.personal_data_layout.addWidget(label, row, 0)
            self.personal_data_layout.addWidget(field, row, 1)

        self.personal_data_layout.setColumnStretch(2, 1)

        scroll_layout.addWidget(self.personal_data_frame)

        # School data section
        self.school_data_frame = QFrame()
        self.school_data_frame.setStyleSheet("background-color: rgba(255, 255, 255, 0.7); border-radius: 5px; border: 1px solid #3498db;")
        self.school_data_layout = QGridLayout(self.school_data_frame)
        self.school_data_layout.setContentsMargins(5, 5, 5, 5)
        self.school_data_layout.setSpacing(2) # مسافة صغيرة بين العناصر

        # Create school data labels and fields
        school_fields = [
            ("السنة الدراسية:", "school_year_display", 1),
            ("المستوى:", "level_display", 2),
            ("القسم:", "class_display", 3),
            ("رت:", "rt_display", 4),
        ]

        for i, (label_text, field_name, row) in enumerate(school_fields):
            label = QLabel(label_text)
            label.setFont(QFont("Calibri", 13, QFont.Bold))
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            label.setFixedWidth(100) # تحديد العرض بـ 100 بكسل
            label.setStyleSheet("border: 1px solid #3498db; border-radius: 3px; padding: 2px; background-color: #EBF5FB;")

            field = QLineEdit()
            field.setFont(QFont("Calibri", 13, QFont.Bold))
            field.setReadOnly(True)
            field.setFixedWidth(200) # تحديد العرض بـ 200 بكسل
            field.setAlignment(Qt.AlignRight) # محاذاة النص داخل الحقل إلى اليمين
            field.setStyleSheet("border: 1px solid #3498db; border-radius: 4px; padding: 2px; background-color: #f8f9fa;") # إضافة حدود زرقاء
            setattr(self, field_name, field)

            self.school_data_layout.addWidget(label, row, 0)
            self.school_data_layout.addWidget(field, row, 1)

        self.school_data_layout.setColumnStretch(2, 1)

        scroll_layout.addWidget(self.school_data_frame)

        # Contact Information section (now in the same tab)
        self.contact_frame = QFrame()
        self.contact_frame.setStyleSheet("background-color: rgba(255, 255, 255, 0.7); border-radius: 5px; border: 1px solid #3498db;")
        self.contact_layout = QGridLayout(self.contact_frame)
        self.contact_layout.setContentsMargins(5, 5, 5, 5)
        self.contact_layout.setSpacing(2) # مسافة صغيرة بين العناصر

        # إضافة عنوان فرعي لقسم معلومات الاتصال
        contact_title = QLabel("معلومات الاتصال - يمكن تعديلها:")
        contact_title.setFont(QFont("Calibri", 14, QFont.Bold))
        contact_title.setStyleSheet("color: #2980b9; margin-bottom: 5px;")
        self.contact_layout.addWidget(contact_title, 0, 0, 1, 2)

        # Create contact labels and fields
        contact_fields = [
            ("الهاتف 1:", "phone1_field", 1),
            ("الهاتف 2:", "phone2_field", 2),
        ]

        for i, (label_text, field_name, row) in enumerate(contact_fields):
            label = QLabel(label_text)
            label.setFont(QFont("Calibri", 13, QFont.Bold))
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            label.setFixedWidth(100) # تحديد العرض بـ 100 بكسل
            label.setStyleSheet("border: 1px solid #3498db; border-radius: 3px; padding: 2px; background-color: #EBF5FB;")

            field = QLineEdit()
            field.setFont(QFont("Calibri", 13, QFont.Bold))
            field.setReadOnly(False) # تغيير قيمة ReadOnly لتمكين التعديل
            field.setFixedWidth(200) # تحديد العرض بـ 200 بكسل
            field.setAlignment(Qt.AlignRight) # محاذاة النص داخل الحقل إلى اليمين
            field.setStyleSheet("border: 1px solid #3498db; border-radius: 4px; padding: 2px; background-color: #ffffff;") # تغيير لون الخلفية لتوضيح أنها قابلة للتعديل
            setattr(self, field_name, field)

            self.contact_layout.addWidget(label, row, 0)
            self.contact_layout.addWidget(field, row, 1)

        # Notes field
        notes_label = QLabel("ملاحظات:")
        notes_label.setFont(QFont("Calibri", 13, QFont.Bold))
        notes_label.setAlignment(Qt.AlignRight | Qt.AlignTop)
        notes_label.setStyleSheet("border: 1px solid #3498db; border-radius: 3px; padding: 2px; background-color: #EBF5FB;")

        self.notes_field = QTextEdit()
        self.notes_field.setFont(QFont("Calibri", 13))
        self.notes_field.setReadOnly(False) # تغيير قيمة ReadOnly لتمكين التعديل
        self.notes_field.setFixedHeight(80)
        self.notes_field.setStyleSheet("border: 1px solid #3498db; border-radius: 4px; padding: 2px; background-color: #ffffff;") # تغيير لون الخلفية لتوضيح أنها قابلة للتعديل

        self.contact_layout.addWidget(notes_label, 3, 0)
        self.contact_layout.addWidget(self.notes_field, 3, 1)

        # إضافة تخطيط أفقي للأزرار
        buttons_layout = QHBoxLayout()

        # إضافة زر الحفظ
        save_button = QPushButton("حفظ التعديلات")
        save_button.setFont(QFont("Calibri", 13, QFont.Bold))
        save_button.setCursor(Qt.PointingHandCursor)
        save_button.setMinimumHeight(40)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
                margin-top: 5px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #219652;
            }
        """)
        save_button.clicked.connect(self.save_contact_info)

        # إضافة زر إضافة ملاحظات مختصرة
        notes_button = QPushButton("إضافة ملاحظات مختصرة")
        notes_button.setFont(QFont("Calibri", 13, QFont.Bold))
        notes_button.setCursor(Qt.PointingHandCursor)
        notes_button.setMinimumHeight(40)
        notes_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
                margin-top: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        notes_button.clicked.connect(self.show_quick_notes_dialog)

        # إضافة الأزرار إلى التخطيط الأفقي
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(notes_button)

        # إضافة التخطيط الأفقي إلى تخطيط الاتصال
        self.contact_layout.addLayout(buttons_layout, 4, 0, 1, 2)

        self.contact_layout.setColumnStretch(2, 1)

        scroll_layout.addWidget(self.contact_frame)

        # Add scroll content to scroll area
        scroll_area.setWidget(scroll_content)
        self.tab_student_layout.addWidget(scroll_area)

        # Add this first tab to the tab widget
        self.tab_widget.addTab(self.tab_student_data, "بيانات التلميذ")

        # Add entry permission tab - redesigned with two tables
        entry_tab = QWidget()
        entry_tab_layout = QVBoxLayout(entry_tab)
        entry_tab_layout.setContentsMargins(5, 5, 5, 5)
        entry_tab_layout.setSpacing(5)

        # عنوان التبويب
        title_label = QLabel("سجلات ورقة السماح بالدخول")
        title_label.setFont(QFont("Calibri", 13, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
        entry_tab_layout.addWidget(title_label)

        # تخطيط أفقي للجدولين
        tables_layout = QHBoxLayout()
        tables_layout.setSpacing(10)

        # الجانب الأيمن: جدول السماح
        entry_side = QVBoxLayout()

        entry_title = QLabel("سجلات السماح")
        entry_title.setFont(QFont("Calibri", 12, QFont.Bold))
        entry_title.setAlignment(Qt.AlignCenter)
        entry_title.setStyleSheet("color: #27ae60;")
        entry_side.addWidget(entry_title)

        self.entry_records_table = QTableView()
        self.entry_records_table.setAlternatingRowColors(True)
        self.entry_records_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.entry_records_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.entry_records_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.entry_records_table.setSortingEnabled(True)
        self.entry_records_table.setFont(QFont("Calibri", 12))
        self.entry_records_table.setStyleSheet("""
            QTableView {
                background-color: white;
                alternate-background-color: #f7f9fc;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #27ae60;
                color: white;
                padding: 3px;
                font-weight: bold;
                border: none;
                border-right: 1px solid #219a52;
            }
        """)
        self.entry_records_model = QSqlQueryModel(self)
        self.entry_records_table.setModel(self.entry_records_model)
        entry_side.addWidget(self.entry_records_table)

        # أزرار السماح
        entry_buttons = QHBoxLayout()
        delete_entry_btn = QPushButton("حذف السجل المحدد")
        delete_entry_btn.setFont(QFont("Calibri", 10, QFont.Bold))
        delete_entry_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        delete_entry_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 3px 6px;
                min-height: 35px; /* Added height */
                max-height: 35px; /* Added height */
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_entry_btn.clicked.connect(self.delete_selected_entry_record)
        entry_buttons.addWidget(delete_entry_btn)
        entry_side.addLayout(entry_buttons)

        # الجانب الأيسر: جدول التأخر
        late_side = QVBoxLayout()

        late_title = QLabel("سجلات التأخر")
        late_title.setFont(QFont("Calibri", 12, QFont.Bold))
        late_title.setAlignment(Qt.AlignCenter)
        late_title.setStyleSheet("color: #e74c3c;")
        late_side.addWidget(late_title)

        self.late_records_table = QTableView()
        self.late_records_table.setAlternatingRowColors(True)
        self.late_records_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.late_records_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.late_records_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.late_records_table.setSortingEnabled(True)
        self.late_records_table.setFont(QFont("Calibri", 12))
        self.late_records_table.setStyleSheet("""
            QTableView {
                background-color: white;
                alternate-background-color: #f7f9fc;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #e74c3c;
                color: white;
                padding: 3px;
                font-weight: bold;
                border: none;
                border-right: 1px solid #c0392b;
            }
        """)
        self.late_records_model = QSqlQueryModel(self)
        self.late_records_table.setModel(self.late_records_model)
        late_side.addWidget(self.late_records_table)

        # أزرار التأخر
        late_buttons = QHBoxLayout()
        delete_late_btn = QPushButton("حذف السجل المحدد")
        delete_late_btn.setFont(QFont("Calibri", 10, QFont.Bold))
        delete_late_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        delete_late_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 4px;
                padding: 3px 6px;
                min-height: 35px; /* Added height */
                max-height: 35px; /* Added height */
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_late_btn.clicked.connect(self.delete_selected_late_record)
        late_buttons.addWidget(delete_late_btn)
        late_side.addLayout(late_buttons)

        # إضافة الجانبين إلى التخطيط الأفقي
        tables_layout.addLayout(entry_side)
        tables_layout.addLayout(late_side)

        entry_tab_layout.addLayout(tables_layout)

        # أزرار للطباعة أسفل المنطقة
        bottom_buttons = QHBoxLayout()
        print_btn = QPushButton("طباعة السجلات")
        print_btn.setFont(QFont("Calibri", 11, QFont.Bold))
        print_btn.setCursor(Qt.PointingHandCursor)  # تغيير المؤشر إلى شكل يد
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
                margin-top: 5px;
                min-height: 35px; /* Added height */
                max-height: 35px; /* Added height */
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        print_btn.clicked.connect(self.print_entry_records)
        bottom_buttons.addWidget(print_btn)

        entry_tab_layout.addLayout(bottom_buttons)

        self.tab_widget.addTab(entry_tab, "ورقة السماح بالدخول")

        # Add violations tab - with nested subtabs
        violations_tab = QWidget()
        violations_tab_layout = QVBoxLayout(violations_tab)
        violations_tab_layout.setContentsMargins(5, 5, 5, 5)
        violations_tab_layout.setSpacing(5)

        # إضافة تبويبات فرعية داخل تبويب المخالفات
        violations_subtabs = QTabWidget()
        violations_subtabs.setFont(QFont("Calibri", 13, QFont.Bold))
        violations_subtabs.setLayoutDirection(Qt.RightToLeft)
        violations_subtabs.setStyleSheet('''
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: rgba(255, 255, 255, 0.7);
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 5px 10px;
                margin-right: 2px;
                min-width: 150px;
                max-width: 150px;
                width: 150px;
                color: black;
                font-weight: bold;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                cursor: pointer;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        ''')

        # تبويب مسك المخالفات - تضمين النافذة مباشرة بدلاً من فتحها كنافذة منفصلة
        entry_subtab = QWidget()
        entry_layout = QVBoxLayout(entry_subtab)
        entry_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للاستفادة من المساحة المتاحة

        # تهيئة نافذة مسك المخالفات المضمنة مباشرة في التبويب
        if VIOLATIONS_WINDOW_AVAILABLE:
            self.embedded_violations_window = None
            try:
                # إنشاء نافذة المخالفات مباشرة كعنصر في التبويب
                self.embedded_violations_window = sub11_module.ViolationsWindow(
                    db_path="data.db",
                    window_width=1000,  # تعديل الحجم ليناسب التبويب
                    window_height=1100,
                    parent=entry_subtab
                )

                # ضبط النافذة كعنصر واجهة وليس كنافذة مستقلة
                self.embedded_violations_window.setWindowFlags(Qt.Widget)

                # إخفاء شريط العنوان إذا كان موجوداً
                if hasattr(self.embedded_violations_window, 'title_label'):
                    self.embedded_violations_window.title_label.hide()

                # إخفاء العنوان إذا كان موجوداً
                if hasattr(self.embedded_violations_window, 'header_frame'):
                    self.embedded_violations_window.header_frame.hide()

                # إخفاء زر الإغلاق إذا كان موجوداً
                if hasattr(self.embedded_violations_window, 'close_button'):
                    self.embedded_violations_window.close_button.hide()

                # إضافة النافذة المضمنة إلى تخطيط التبويب الفرعي
                entry_layout.addWidget(self.embedded_violations_window)

                # إذا كان هناك رمز طالب متاح، يتم تحديث بيانات الطالب في النافذة المضمنة
                if self.current_record_id:
                    level = self.level_display.text()
                    class_name = self.class_display.text()
                    student_name = self.name_display.text()
                    rt = self.rt_display.text()
                    self.embedded_violations_window.set_student_info(
                        self.current_record_id,
                        student_name,
                        rt,
                        level,
                        class_name
                    )
            except Exception as e:
                print(f"خطأ في تضمين نافذة مسك المخالفات: {e}")
                import traceback
                traceback.print_exc()
                # في حالة حدوث خطأ، إنشاء رسالة توضيحية
                error_label = QLabel(f"تعذر تحميل نافذة مسك المخالفات: {str(e)}")
                error_label.setStyleSheet("color: red;")
                entry_layout.addWidget(error_label)
        else:
            # إضافة رسالة توضيحية إذا كانت النافذة غير متوفرة
            entry_info = QLabel("نافذة مسك المخالفات غير متوفرة")
            entry_info.setFont(QFont("Calibri", 11))
            entry_info.setStyleSheet("color: red;")
            entry_info.setAlignment(Qt.AlignCenter)
            entry_layout.addWidget(entry_info)

        # تبويب معالجة المخالفات - يعمل مباشرة عند النقر عليه
        management_subtab = QWidget()
        management_layout = QVBoxLayout(management_subtab)
        management_layout.setAlignment(Qt.AlignCenter)

        # إضافة رسالة توجيهية
        management_info = QLabel("انقر هنا لفتح نافذة معالجة المخالفات")
        management_info.setFont(QFont("Calibri", 11))
        management_info.setStyleSheet("color: #27ae60;")
        management_info.setAlignment(Qt.AlignCenter)
        management_layout.addWidget(management_info)

        # إضافة أيقونة (يمكن استبدالها بأيقونة مناسبة)
        management_icon = QPushButton()
        management_icon.setIcon(QIcon.fromTheme("document-edit", QIcon()))
        management_icon.setIconSize(QSize(48, 48))
        management_icon.setStyleSheet("background-color: transparent; border: none;")
        management_icon.setCursor(Qt.PointingHandCursor)
        management_icon.clicked.connect(self.open_violations_management)
        management_layout.addWidget(management_icon)

        # جعل التبويب بأكمله قابل للنقر
        management_subtab.mousePressEvent = lambda event: self.open_violations_management()
        management_subtab.setCursor(Qt.PointingHandCursor)

        # تم إزالة تبويب طباعة سجلات المخالفة

        # إضافة التبويبات الفرعية إلى تبويب المخالفات الرئيسي
        violations_subtabs.addTab(entry_subtab, "مسك المخالفات")
        violations_subtabs.addTab(management_subtab, "معالجة المخالفات")
        # تم إزالة تبويب طباعة السجلات

        # تعيين مؤشر اليد لجميع التبويبات الفرعية
        violations_subtabs.tabBar().setCursor(Qt.PointingHandCursor)

        # إضافة تحكم لتنفيذ الوظيفة المناسبة عند تغيير التبويب
        violations_subtabs.currentChanged.connect(self.handle_violations_subtab_change)

        # إضافة التبويبات الفرعية إلى تبويب المخالفات الرئيسي
        violations_tab_layout.addWidget(violations_subtabs)

        self.tab_widget.addTab(violations_tab, "المخالفات")

        # Add absence tab - with nested subtabs (similar to violations tab)
        absence_tab = QWidget()
        absence_tab_layout = QVBoxLayout(absence_tab)
        absence_tab_layout.setContentsMargins(5, 5, 5, 5)
        absence_tab_layout.setSpacing(5)

        # إضافة تبويبات فرعية داخل تبويب تبرير الغياب
        absence_subtabs = QTabWidget()
        absence_subtabs.setFont(QFont("Calibri", 13, QFont.Bold))
        absence_subtabs.setLayoutDirection(Qt.RightToLeft)
        absence_subtabs.setStyleSheet('''
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: rgba(255, 255, 255, 0.7);
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 5px 10px;
                margin-right: 2px;
                min-width: 150px;
                max-width: 150px;
                width: 150px;
                color: black;
                font-weight: bold;
                border-top-left-radius: 5px;
                cursor: pointer;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        ''')

        # تبويب مسك تبرير الغياب - دمج نافذة تبرير الغياب مباشرة
        entry_absence_subtab = QWidget()
        entry_absence_layout = QVBoxLayout(entry_absence_subtab)
        entry_absence_layout.setContentsMargins(0, 0, 0, 0)

        # تهيئة نافذة مسك تبرير الغياب المضمنة داخل التبويب
        if ABSENCE_WINDOW_AVAILABLE:
            # إنشاء متغير عضو لحفظ إشارة إلى نافذة تبرير الغياب المضمنة
            self.embedded_absence_window = None
            try:
                # إنشاء نافذة تبرير الغياب مباشرة كعنصر في التبويب وتخزينها في متغير عضو
                print("محاولة إنشاء نافذة تبرير الغياب المضمنة...")
                # قمنا بإزالة معامل db لأن الفصل لا يقبله
                self.embedded_absence_window = sub13_module.AbsenceJustificationWindow(
                    parent=entry_absence_subtab
                )
                print("تم إنشاء كائن نافذة تبرير الغياب بنجاح")

                # تعديل خصائص النافذة لتعمل كويدجت داخل التبويب
                if hasattr(self.embedded_absence_window, 'setWindowFlags'):
                    self.embedded_absence_window.setWindowFlags(Qt.Widget)

                # إخفاء العناصر غير الضرورية مثل زر الإغلاق أو العنوان إذا كانت موجودة
                if hasattr(self.embedded_absence_window, 'close_button'):
                    self.embedded_absence_window.close_button.hide()
                if hasattr(self.embedded_absence_window, 'title_label'):
                    self.embedded_absence_window.title_label.hide()

                # ضبط الحجم المناسب للنافذة المضمنة
                if hasattr(self.embedded_absence_window, 'setFixedSize'):
                    # يمكن ضبط الحجم وفقًا لحجم التبويب
                    self.embedded_absence_window.setFixedSize(1200, 500)

                # إضافة النافذة المضمنة إلى تخطيط التبويب الفرعي
                entry_absence_layout.addWidget(self.embedded_absence_window)
                print("تم إضافة نافذة تبرير الغياب كعنصر في التبويب بنجاح")

                # تأكد من أن واجهة المستخدم تظهر الآن
                QApplication.processEvents()

                # في حال وجود بيانات طالب حالي، قم بتحديثها في النافذة المضمنة
                if hasattr(self, 'current_record_id') and self.current_record_id:
                    print(f"تحديث بيانات التلميذ {self.current_record_id} في نافذة تبرير الغياب أثناء الإنشاء")
                    self.update_embedded_absence_window()

                print("تم تضمين نافذة تبرير الغياب بنجاح وتخزينها في متغير عضو")
            except Exception as e:
                print(f"خطأ في تضمين نافذة مسك تبرير الغياب: {e}")
                import traceback
                traceback.print_exc()
                # في حالة حدوث خطأ، إنشاء رسالة توضيحية
                error_label = QLabel(f"تعذر تحميل نافذة مسك تبرير الغياب: {str(e)}")
                error_label.setStyleSheet("color: red;")
                entry_absence_layout.addWidget(error_label)
        else:
            # إضافة رسالة توضيحية إذا كانت النافذة غير متوفرة
            entry_info = QLabel("نافذة مسك تبرير الغياب غير متوفرة")
            entry_info.setFont(QFont("Calibri", 11))
            entry_info.setStyleSheet("color: red;")
            entry_info.setAlignment(Qt.AlignCenter)
            entry_absence_layout.addWidget(entry_info)

        # تبويب معالجة تبرير الغياب - دمج نافذة معالجة تبرير الغياب مباشرة
        manage_absence_subtab = QWidget()
        manage_absence_layout = QVBoxLayout(manage_absence_subtab)
        manage_absence_layout.setContentsMargins(0, 0, 0, 0)

        # تهيئة نافذة معالجة تبرير الغياب المضمنة داخل التبويب
        if ABSENCE_MANAGEMENT_WINDOW_AVAILABLE:
            # إنشاء متغير عضو لحفظ إشارة إلى نافذة معالجة تبرير الغياب المضمنة
            self.embedded_absence_management_window = None
            try:
                # إنشاء نافذة معالجة تبرير الغياب مباشرة كعنصر في التبويب مع تمرير معرف التلميذ الحالي
                self.embedded_absence_management_window = sub14_module.AbsenceManagementWindow(
                    student_code=self.current_record_id if hasattr(self, 'current_record_id') else None,
                    parent=manage_absence_subtab,
                    db=self.external_db if hasattr(self, 'external_db') else None,
                    academic_year=self.current_academic_year if hasattr(self, 'current_academic_year') else None
                )

                # تعديل خصائص النافذة لتعمل كويدجت داخل التبويب
                if hasattr(self.embedded_absence_management_window, 'setWindowFlags'):
                    self.embedded_absence_management_window.setWindowFlags(Qt.Widget)

                # إخفاء زر الإغلاق إذا وجد
                if hasattr(self.embedded_absence_management_window, 'close_button') and self.embedded_absence_management_window.close_button:
                    self.embedded_absence_management_window.close_button.hide()

                # إضافة النافذة المضمنة إلى تخطيط التبويب الفرعي
                manage_absence_layout.addWidget(self.embedded_absence_management_window)

            except Exception as e:
                print(f"خطأ في تضمين نافذة معالجة تبرير الغياب: {e}")
                import traceback
                traceback.print_exc()
                # في حالة حدوث خطأ، إنشاء رسالة توضيحية
                error_label = QLabel(f"تعذر تحميل نافذة معالجة تبرير الغياب: {str(e)}")
                error_label.setStyleSheet("color: red;")
                manage_absence_layout.addWidget(error_label)
        else:
            # إضافة رسالة توضيحية إذا كانت النافذة غير متوفرة
            manage_info = QLabel("نافذة معالجة تبرير الغياب غير متوفرة")
            manage_info.setFont(QFont("Calibri", 11))
            manage_info.setStyleSheet("color: red;")
            manage_info.setAlignment(Qt.AlignCenter)
            manage_absence_layout.addWidget(manage_info)

        # إضافة التبويبات الفرعية إلى التبويب الأصلي
        absence_subtabs.addTab(entry_absence_subtab, "مسك تبرير الغياب")
        absence_subtabs.addTab(manage_absence_subtab, "معالجة تبرير الغياب")

        # تعيين مؤشر اليد لجميع التبويبات الفرعية
        absence_subtabs.tabBar().setCursor(Qt.PointingHandCursor)

        # إضافة التبويبات الفرعية إلى تبويب تبرير الغياب
        absence_tab_layout.addWidget(absence_subtabs)

        # إضافة تبويب تبرير الغياب إلى التبويب الرئيسي
        self.tab_widget.addTab(absence_tab, "تبرير الغياب")

        # إضافة تحكم لتنفيذ الوظيفة المناسبة عند تغيير التبويب الفرعي لتبرير الغياب
        absence_subtabs.currentChanged.connect(self.handle_absence_subtab_change)

        # Add parent visits tab - simplified with embedded directly in subtab
        visits_tab = QWidget()
        visits_tab_layout = QVBoxLayout(visits_tab)
        visits_tab_layout.setContentsMargins(5, 5, 5, 5)
        visits_tab_layout.setSpacing(5)

        # إضافة تبويبات فرعية داخل تبويب زيارة أولياء الأمور
        visits_subtabs = QTabWidget()
        visits_subtabs.setFont(QFont("Arial", 13, QFont.Bold))
        visits_subtabs.setLayoutDirection(Qt.RightToLeft)
        visits_subtabs.setStyleSheet('''
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: rgba(255, 255, 255, 0.7);
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 5px 10px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                cursor: pointer;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        ''')

        # التبويب الأول: توثيق زيارة جديدة - دمج واجهة ParentVisitWindow مباشرة
        new_visit_tab = QWidget()
        new_visit_layout = QVBoxLayout(new_visit_tab)
        new_visit_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للاستفادة من المساحة المتاحة

        # تحميل معلومات الطالب الحالي
        student_code = self.current_record_id if hasattr(self, 'current_record_id') else None
        student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
        student_level = self.level_display.text() if hasattr(self, 'level_display') else ""
        student_class = self.class_display.text() if hasattr(self, 'class_display') else ""
        student_rt = self.rt_display.text() if hasattr(self, 'rt_display') else ""

        if PARENT_VISIT_WINDOW_AVAILABLE:
            try:
                # إنشاء واجهة ParentVisitWindow مباشرة داخل التبويب
                self.embedded_parent_visit = ParentVisitWindow(
                    parent=new_visit_tab
                )

                # تعديل خصائص النافذة لتعمل كويدجت داخل التبويب
                self.embedded_parent_visit.setWindowFlags(Qt.Widget)

                # تأكد من أن النافذة مرئية
                self.embedded_parent_visit.setVisible(True)

                # إخفاء زر الإغلاق وعنوان النافذة لتناسب الواجهة المضمنة
                if hasattr(self.embedded_parent_visit, 'close_button'):
                    self.embedded_parent_visit.close_button.hide()

                # ضبط الحجم المناسب للواجهة المضمنة
                self.embedded_parent_visit.setFixedSize(1000, 500)

                # إضافة الواجهة المضمنة إلى تخطيط التبويب
                new_visit_layout.addWidget(self.embedded_parent_visit)

                # تعيين معلومات الطالب في الواجهة المضمنة - لابد من تأجيلها حتى يتم تحميل البيانات كاملة
                QApplication.processEvents()

                # تحديث بيانات الطالب في نافذة توثيق الزيارة
                self.update_embedded_parent_visit_window()

                print("تم دمج نافذة توثيق زيارة أولياء الأمور بنجاح داخل التبويب")

            except Exception as e:
                print(f"خطأ في دمج واجهة توثيق زيارة أولياء الأمور: {e}")
                import traceback
                traceback.print_exc()

                # إضافة رسالة توضيحية في حال فشل الدمج
                error_label = QLabel(f"تعذر تحميل واجهة توثيق الزيارات: {str(e)}")
                error_label.setStyleSheet("color: red;")
                error_label.setAlignment(Qt.AlignCenter)
                new_visit_layout.addWidget(error_label)
        else:
            # إضافة رسالة توضيحية إذا كانت النافذة غير متوفرة
            unavailable_label = QLabel("واجهة توثيق زيارة أولياء الأمور غير متوفرة")
            unavailable_label.setFont(QFont("Calibri", 11))
            unavailable_label.setStyleSheet("color: red;")
            unavailable_label.setAlignment(Qt.AlignCenter)
            new_visit_layout.addWidget(unavailable_label)

        # التبويب الثاني: استعراض سجل الزيارات
        view_visits_tab = QWidget()
        view_visits_layout = QVBoxLayout(view_visits_tab)
        view_visits_layout.setAlignment(Qt.AlignCenter)

        # إضافة رسالة توجيهية
        view_visits_info = QLabel("انقر على الزر أدناه لاستعراض سجل الزيارات")
        view_visits_info.setFont(QFont("Calibri", 11))
        view_visits_info.setStyleSheet("color: #27ae60;")
        view_visits_info.setAlignment(Qt.AlignCenter)
        view_visits_layout.addWidget(view_visits_info)

        # إضافة أيقونة
        view_visits_icon = QPushButton()
        view_visits_icon.setIcon(QIcon.fromTheme("view-list", QIcon()))
        view_visits_icon.setIconSize(QSize(48, 48))
        view_visits_icon.setStyleSheet("background-color: transparent; border: none;")
        view_visits_icon.setCursor(Qt.PointingHandCursor)
        view_visits_icon.clicked.connect(self.view_parent_visits)
        view_visits_layout.addWidget(view_visits_icon)

        # إزالة معالج حدث النقر وتعيين مؤشر الماوس العادي
        view_visits_tab.setCursor(Qt.ArrowCursor)

        # إضافة التبويبات الفرعية إلى تبويب الزيارات الرئيسي
        visits_subtabs.addTab(new_visit_tab, "توثيق زيارة جديدة")
        visits_subtabs.addTab(view_visits_tab, "استعراض سجل الزيارات")

        # تعيين مؤشر اليد لجميع التبويبات الفرعية
        visits_subtabs.tabBar().setCursor(Qt.PointingHandCursor)

        # إضافة تحكم لتنفيذ الوظيفة المناسبة عند تغيير التبويب الفرعي
        visits_subtabs.currentChanged.connect(self.handle_visits_subtab_change)

        # إضافة التبويبات الفرعية إلى تبويب الزيارات الرئيسي
        visits_tab_layout.addWidget(visits_subtabs)
        visits_tab_layout.addStretch()

        self.tab_widget.addTab(visits_tab, "زيارات أولياء الأمور")

        # إضافة التبويب إلى التخطيط الرئيسي - تمت إزالة أزرار التنقل
        layout.addWidget(self.tab_widget)

        # إضافة متغير لتخزين السنة الدراسية الحالية
        self.current_academic_year = None

        # Connect to database
        self.connect_to_database()

        # تحميل السنة الدراسية قبل تحميل البيانات
        self.load_current_academic_year()

        # Load initial data
        self.current_record_id = None
        self.load_first_record()

        # تعيين مؤشر اليد لجميع التبويبات - استخدام تأخير أطول للتأكد من تحميل جميع العناصر
        QTimer.singleShot(1000, self.set_hand_cursor_for_all_tabs)

        # إضافة مؤقت إضافي للتأكد من تطبيق مؤشر اليد بعد تحميل النافذة بالكامل
        QTimer.singleShot(2000, self.set_hand_cursor_for_all_tabs)

        # عرض النافذة بالحجم الكامل
        self.showMaximized()

    def connect_to_database(self):
        try:
            # استخدام قاعدة البيانات الخارجية إذا تم تمريرها
            if self.external_db and hasattr(self.external_db, 'isOpen') and self.external_db.isOpen():
                print("استخدام اتصال قاعدة البيانات الخارجي")
                self.conn = None  # لن نحتاج إلى اتصال SQLite
                self.cursor = None
                return

            # إنشاء اتصال جديد إذا لم يتم توفير اتصال خارجي
            self.conn = sqlite3.connect("data.db")
            self.cursor = self.conn.cursor()
            print("Connected to database successfully")
        except Exception as e:
            print(f"Error connecting to database: {e}")

    def load_current_academic_year(self):
        """تحميل السنة الدراسية الحالية من جدول بيانات_المؤسسة"""
        try:
            # استخدام السنة الدراسية الخارجية إذا تم تمريرها
            if self.external_academic_year:
                self.current_academic_year = self.external_academic_year
                print(f"تم استخدام السنة الدراسية الخارجية: {self.current_academic_year}")
                return

            # الحصول على السنة الدراسية الحالية من جدول بيانات_المؤسسة
            self.cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = self.cursor.fetchone()
            if (result and result[0]):
                self.current_academic_year = result[0]
                print(f"تم تحميل السنة الدراسية الحالية: {self.current_academic_year}")
            else:
                print("تحذير: لم يتم العثور على السنة الدراسية في جدول بيانات_المؤسسة")
        except Exception as e:
            print(f"خطأ في تحميل السنة الدراسية الحالية: {e}")

    def set_hand_cursor_for_all_tabs(self):
        """تعيين مؤشر اليد لجميع التبويبات"""
        try:
            # تعيين مؤشر اليد للتبويبات الرئيسية
            tab_bar = self.tab_widget.tabBar()
            tab_bar.setCursor(Qt.PointingHandCursor)

            # تعيين مؤشر اليد لجميع التبويبات الفرعية
            self.set_cursor_for_child_tabs(self)

            # طريقة بديلة: تعيين مؤشر اليد لجميع التبويبات في النافذة
            for widget in self.findChildren(QTabWidget):
                if widget and widget.tabBar():
                    widget.tabBar().setCursor(Qt.PointingHandCursor)
                    # تعيين مؤشر اليد لكل تبويب فرعي بشكل فردي
                    for i in range(widget.count()):
                        widget.tabBar().setTabData(i, Qt.PointingHandCursor)
                    print(f"تم تعيين مؤشر اليد لتبويب: {widget.objectName()}")

            # إصلاح مشكلة التبويبات الفرعية في تبويب المخالفات
            self.fix_violations_subtabs()

            print("تم تعيين مؤشر اليد لجميع التبويبات بنجاح")
        except Exception as e:
            print(f"خطأ في تعيين مؤشر اليد للتبويبات: {e}")

    def fix_violations_subtabs(self):
        """إصلاح مشكلة التبويبات الفرعية في تبويب المخالفات"""
        try:
            # البحث عن تبويب المخالفات (عادة التبويب الثالث)
            if self.tab_widget.count() > 2:
                violations_tab = self.tab_widget.widget(2)
                if violations_tab and violations_tab.layout():
                    # البحث عن التبويبات الفرعية في تبويب المخالفات
                    for i in range(violations_tab.layout().count()):
                        item = violations_tab.layout().itemAt(i)
                        if item and item.widget() and isinstance(item.widget(), QTabWidget):
                            subtab_widget = item.widget()

                            # تعيين مؤشر اليد للتبويبات الفرعية
                            subtab_widget.tabBar().setCursor(Qt.PointingHandCursor)

                            # إصلاح مشكلة النقر المزدوج عن طريق تعديل إشارات النقر
                            if not hasattr(subtab_widget, '_click_handler_fixed'):
                                # تعديل سلوك النقر على التبويبات
                                tab_bar = subtab_widget.tabBar()

                                # تعديل ستايل التبويبات لتحسين الاستجابة
                                tab_bar.setStyleSheet("""
                                    QTabBar::tab {
                                        background-color: #c0d8e8;
                                        padding: 6px 12px;
                                        margin-right: 2px;
                                        color: black;
                                        font-weight: bold;
                                        cursor: pointer;
                                    }
                                    QTabBar::tab:selected {
                                        background-color: #3498db;
                                        color: white;
                                    }
                                    QTabBar::tab:hover {
                                        background-color: #a7c7e7;
                                    }
                                """)

                                # تعديل سلوك التبويبات عن طريق تعديل الإشارات
                                if not hasattr(subtab_widget, 'tabBarClicked'):
                                    # إذا لم تكن الإشارة موجودة، نستخدم طريقة بديلة
                                    # حفظ الدالة الأصلية للتبديل بين التبويبات
                                    original_tab_change = subtab_widget.setCurrentIndex

                                    def enhanced_tab_change(index):
                                        """دالة محسنة لتغيير التبويب تعمل من أول نقرة"""
                                        # استدعاء الدالة الأصلية
                                        original_tab_change(index)
                                        # تنفيذ أي منطق إضافي مطلوب عند تغيير التبويب
                                        print(f"تم تغيير التبويب الفرعي إلى {index}")
                                        # إذا كان هناك دالة معالجة للتبويبات الفرعية، استدعها
                                        if hasattr(self, 'handle_violations_subtab_change'):
                                            self.handle_violations_subtab_change(index)

                                    # استبدال الدالة الأصلية بالدالة المحسنة
                                    subtab_widget.setCurrentIndex = enhanced_tab_change
                                else:
                                    # استخدام إشارة النقر على التبويب مباشرة
                                    subtab_widget.tabBarClicked.connect(lambda index: subtab_widget.setCurrentIndex(index))

                                # وضع علامة على التبويب بأنه تم إصلاحه
                                subtab_widget._click_handler_fixed = True

                                print(f"تم إصلاح مشكلة النقر المزدوج للتبويبات الفرعية في تبويب المخالفات")
        except Exception as e:
            print(f"خطأ في إصلاح التبويبات الفرعية للمخالفات: {e}")

    def set_cursor_for_child_tabs(self, parent_widget):
        """تعيين مؤشر اليد لجميع التبويبات الفرعية بشكل متكرر"""
        try:
            if not parent_widget or not parent_widget.layout():
                return

            # البحث في جميع العناصر في التخطيط
            for i in range(parent_widget.layout().count()):
                item = parent_widget.layout().itemAt(i)
                if not item:
                    continue

                # إذا كان العنصر تبويب، قم بتعيين مؤشر اليد له
                if item.widget() and isinstance(item.widget(), QTabWidget):
                    tab_widget = item.widget()
                    tab_widget.tabBar().setCursor(Qt.PointingHandCursor)
                    # تعيين مؤشر اليد لكل تبويب فرعي بشكل فردي
                    for j in range(tab_widget.count()):
                        tab_widget.tabBar().setTabData(j, Qt.PointingHandCursor)
                    print(f"تم تعيين مؤشر اليد لتبويب فرعي: {tab_widget.objectName()}")

                # إذا كان العنصر ويدجت عادي، ابحث داخله عن تبويبات
                elif item.widget():
                    self.set_cursor_for_child_tabs(item.widget())
        except Exception as e:
            print(f"خطأ في تعيين مؤشر اليد للتبويبات الفرعية: {e}")

    def load_first_record(self):
        """تحميل أول سجل من قاعدة البيانات"""
        try:
            result_id = None
            base_query = """
                SELECT DISTINCT s.الرمز
                FROM السجل_العام s
                JOIN اللوائح l ON s.الرمز = l.الرمز
            """
            order_limit = " ORDER BY s.الرمز ASC LIMIT 1"
            where_clause = ""
            params = []

            if hasattr(self, 'current_academic_year') and self.current_academic_year:
                where_clause = " WHERE l.السنة_الدراسية = ?"
                params.append(self.current_academic_year)

            full_query = base_query + where_clause + order_limit

            # --- >> تعديل: استخدام QSqlQuery أو sqlite3 cursor << ---
            if hasattr(self, 'db') and self.db and self.db.isOpen():
                print("StudentCardWindow: استخدام QSqlQuery لتحميل أول سجل")
                query = QSqlQuery(self.db)
                # QSqlQuery uses named placeholders or positional ?
                if hasattr(self, 'current_academic_year') and self.current_academic_year:
                    query.prepare(base_query + " WHERE l.السنة_الدراسية = :year" + order_limit)
                    query.bindValue(":year", self.current_academic_year)
                else:
                    query.prepare(base_query + order_limit)

                if query.exec_() and query.next():
                    result_id = query.value(0)
            elif hasattr(self, 'conn') and hasattr(self, 'cursor') and self.conn and self.cursor:
                print("StudentCardWindow: استخدام sqlite3 لتحميل أول سجل")
                self.cursor.execute(full_query, params)
                result = self.cursor.fetchone()
                if result:
                    result_id = result[0]
            # --- >> نهاية التعديل << ---

            if result_id:
                self.load_student_data(result_id)
            else:
                print("لا توجد سجلات للعرض.") # Handle case with no records

        except Exception as e:
            print(f"Error loading first record: {e}")
            traceback.print_exc() # Print detailed traceback

    def load_student_data(self, student_id):
        try:
            print(f"تحميل بيانات الطالب: {student_id}")
            current_year = self.current_academic_year
            print(f"السنة الدراسية الحالية للتصفية: {current_year}")

            # Reset UI fields
            self.code_field.clear()
            self.name_field.clear()
            self.code_display.clear()
            self.name_display.clear()
            self.gender_display.clear()
            self.birth_date_display.clear()
            self.birth_place_display.clear()
            self.phone1_field.clear()
            self.phone2_field.clear()
            self.notes_field.clear()
            self.school_year_display.clear()
            self.class_display.clear()
            self.level_display.clear()
            self.rt_display.clear()
            QApplication.processEvents()

            general_data = None
            school_data = None

            # --- >> تعديل: استخدام QSqlQuery أو sqlite3 cursor << ---
            if self.db and self.db.isOpen():
                # استخدام QSqlDatabase
                print("StudentCardWindow: استخدام QSqlQuery لتحميل البيانات العامة")
                query_general = QSqlQuery(self.db)
                query_general.prepare("""
                    SELECT الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد, مكان_الازدياد,
                           الرمز_السري, الهاتف_الأول, الهاتف_الثاني, ملاحظات
                    FROM السجل_العام
                    WHERE الرمز = :code
                """)
                query_general.bindValue(":code", student_id)
                if query_general.exec_() and query_general.next():
                    general_data = [query_general.value(i) for i in range(query_general.record().count())]

                print("StudentCardWindow: استخدام QSqlQuery لتحميل بيانات التمدرس")
                query_school = QSqlQuery(self.db)
                if current_year:
                    query_school.prepare("""
                        SELECT السنة_الدراسية, القسم, المستوى, رت
                        FROM اللوائح
                        WHERE الرمز = :code AND السنة_الدراسية = :year
                        LIMIT 1
                    """)
                    query_school.bindValue(":code", student_id)
                    query_school.bindValue(":year", current_year)
                else:
                     query_school.prepare("""
                        SELECT السنة_الدراسية, القسم, المستوى, رت
                        FROM اللوائح
                        WHERE الرمز = :code
                        ORDER BY السنة_الدراسية DESC
                        LIMIT 1
                    """)
                     query_school.bindValue(":code", student_id)

                if query_school.exec_() and query_school.next():
                    school_data = [query_school.value(i) for i in range(query_school.record().count())]

            elif self.conn and self.cursor:
                # استخدام sqlite3
                print("StudentCardWindow: استخدام sqlite3 لتحميل البيانات العامة")
                self.cursor.execute("""
                    SELECT الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد, مكان_الازدياد,
                           الرمز_السري, الهاتف_الأول, الهاتف_الثاني, ملاحظات
                    FROM السجل_العام
                    WHERE الرمز = ?
                """, (student_id,))
                general_data = self.cursor.fetchone()

                print("StudentCardWindow: استخدام sqlite3 لتحميل بيانات التمدرس")
                if current_year:
                    self.cursor.execute("""
                        SELECT السنة_الدراسية, القسم, المستوى, رت
                        FROM اللوائح
                        WHERE الرمز = ? AND السنة_الدراسية = ?
                        LIMIT 1
                    """, (student_id, current_year))
                else:
                    self.cursor.execute("""
                        SELECT السنة_الدراسية, القسم, المستوى, رت
                        FROM اللوائح
                        WHERE الرمز = ?
                        ORDER BY السنة_الدراسية DESC
                        LIMIT 1
                    """, (student_id,))
                school_data = self.cursor.fetchone()
            # --- >> نهاية التعديل << ---

            if general_data:
                self.current_record_id = student_id
                # تعيين البيانات العامة وطباعتها للتشخيص
                code_value = str(general_data[0] or "")
                name_value = str(general_data[1] or "")
                gender_value = str(general_data[2] or "")
                birth_date_value = str(general_data[3] or "")
                birth_place_value = str(general_data[4] or "")
                phone1_value = str(general_data[6] or "")
                phone2_value = str(general_data[7] or "")
                notes_value = str(general_data[8] or "")

                print(f"البيانات العامة: الرمز={code_value}, الاسم={name_value}")

                # تعديل: تعيين النص المدمج في حقول الواجهة
                self.code_field.setText(f"الرمز: {code_value}")
                self.name_field.setText(f"الاسم والنسب: {name_value}")

                # بقية الحقول كما هي
                self.code_display.setText(code_value)
                self.name_display.setText(name_value)
                self.gender_display.setText(gender_value)
                self.birth_date_display.setText(birth_date_value)
                self.birth_place_display.setText(birth_place_value)
                self.phone1_field.setText(phone1_value)
                self.phone2_field.setText(phone2_value)
                self.notes_field.setText(notes_value)

                # تحديث واجهة المستخدم بعد تعيين البيانات العامة
                QApplication.processEvents()

                if school_data:
                    print(f"تم العثور على بيانات التمدرس: {school_data}")

                    # حفظ القيم في متغيرات
                    school_year = str(school_data[0] or "")
                    class_value = str(school_data[1] or "")
                    level_value = str(school_data[2] or "")
                    rt_value = str(school_data[3] or "")

                    print(f"القيم المستخرجة: السنة={school_year}, القسم={class_value}, المستوى={level_value}, رت={rt_value}")

                    # إظهار البيانات في واجهة المستخدم بشكل مؤكد
                    self.school_year_display.setText(school_year)
                    self.class_display.setText(class_value)
                    self.level_display.setText(level_value)
                    self.rt_display.setText(rt_value)

                    # إضافة تأكيد مرئي على تغيير البيانات
                    self.school_year_display.setStyleSheet("background-color: #e6ffe6;")
                    self.class_display.setStyleSheet("background-color: #e6ffe6;")
                    self.level_display.setStyleSheet("background-color: #e6ffe6;")
                    self.rt_display.setStyleSheet("background-color: #e6ffe6;")

                    # تحديث واجهة المستخدم مرة أخرى بعد تعيين بيانات التمدرس
                    QApplication.processEvents()

                    print(f"القيم النهائية المعروضة: السنة={self.school_year_display.text()}, "
                          f"القسم={self.class_display.text()}, المستوى={self.level_display.text()}, "
                          f"رت={self.rt_display.text()}")
                else:
                    print(f"لم يتم العثور على بيانات تمدرس للطالب {student_id}")
                    # مسح بيانات التمدرس وتحديث التنسيق لتوضيح الحقول الفارغة
                    self.school_year_display.clear()
                    self.class_display.clear()
                    self.level_display.clear()
                    self.rt_display.clear()
                    self.school_year_display.setStyleSheet("background-color: #ffeeee;")
                    self.class_display.setStyleSheet("background-color: #ffeeee;")
                    self.level_display.setStyleSheet("background-color: #ffeeee;")
                    self.rt_display.setStyleSheet("background-color: #ffeeee;")

                # تحميل سجلات ورقة السماح بالدخول
                self.load_entry_records()

                # تحديث بيانات التلميذ في نافذة المخالفات المضمنة إذا كانت متاحة
                self.update_embedded_violations_window()

                # تحديث بيانات التلميذ في نافذة تبرير الغياب المضمنة إذا كانت متاحة
                self.update_embedded_absence_window()

                # تحديث بيانات التلميذ في نافذة معالجة تبرير الغياب المضمنة إذا كانت متاحة
                self.update_embedded_absence_management_window()

                # تحديث بيانات التلميذ في نافذة توثيق زيارة أولياء الأمور
                self.update_embedded_parent_visit_window()

                # تحديث بيانات التلميذ في نافذة معالجة زيارات أولياء الأمور المضمنة
                if hasattr(self, 'embedded_parent_visit_management'):
                    self.update_embedded_visit_management_student_data()

            else:
                print(f"لم يتم العثور على بيانات عامة للطالب {student_id}")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الطالب: {e}")
            traceback.print_exc()

    def update_embedded_violations_window(self):
        """تحديث بيانات التلميذ في نافذة المخالفات المضمنة"""
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        QTimer.singleShot(10, self._delayed_update_embedded_violations_window)

    def _delayed_update_embedded_violations_window(self):
        """تنفيذ تحديث بيانات التلميذ في نافذة المخالفات المضمنة بعد تأخير قصير"""
        try:
            # التحقق من وجود النافذة المضمنة وأنها تم إنشاؤها بنجاح
            if hasattr(self, 'embedded_violations_window') and self.embedded_violations_window:
                # التأكد من وجود جميع البيانات المطلوبة
                if hasattr(self, 'current_record_id') and self.current_record_id:
                    # استخراج بيانات التلميذ من الواجهة
                    level = self.level_display.text() if hasattr(self, 'level_display') else ""
                    class_name = self.class_display.text() if hasattr(self, 'class_display') else ""
                    student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
                    rt = self.rt_display.text() if hasattr(self, 'rt_display') else ""

                    # التحقق من توفر وسيلة لتعيين معلومات التلميذ
                    if hasattr(self.embedded_violations_window, 'set_student_info'):
                        # تعيين معلومات التلميذ في نافذة المخالفات
                        print(f"تحديث بيانات التلميذ في نافذة المخالفات: {self.current_record_id}, {student_name}")
                        self.embedded_violations_window.set_student_info(
                            self.current_record_id,
                            student_name,
                            rt,
                            level,
                            class_name
                        )

                        # تنفيذ تحديثات البيانات في مهام منفصلة مع تأخير قصير بينها
                        # لتحسين استجابة واجهة المستخدم

                        # تحميل بيانات المواد والأساتذة - إذا كانت الدالة متوفرة
                        if hasattr(self.embedded_violations_window, 'load_subjects_and_teachers'):
                            QTimer.singleShot(50, lambda: self._load_subjects_and_teachers())

                        # تحميل القوائم المنسدلة للتصنيفات إذا كانت متوفرة
                        if hasattr(self.embedded_violations_window, 'load_violation_categories'):
                            QTimer.singleShot(100, lambda: self._load_violation_categories())

                        # تحديث الواجهة بعد تعيين البيانات
                        if hasattr(self.embedded_violations_window, 'update_ui'):
                            QTimer.singleShot(150, lambda: self._update_violations_ui())
                    else:
                        print("نافذة المخالفات المضمنة لا تحتوي على دالة set_student_info")
                else:
                    print("بيانات التلميذ غير مكتملة لتحديث نافذة المخالفات")
        except Exception as e:
            print(f"خطأ في تحديث بيانات التلميذ في نافذة المخالفات: {e}")
            traceback.print_exc()

    def _load_subjects_and_teachers(self):
        """تحميل بيانات المواد والأساتذة في نافذة المخالفات"""
        try:
            if hasattr(self, 'embedded_violations_window') and self.embedded_violations_window:
                if hasattr(self.embedded_violations_window, 'load_subjects_and_teachers'):
                    self.embedded_violations_window.load_subjects_and_teachers()
                    print("تم تحميل بيانات المواد والأساتذة في نافذة المخالفات")
        except Exception as e:
            print(f"خطأ في تحميل بيانات المواد والأساتذة: {e}")

    def _load_violation_categories(self):
        """تحميل تصنيفات المخالفات في نافذة المخالفات"""
        try:
            if hasattr(self, 'embedded_violations_window') and self.embedded_violations_window:
                if hasattr(self.embedded_violations_window, 'load_violation_categories'):
                    self.embedded_violations_window.load_violation_categories()
                    print("تم تحميل تصنيفات المخالفات في نافذة المخالفات")
        except Exception as e:
            print(f"خطأ في تحميل تصنيفات المخالفات: {e}")

    def _update_violations_ui(self):
        """تحديث واجهة نافذة المخالفات"""
        try:
            if hasattr(self, 'embedded_violations_window') and self.embedded_violations_window:
                if hasattr(self.embedded_violations_window, 'update_ui'):
                    self.embedded_violations_window.update_ui()
                    print("تم تحديث واجهة نافذة المخالفات")
        except Exception as e:
            print(f"خطأ في تحديث واجهة نافذة المخالفات: {e}")

    def update_embedded_absence_window(self):
        """تحديث بيانات التلميذ في نافذة تبرير الغياب المضمنة"""
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        QTimer.singleShot(10, self._delayed_update_embedded_absence_window)

    def _delayed_update_embedded_absence_window(self):
        """تنفيذ تحديث بيانات التلميذ في نافذة تبرير الغياب المضمنة بعد تأخير قصير"""
        try:
            # التحقق من وجود النافذة المضمنة
            if not hasattr(self, 'embedded_absence_window') or not self.embedded_absence_window:
                print("نافذة تبرير الغياب غير موجودة")
                return

            # التحقق من وجود بيانات التلميذ المطلوبة
            if not hasattr(self, 'current_record_id') or not self.current_record_id:
                print("تنبيه: لم يتم تحديد تلميذ حالي لتحديث نافذة تبرير الغياب")
                return

            # التحقق من وجود دالة set_student_info في نافذة تبرير الغياب المضمنة
            if not hasattr(self.embedded_absence_window, 'set_student_info'):
                print("تنبيه: نافذة تبرير الغياب المضمنة لا تحتوي على دالة set_student_info")
                return

            # تنفيذ تحديثات البيانات في مهام منفصلة مع تأخير قصير بينها
            # لتحسين استجابة واجهة المستخدم

            # تحديث معلومات الطالب
            QTimer.singleShot(10, self._update_absence_student_info)

            # تحديث قوائم البيانات في نافذة تبرير الغياب إذا كانت متوفرة
            if hasattr(self.embedded_absence_window, 'load_absence_data'):
                QTimer.singleShot(50, self._load_absence_data)

            # تحديث الواجهة كاملة
            if hasattr(self.embedded_absence_window, 'update_ui'):
                QTimer.singleShot(100, self._update_absence_ui)

        except Exception as e:
            print(f"خطأ في تحديث بيانات التلميذ في نافذة تبرير الغياب: {e}")
            traceback.print_exc()

    def _update_absence_student_info(self):
        """تحديث معلومات الطالب في نافذة تبرير الغياب"""
        try:
            # استخراج بيانات التلميذ
            level = self.level_display.text() if hasattr(self, 'level_display') else ""
            class_name = self.class_display.text() if hasattr(self, 'class_display') else ""
            student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
            rt = self.rt_display.text() if hasattr(self, 'rt_display') else ""

            print(f"تحديث بيانات التلميذ في نافذة تبرير الغياب: {self.current_record_id}, {student_name}")

            # تحديث معلومات الطالب في نافذة تبرير الغياب
            self.embedded_absence_window.set_student_info(
                code=self.current_record_id,
                name=student_name,
                id_num=rt,
                level=level,
                class_name=class_name
            )

            print("تم تحديث بيانات التلميذ في نافذة تبرير الغياب بنجاح")

        except Exception as e:
            print(f"خطأ في تحديث معلومات الطالب في نافذة تبرير الغياب: {e}")
            traceback.print_exc()

    def _load_absence_data(self):
        """تحميل بيانات الغياب في نافذة تبرير الغياب"""
        try:
            if hasattr(self, 'embedded_absence_window') and self.embedded_absence_window:
                if hasattr(self.embedded_absence_window, 'load_absence_data'):
                    self.embedded_absence_window.load_absence_data()
                    print("تم تحديث بيانات الغياب في نافذة تبرير الغياب")
        except Exception as e:
            print(f"خطأ في تحميل بيانات الغياب: {e}")

    def _update_absence_ui(self):
        """تحديث واجهة نافذة تبرير الغياب"""
        try:
            if hasattr(self, 'embedded_absence_window') and self.embedded_absence_window:
                if hasattr(self.embedded_absence_window, 'update_ui'):
                    self.embedded_absence_window.update_ui()
                    print("تم تحديث واجهة نافذة تبرير الغياب")
        except Exception as e:
            print(f"خطأ في تحديث واجهة نافذة تبرير الغياب: {e}")

    def update_embedded_absence_management_window(self):
        """تحديث بيانات التلميذ في نافذة معالجة تبرير الغياب المضمنة"""
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        QTimer.singleShot(10, self._delayed_update_embedded_absence_management_window)

    def _delayed_update_embedded_absence_management_window(self):
        """تنفيذ تحديث بيانات التلميذ في نافذة معالجة تبرير الغياب المضمنة بعد تأخير قصير"""
        try:
            # التحقق من وجود النافذة المضمنة
            if not hasattr(self, 'embedded_absence_management_window') or not self.embedded_absence_management_window:
                # إذا لم تكن النافذة موجودة، قم بإنشائها
                print("نافذة معالجة تبرير الغياب غير موجودة، سيتم إنشاؤها")
                QTimer.singleShot(10, self.create_embedded_absence_management_window)
                return

            # التحقق من وجود بيانات التلميذ المطلوبة
            if not hasattr(self, 'current_record_id') or not self.current_record_id:
                print("تنبيه: لم يتم تحديد تلميذ حالي لتحديث نافذة معالجة تبرير الغياب")
                return

            # تحديث معرف التلميذ في النافذة المضمنة
            print(f"تحديث بيانات التلميذ في نافذة معالجة تبرير الغياب: {self.current_record_id}")

            # تحديث رمز الطالب في النافذة المضمنة
            if hasattr(self.embedded_absence_management_window, 'student_code'):
                self.embedded_absence_management_window.student_code = self.current_record_id
                print(f"تم تحديث رمز الطالب في نافذة معالجة تبرير الغياب: {self.current_record_id}")

            # تنفيذ تحديثات البيانات في مهام منفصلة مع تأخير قصير بينها
            # لتحسين استجابة واجهة المستخدم

            # تحديث عنوان النافذة
            QTimer.singleShot(50, self._update_absence_management_window_title)

            # تحديث البيانات باستخدام الطرق المختلفة المتاحة
            QTimer.singleShot(100, self._update_absence_management_data)

        except Exception as e:
            print(f"خطأ في تحديث بيانات التلميذ في نافذة معالجة تبرير الغياب: {e}")
            traceback.print_exc()

    def _update_absence_management_window_title(self):
        """تحديث عنوان نافذة معالجة تبرير الغياب"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                # تحديث عنوان النافذة
                if hasattr(self.embedded_absence_management_window, 'setWindowTitle'):
                    student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
                    title = f"معالجة تبرير الغياب: {student_name} ({self.current_record_id})"
                    self.embedded_absence_management_window.setWindowTitle(title)
                    print(f"تم تحديث عنوان نافذة معالجة تبرير الغياب: {title}")
        except Exception as e:
            print(f"خطأ في تحديث عنوان نافذة معالجة تبرير الغياب: {e}")

    def _update_absence_management_data(self):
        """تحديث بيانات نافذة معالجة تبرير الغياب باستخدام الطرق المختلفة المتاحة"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                # محاولة استخدام الطرق المختلفة بالترتيب

                # الطريقة 1: تحديث البيانات إذا كانت هناك دالة تحميل البيانات
                if hasattr(self.embedded_absence_management_window, 'load_data'):
                    QTimer.singleShot(10, self._try_load_absence_management_data)
                    return

                # الطريقة 2: استخدام دالة تصفية خاصة إذا وجدت
                if hasattr(self.embedded_absence_management_window, 'filter_by_student_code'):
                    QTimer.singleShot(10, self._try_filter_absence_by_student_code)
                    return

                # الطريقة 3: استخدام دالة تحديث مخصصة للطالب
                if hasattr(self.embedded_absence_management_window, 'update_data_for_student'):
                    QTimer.singleShot(10, self._try_update_absence_data_for_student)
                    return

                # الطريقة 4: تحديث واجهة المستخدم
                if hasattr(self.embedded_absence_management_window, 'update_ui'):
                    QTimer.singleShot(10, self._try_update_absence_management_ui)
                    return

                # الطريقة 5: طريقة مخصصة لتمرير معرّف الطالب
                if hasattr(self.embedded_absence_management_window, 'set_student_filter'):
                    QTimer.singleShot(10, self._try_set_absence_student_filter)
                    return

                # إذا لم نجد أي طريقة مناسبة، نطبع رسالة تنبيه
                print("تنبيه: لم يتم العثور على طريقة مناسبة لتحديث بيانات الطالب في نافذة معالجة تبرير الغياب")

                # محاولة أخيرة: إعادة إنشاء النافذة مباشرة مع تمرير معرف الطالب
                # هذا حل متطرف ويجب استخدامه فقط في حالة عدم وجود طريقة أخرى
                QTimer.singleShot(50, self._try_recreate_absence_management_window)
        except Exception as e:
            print(f"خطأ في تحديث بيانات نافذة معالجة تبرير الغياب: {e}")
            traceback.print_exc()

    def _try_load_absence_management_data(self):
        """محاولة تحميل بيانات معالجة تبرير الغياب باستخدام دالة load_data"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                if hasattr(self.embedded_absence_management_window, 'load_data'):
                    try:
                        # نحاول استدعاء load_data مع معامل student_code
                        self.embedded_absence_management_window.load_data(student_code=self.current_record_id)
                        print(f"تم تحديث بيانات التلميذ {self.current_record_id} باستخدام load_data مع معامل")
                    except TypeError:
                        # إذا لم تقبل معامل، نستدعيها بدون معاملات
                        self.embedded_absence_management_window.load_data()
                        print(f"تم تحديث بيانات التلميذ {self.current_record_id} باستخدام load_data")
        except Exception as e:
            print(f"خطأ في محاولة تحميل بيانات معالجة تبرير الغياب: {e}")

    def _try_filter_absence_by_student_code(self):
        """محاولة تصفية بيانات معالجة تبرير الغياب حسب رمز التلميذ"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                if hasattr(self.embedded_absence_management_window, 'filter_by_student_code'):
                    self.embedded_absence_management_window.filter_by_student_code(self.current_record_id)
                    print(f"تمت تصفية بيانات تبرير الغياب للتلميذ: {self.current_record_id}")
        except Exception as e:
            print(f"خطأ في محاولة تصفية بيانات معالجة تبرير الغياب: {e}")

    def _try_update_absence_data_for_student(self):
        """محاولة تحديث بيانات معالجة تبرير الغياب للتلميذ"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                if hasattr(self.embedded_absence_management_window, 'update_data_for_student'):
                    self.embedded_absence_management_window.update_data_for_student(self.current_record_id)
                    print(f"تم تحديث بيانات تبرير الغياب للتلميذ: {self.current_record_id}")
        except Exception as e:
            print(f"خطأ في محاولة تحديث بيانات معالجة تبرير الغياب للتلميذ: {e}")

    def _try_update_absence_management_ui(self):
        """محاولة تحديث واجهة نافذة معالجة تبرير الغياب"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                if hasattr(self.embedded_absence_management_window, 'update_ui'):
                    self.embedded_absence_management_window.update_ui()
                    print(f"تم تحديث واجهة نافذة معالجة تبرير الغياب")
        except Exception as e:
            print(f"خطأ في محاولة تحديث واجهة نافذة معالجة تبرير الغياب: {e}")

    def _try_set_absence_student_filter(self):
        """محاولة تعيين تصفية التلميذ في نافذة معالجة تبرير الغياب"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                if hasattr(self.embedded_absence_management_window, 'set_student_filter'):
                    self.embedded_absence_management_window.set_student_filter(self.current_record_id)
                    print(f"تم تعيين تصفية الطالب إلى: {self.current_record_id}")
        except Exception as e:
            print(f"خطأ في محاولة تعيين تصفية التلميذ: {e}")

    def create_embedded_absence_management_window(self):
        """إنشاء نافذة معالجة تبرير الغياب المضمنة"""
        try:
            # التحقق من وجود معرف طالب محدد
            if not hasattr(self, 'current_record_id') or not self.current_record_id:
                print("لا يوجد معرف طالب محدد لإنشاء نافذة معالجة تبرير الغياب")
                return

            # طباعة معلومات تشخيصية
            print(f"بدء إنشاء نافذة معالجة تبرير الغياب للطالب: {self.current_record_id}")

            # البحث عن تبويب تبرير الغياب والتبويب الفرعي "معالجة تبرير الغياب"
            absence_tab_index = -1
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "تبرير الغياب":
                    absence_tab_index = i
                    absence_tab = self.tab_widget.widget(i)
                    print(f"تم العثور على تبويب تبرير الغياب بالمؤشر {i}")
                    break

            if absence_tab_index == -1:
                print("لم يتم العثور على تبويب تبرير الغياب")
                return

            # الانتقال إلى تبويب تبرير الغياب
            self.tab_widget.setCurrentIndex(absence_tab_index)

            # استخدام تأخير قصير قبل البحث عن التبويبات الفرعية
            QTimer.singleShot(50, lambda: self._find_and_setup_absence_subtabs(absence_tab))

        except Exception as e:
            print(f"خطأ في إنشاء نافذة معالجة تبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_management_error(str(e))

    def _find_and_setup_absence_subtabs(self, absence_tab):
        """البحث عن التبويبات الفرعية لتبرير الغياب وإعدادها"""
        try:
            # البحث عن التبويبات الفرعية داخل تبويب تبرير الغياب
            manage_absence_subtab = None
            absence_subtabs = None

            # البحث عن التبويبات الفرعية
            for i in range(absence_tab.layout().count()):
                widget = absence_tab.layout().itemAt(i).widget()
                if isinstance(widget, QTabWidget):
                    absence_subtabs = widget
                    print(f"تم العثور على تبويبات فرعية لتبرير الغياب بـ {widget.count()} تبويب")
                    break

            if not absence_subtabs:
                print("لم يتم العثور على تبويبات فرعية لتبرير الغياب")
                return

            # البحث عن تبويب معالجة تبرير الغياب
            manage_absence_tab_index = -1
            for j in range(absence_subtabs.count()):
                if absence_subtabs.tabText(j) == "معالجة تبرير الغياب":
                    manage_absence_tab_index = j
                    manage_absence_subtab = absence_subtabs.widget(j)
                    print(f"تم العثور على تبويب معالجة تبرير الغياب بالمؤشر {j}")
                    break

            if manage_absence_tab_index == -1 or not manage_absence_subtab:
                print("لم يتم العثور على تبويب معالجة تبرير الغياب")
                return

            # الانتقال إلى تبويب معالجة تبرير الغياب
            absence_subtabs.setCurrentIndex(manage_absence_tab_index)

            # استخدام تأخير قصير قبل تنظيف وإعداد التخطيط
            QTimer.singleShot(50, lambda: self._setup_absence_management_layout(manage_absence_subtab))

        except Exception as e:
            print(f"خطأ في البحث عن التبويبات الفرعية لتبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_management_error(str(e))

    def _setup_absence_management_layout(self, manage_absence_subtab):
        """إعداد تخطيط نافذة معالجة تبرير الغياب"""
        try:
            # التحقق من وجود تخطيط سابق وتنظيفه
            current_layout = manage_absence_subtab.layout()

            # إذا كان هناك تخطيط سابق، نحذف كل العناصر فيه
            if current_layout:
                # حذف العناصر من التخطيط السابق - بطريقة آمنة
                while current_layout.count():
                    item = current_layout.takeAt(0)
                    widget = item.widget()
                    # تحقق من أن العنصر ليس None قبل محاولة حذفه
                    if widget:
                        widget.hide()  # إخفاء العنصر أولاً
                        widget.setParent(None)  # فصله عن الأب
                        widget.deleteLater()  # جدولة حذفه

                # استخدام التخطيط الحالي بعد تنظيفه
                absence_layout = current_layout
            else:
                # إنشاء تخطيط جديد إذا لم يكن هناك تخطيط
                absence_layout = QVBoxLayout()
                manage_absence_subtab.setLayout(absence_layout)

            # استخدام تأخير قصير قبل إنشاء النافذة المضمنة
            QTimer.singleShot(50, lambda: self._create_absence_management_window(manage_absence_subtab, absence_layout))

        except Exception as e:
            print(f"خطأ في إعداد تخطيط نافذة معالجة تبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_management_error(str(e))

    def _create_absence_management_window(self, manage_absence_subtab, absence_layout):
        """إنشاء نافذة معالجة تبرير الغياب"""
        try:
            # التحقق من وجود وحدة sub14_module
            if 'sub14_module' not in globals():
                print("وحدة sub14_module غير متوفرة")
                # إضافة رسالة توضيحية
                info_label = QLabel("نافذة معالجة تبرير الغياب غير متوفرة")
                info_label.setFont(QFont("Arial", 12))
                info_label.setStyleSheet("color: red; font-weight: bold;")
                info_label.setAlignment(Qt.AlignCenter)
                absence_layout.addWidget(info_label)
                return

            # إنشاء نافذة معالجة تبرير الغياب
            print("إنشاء نافذة معالجة تبرير الغياب...")

            # إنشاء كائن نافذة جديد
            embedded_window = sub14_module.AbsenceManagementWindow(
                student_code=self.current_record_id,
                parent=manage_absence_subtab,
                db=self.external_db if hasattr(self, 'external_db') else None,
                academic_year=self.current_academic_year if hasattr(self, 'current_academic_year') else None
            )

            # تعديل خصائص النافذة
            embedded_window.setWindowFlags(Qt.Widget)
            print("تم تعيين خصائص النافذة إلى Widget")

            # إخفاء زر الإغلاق
            if hasattr(embedded_window, 'close_button'):
                embedded_window.close_button.hide()
                print("تم إخفاء زر الإغلاق")

            # تعيين الحجم المناسب
            embedded_window.setMinimumHeight(400)
            embedded_window.setMinimumWidth(600)

            # إضافة النافذة إلى التخطيط مباشرة
            absence_layout.addWidget(embedded_window)
            print("تم إضافة النافذة إلى التخطيط")

            # حفظ مرجع للنافذة المضمنة في المتغير العضو
            self.embedded_absence_management_window = embedded_window

            # استخدام تأخير قصير قبل تحديث معلومات الطالب
            QTimer.singleShot(50, lambda: self._update_absence_management_student_info(embedded_window))

        except Exception as e:
            print(f"خطأ في إنشاء نافذة معالجة تبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_management_error(str(e))

    def _update_absence_management_student_info(self, embedded_window):
        """تحديث معلومات الطالب في نافذة معالجة تبرير الغياب"""
        try:
            # تحديث معلومات الطالب
            student_name = self.name_display.text() if hasattr(self, 'name_display') else ""

            # تحديث عنوان النافذة
            if hasattr(embedded_window, 'setWindowTitle'):
                title = f"معالجة تبرير الغياب: {student_name} ({self.current_record_id})"
                embedded_window.setWindowTitle(title)
                print(f"تم تحديث عنوان النافذة: {title}")

            # تحديث معلومات الطالب في واجهة المستخدم
            if hasattr(embedded_window, 'student_code'):
                embedded_window.student_code = self.current_record_id
                print(f"تم تحديث رمز الطالب: {self.current_record_id}")

            # إظهار النافذة بشكل صريح
            embedded_window.show()
            print("تم إظهار النافذة المضمنة بشكل صريح")

            # تحديث البيانات
            self._try_load_absence_management_data()

        except Exception as e:
            print(f"خطأ في تحديث معلومات الطالب في نافذة معالجة تبرير الغياب: {e}")
            traceback.print_exc()

    def _show_absence_management_error(self, error_message):
        """عرض رسالة خطأ في نافذة معالجة تبرير الغياب"""
        try:
            error_label = QLabel(f"خطأ في تحميل نافذة معالجة تبرير الغياب: {error_message}")
            error_label.setStyleSheet("color: red; font-weight: bold;")
            error_label.setAlignment(Qt.AlignCenter)

            # البحث عن التبويب والتخطيط المناسب
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "تبرير الغياب":
                    absence_tab = self.tab_widget.widget(i)

                    # البحث عن التبويبات الفرعية
                    for j in range(absence_tab.layout().count()):
                        widget = absence_tab.layout().itemAt(j).widget()
                        if isinstance(widget, QTabWidget):
                            # البحث عن تبويب معالجة تبرير الغياب
                            for k in range(widget.count()):
                                if widget.tabText(k) == "معالجة تبرير الغياب":
                                    subtab = widget.widget(k)

                                    # تنظيف التخطيط الحالي وإضافة رسالة الخطأ
                                    if subtab.layout():
                                        layout = subtab.layout()
                                        while layout.count():
                                            item = layout.takeAt(0)
                                            if item.widget():
                                                item.widget().setParent(None)
                                                item.widget().deleteLater()
                                        layout.addWidget(error_label)
                                    else:
                                        new_layout = QVBoxLayout(subtab)
                                        new_layout.addWidget(error_label)
                                    break
                            break
                    break
        except:
            # في حالة فشل عرض الخطأ، نتجاهل
            pass

    def create_embedded_absence_window(self):
        """إنشاء نافذة مسك تبرير الغياب المضمنة"""
        try:
            # التحقق من وجود معرف طالب محدد
            if not hasattr(self, 'current_record_id') or not self.current_record_id:
                print("لا يوجد معرف طالب محدد لإنشاء نافذة مسك تبرير الغياب")
                return

            # طباعة معلومات تشخيصية
            print(f"بدء إنشاء نافذة مسك تبرير الغياب للطالب: {self.current_record_id}")

            # البحث عن تبويب تبرير الغياب والتبويب الفرعي "مسك تبرير الغياب"
            absence_tab_index = -1
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "تبرير الغياب":
                    absence_tab_index = i
                    absence_tab = self.tab_widget.widget(i)
                    print(f"تم العثور على تبويب تبرير الغياب بالمؤشر {i}")
                    break

            if absence_tab_index == -1:
                print("لم يتم العثور على تبويب تبرير الغياب")
                return

            # الانتقال إلى تبويب تبرير الغياب
            self.tab_widget.setCurrentIndex(absence_tab_index)

            # استخدام تأخير قصير قبل البحث عن التبويبات الفرعية
            QTimer.singleShot(50, lambda: self._find_and_setup_entry_absence_subtabs(absence_tab))

        except Exception as e:
            print(f"خطأ في إنشاء نافذة مسك تبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_entry_error(str(e))

    def _find_and_setup_entry_absence_subtabs(self, absence_tab):
        """البحث عن التبويبات الفرعية لمسك تبرير الغياب وإعدادها"""
        try:
            # البحث عن التبويبات الفرعية داخل تبويب تبرير الغياب
            entry_absence_subtab = None
            absence_subtabs = None

            # البحث عن التبويبات الفرعية
            for i in range(absence_tab.layout().count()):
                widget = absence_tab.layout().itemAt(i).widget()
                if isinstance(widget, QTabWidget):
                    absence_subtabs = widget
                    print(f"تم العثور على تبويبات فرعية لتبرير الغياب بـ {widget.count()} تبويب")
                    break

            if not absence_subtabs:
                print("لم يتم العثور على تبويبات فرعية لتبرير الغياب")
                return

            # البحث عن تبويب مسك تبرير الغياب
            entry_absence_tab_index = -1
            for j in range(absence_subtabs.count()):
                if absence_subtabs.tabText(j) == "مسك تبرير الغياب":
                    entry_absence_tab_index = j
                    entry_absence_subtab = absence_subtabs.widget(j)
                    print(f"تم العثور على تبويب مسك تبرير الغياب بالمؤشر {j}")
                    break

            if entry_absence_tab_index == -1 or not entry_absence_subtab:
                print("لم يتم العثور على تبويب مسك تبرير الغياب")
                return

            # الانتقال إلى تبويب مسك تبرير الغياب
            absence_subtabs.setCurrentIndex(entry_absence_tab_index)

            # استخدام تأخير قصير قبل تنظيف وإعداد التخطيط
            QTimer.singleShot(50, lambda: self._setup_absence_entry_layout(entry_absence_subtab))

        except Exception as e:
            print(f"خطأ في البحث عن التبويبات الفرعية لمسك تبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_entry_error(str(e))

    def _setup_absence_entry_layout(self, entry_absence_subtab):
        """إعداد تخطيط نافذة مسك تبرير الغياب"""
        try:
            # التحقق من وجود تخطيط سابق وتنظيفه
            current_layout = entry_absence_subtab.layout()

            # إذا كان هناك تخطيط سابق، نحذف كل العناصر فيه
            if current_layout:
                # حذف العناصر من التخطيط السابق - بطريقة آمنة
                while current_layout.count():
                    item = current_layout.takeAt(0)
                    widget = item.widget()
                    # تحقق من أن العنصر ليس None قبل محاولة حذفه
                    if widget:
                        widget.hide()  # إخفاء العنصر أولاً
                        widget.setParent(None)  # فصله عن الأب
                        widget.deleteLater()  # جدولة حذفه

                # استخدام التخطيط الحالي بعد تنظيفه
                entry_absence_layout = current_layout
            else:
                # إنشاء تخطيط جديد إذا لم يكن هناك تخطيط
                entry_absence_layout = QVBoxLayout()
                entry_absence_subtab.setLayout(entry_absence_layout)

            # استخدام تأخير قصير قبل إنشاء النافذة المضمنة
            QTimer.singleShot(50, lambda: self._create_absence_entry_window(entry_absence_subtab, entry_absence_layout))

        except Exception as e:
            print(f"خطأ في إعداد تخطيط نافذة مسك تبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_entry_error(str(e))

    def _create_absence_entry_window(self, entry_absence_subtab, entry_absence_layout):
        """إنشاء نافذة مسك تبرير الغياب"""
        try:
            # التحقق من وجود وحدة sub13_module
            if 'sub13_module' not in globals():
                print("وحدة sub13_module غير متوفرة")
                # إضافة رسالة توضيحية
                info_label = QLabel("نافذة مسك تبرير الغياب غير متوفرة")
                info_label.setFont(QFont("Arial", 12))
                info_label.setStyleSheet("color: red; font-weight: bold;")
                info_label.setAlignment(Qt.AlignCenter)
                entry_absence_layout.addWidget(info_label)
                return

            # إنشاء نافذة مسك تبرير الغياب
            print("إنشاء نافذة مسك تبرير الغياب...")

            # إنشاء كائن نافذة جديد
            embedded_window = sub13_module.AbsenceJustificationWindow(
                parent=entry_absence_subtab
            )

            # تعديل خصائص النافذة
            embedded_window.setWindowFlags(Qt.Widget)
            print("تم تعيين خصائص النافذة إلى Widget")

            # إخفاء زر الإغلاق وعنوان النافذة
            if hasattr(embedded_window, 'close_button'):
                embedded_window.close_button.hide()
                print("تم إخفاء زر الإغلاق")

            if hasattr(embedded_window, 'title_label'):
                embedded_window.title_label.hide()
                print("تم إخفاء عنوان النافذة")

            # تعيين الحجم المناسب
            if hasattr(embedded_window, 'setFixedSize'):
                embedded_window.setFixedSize(1200, 500)
                print("تم تعيين الحجم المناسب للنافذة")

            # إضافة النافذة إلى التخطيط مباشرة
            entry_absence_layout.addWidget(embedded_window)
            print("تم إضافة النافذة إلى التخطيط")

            # حفظ مرجع للنافذة المضمنة في المتغير العضو
            self.embedded_absence_window = embedded_window

            # استخدام تأخير قصير قبل تحديث معلومات الطالب
            QTimer.singleShot(50, lambda: self._update_absence_entry_student_info(embedded_window))

        except Exception as e:
            print(f"خطأ في إنشاء نافذة مسك تبرير الغياب: {e}")
            traceback.print_exc()
            self._show_absence_entry_error(str(e))

    def _update_absence_entry_student_info(self, embedded_window):
        """تحديث معلومات الطالب في نافذة مسك تبرير الغياب"""
        try:
            # استخراج بيانات التلميذ
            level = self.level_display.text() if hasattr(self, 'level_display') else ""
            class_name = self.class_display.text() if hasattr(self, 'class_display') else ""
            student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
            rt = self.rt_display.text() if hasattr(self, 'rt_display') else ""

            print(f"تحديث بيانات التلميذ في نافذة مسك تبرير الغياب: {self.current_record_id}, {student_name}")

            # تحديث معلومات الطالب في نافذة مسك تبرير الغياب
            if hasattr(embedded_window, 'set_student_info'):
                embedded_window.set_student_info(
                    code=self.current_record_id,
                    name=student_name,
                    id_num=rt,
                    level=level,
                    class_name=class_name
                )
                print("تم تحديث بيانات التلميذ في نافذة مسك تبرير الغياب بنجاح")

            # إظهار النافذة بشكل صريح
            embedded_window.show()
            print("تم إظهار النافذة المضمنة بشكل صريح")

            # تحديث البيانات
            self._load_absence_data()

        except Exception as e:
            print(f"خطأ في تحديث معلومات الطالب في نافذة مسك تبرير الغياب: {e}")
            traceback.print_exc()

    def _show_absence_entry_error(self, error_message):
        """عرض رسالة خطأ في نافذة مسك تبرير الغياب"""
        try:
            error_label = QLabel(f"خطأ في تحميل نافذة مسك تبرير الغياب: {error_message}")
            error_label.setStyleSheet("color: red; font-weight: bold;")
            error_label.setAlignment(Qt.AlignCenter)

            # البحث عن التبويب والتخطيط المناسب
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "تبرير الغياب":
                    absence_tab = self.tab_widget.widget(i)

                    # البحث عن التبويبات الفرعية
                    for j in range(absence_tab.layout().count()):
                        widget = absence_tab.layout().itemAt(j).widget()
                        if isinstance(widget, QTabWidget):
                            # البحث عن تبويب مسك تبرير الغياب
                            for k in range(widget.count()):
                                if widget.tabText(k) == "مسك تبرير الغياب":
                                    subtab = widget.widget(k)

                                    # تنظيف التخطيط الحالي وإضافة رسالة الخطأ
                                    if subtab.layout():
                                        layout = subtab.layout()
                                        while layout.count():
                                            item = layout.takeAt(0)
                                            if item.widget():
                                                item.widget().setParent(None)
                                                item.widget().deleteLater()
                                        layout.addWidget(error_label)
                                    else:
                                        new_layout = QVBoxLayout(subtab)
                                        new_layout.addWidget(error_label)
                                    break
                            break
                    break
        except:
            # في حالة فشل عرض الخطأ، نتجاهل
            pass

    def _try_recreate_absence_management_window(self):
        """محاولة إعادة إنشاء نافذة معالجة تبرير الغياب"""
        try:
            if hasattr(self, 'embedded_absence_management_window') and self.embedded_absence_management_window:
                # تحقق من وجود parent النافذة الحالية
                if hasattr(self.embedded_absence_management_window, 'parent'):
                    parent_widget = self.embedded_absence_management_window.parent()
                    if parent_widget:
                        # حذف النافذة القديمة
                        self.embedded_absence_management_window.setParent(None)
                        self.embedded_absence_management_window.deleteLater()

                        # إعادة إنشاء النافذة مع تمرير معرف الطالب
                        self.embedded_absence_management_window = sub14_module.AbsenceManagementWindow(
                            student_code=self.current_record_id,
                            parent=parent_widget,
                            db=self.external_db if hasattr(self, 'external_db') else None,
                            academic_year=self.current_academic_year if hasattr(self, 'current_academic_year') else None
                        )

                        # تعديل خصائص النافذة
                        self.embedded_absence_management_window.setWindowFlags(Qt.Widget)
                        if hasattr(self.embedded_absence_management_window, 'close_button'):
                            self.embedded_absence_management_window.close_button.hide()

                        # إعادة إضافة النافذة إلى parent
                        if hasattr(parent_widget, 'layout') and parent_widget.layout():
                            parent_widget.layout().addWidget(self.embedded_absence_management_window)
                            print(f"تم إعادة إنشاء نافذة معالجة تبرير الغياب مع تمرير معرف الطالب: {self.current_record_id}")
        except Exception as e:
            print(f"فشلت المحاولة الأخيرة لتحديث معرف الطالب: {e}")

    def update_embedded_parent_visit_window(self):
        """تحديث بيانات التلميذ في نافذة توثيق زيارة أولياء الأمور المضمنة"""
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        QTimer.singleShot(10, self._delayed_update_embedded_parent_visit_window)

    def _delayed_update_embedded_parent_visit_window(self):
        """تنفيذ تحديث بيانات التلميذ في نافذة توثيق زيارة أولياء الأمور المضمنة بعد تأخير قصير"""
        try:
            # التحقق من وجود النافذة المضمنة
            if hasattr(self, 'embedded_parent_visit') and self.embedded_parent_visit:
                # التحقق من وجود بيانات التلميذ المطلوبة
                if hasattr(self, 'current_record_id') and self.current_record_id:
                    # استخراج بيانات التلميذ
                    student_code = self.current_record_id
                    student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
                    level = self.level_display.text() if hasattr(self, 'level_display') else ""
                    class_name = self.class_display.text() if hasattr(self, 'class_display') else ""
                    rt = self.rt_display.text() if hasattr(self, 'rt_display') else ""

                    print(f"تحديث بيانات التلميذ في نافذة توثيق زيارة أولياء الأمور: {student_code}, {student_name}, {level}, {class_name}")

                    # التحقق من وجود دالة set_student_info في نافذة توثيق الزيارة
                    if hasattr(self.embedded_parent_visit, 'set_student_info'):
                        # استدعاء دالة تعيين بيانات التلميذ مع الحرص على عدم إرسال قيم None
                        self.embedded_parent_visit.set_student_info(
                            code=student_code,
                            name=student_name,
                            id_num=rt,
                            level=level,
                            class_name=class_name
                        )
                        print(f"تم تعيين بيانات التلميذ في نافذة توثيق زيارة أولياء الأمور بنجاح: {student_name}")

                        # تأكد من أن النافذة مرئية - تنفيذ بعد تأخير قصير
                        QTimer.singleShot(50, lambda: self._ensure_parent_visit_window_visible())
                    else:
                        print("نافذة توثيق زيارة أولياء الأمور لا تحتوي على دالة set_student_info")
                else:
                    print("لم يتم تحديد تلميذ حالي لتحديث نافذة توثيق زيارة أولياء الأمور")
            else:
                print("النافذة المضمنة 'embedded_parent_visit' غير موجودة")
        except Exception as e:
            print(f"خطأ في تحديث بيانات التلميذ في نافذة توثيق زيارة أولياء الأمور: {e}")
            traceback.print_exc()

    def _ensure_parent_visit_window_visible(self):
        """التأكد من أن نافذة توثيق زيارة أولياء الأمور مرئية"""
        try:
            if hasattr(self, 'embedded_parent_visit') and self.embedded_parent_visit:
                # تأكد من أن النافذة مرئية
                self.embedded_parent_visit.show()
                print("تم التأكد من أن نافذة توثيق زيارة أولياء الأمور مرئية")
        except Exception as e:
            print(f"خطأ في التأكد من أن نافذة توثيق زيارة أولياء الأمور مرئية: {e}")

    def update_embedded_visit_management_student_data(self):
        """تحديث بيانات الطالب في نافذة معالجة زيارات أولياء الأمور المضمنة"""
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        QTimer.singleShot(10, self._delayed_update_embedded_visit_management_student_data)

    def _delayed_update_embedded_visit_management_student_data(self):
        """تنفيذ تحديث بيانات الطالب في نافذة معالجة زيارات أولياء الأمور المضمنة بعد تأخير قصير"""
        try:
            if not hasattr(self, 'embedded_parent_visit_management') or not self.embedded_parent_visit_management:
                return

            # تعيين رمز الطالب الحالي في النافذة المضمنة
            if hasattr(self.embedded_parent_visit_management, 'student_code'):
                # تحديث رمز الطالب
                self.embedded_parent_visit_management.student_code = self.current_record_id

                # تنفيذ تحديثات البيانات في مهام منفصلة مع تأخير قصير بينها
                # لتحسين استجابة واجهة المستخدم

                # إعادة تحميل البيانات إذا كانت هناك دالة لذلك
                if hasattr(self.embedded_parent_visit_management, 'load_visits'):
                    QTimer.singleShot(50, self._load_visit_management_data)

                # تحديث عنوان النافذة وبيانات الطالب
                QTimer.singleShot(100, self._update_visit_management_window_title)

        except Exception as e:
            print(f"خطأ في تحديث بيانات الطالب في نافذة معالجة زيارات أولياء الأمور: {e}")
            traceback.print_exc()

    def _load_visit_management_data(self):
        """تحميل بيانات الزيارات في نافذة معالجة زيارات أولياء الأمور"""
        try:
            if hasattr(self, 'embedded_parent_visit_management') and self.embedded_parent_visit_management:
                if hasattr(self.embedded_parent_visit_management, 'load_visits'):
                    self.embedded_parent_visit_management.load_visits()
                    print(f"تم تحديث بيانات الزيارات للطالب {self.current_record_id}")
        except Exception as e:
            print(f"خطأ في تحميل بيانات الزيارات: {e}")

    def _update_visit_management_window_title(self):
        """تحديث عنوان النافذة ومعلومات الطالب في نافذة معالجة زيارات أولياء الأمور"""
        try:
            if hasattr(self, 'embedded_parent_visit_management') and self.embedded_parent_visit_management:
                student_name = self.name_display.text() if hasattr(self, 'name_display') else ""

                # تحديث عنوان النافذة إذا كانت هناك وسيلة لذلك
                if hasattr(self.embedded_parent_visit_management, 'update_window_title'):
                    self.embedded_parent_visit_management.update_window_title(student_name)
                elif hasattr(self.embedded_parent_visit_management, 'setWindowTitle'):
                    title = f"سجلات زيارات أولياء الأمور: {student_name}"
                    self.embedded_parent_visit_management.setWindowTitle(title)

                # تحديث معلومات الطالب في واجهة المستخدم
                if hasattr(self.embedded_parent_visit_management, 'update_student_info'):
                    self.embedded_parent_visit_management.update_student_info(self.current_record_id, student_name)
        except Exception as e:
            print(f"خطأ في تحديث عنوان النافذة ومعلومات الطالب: {e}")

    def update_embedded_parent_visit_management_window(self):
        """تضمين نافذة معالجة زيارات أولياء الأمور داخل التبويب"""
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        QTimer.singleShot(10, self._delayed_update_embedded_parent_visit_management_window)

    def _delayed_update_embedded_parent_visit_management_window(self):
        """تنفيذ تضمين نافذة معالجة زيارات أولياء الأمور داخل التبويب بعد تأخير قصير"""
        try:
            # التحقق من وجود معرف طالب محدد
            if not hasattr(self, 'current_record_id') or not self.current_record_id:
                print("لا يوجد معرف طالب محدد لتحميل سجلات الزيارات")
                return

            # طباعة معلومات تشخيصية
            print(f"بدء تضمين نافذة معالجة زيارات أولياء الأمور للطالب: {self.current_record_id}")

            # البحث عن تبويب زيارات أولياء الأمور والتبويب الفرعي "استعراض سجل الزيارات"
            visits_tab_index = -1
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "زيارات أولياء الأمور":
                    visits_tab_index = i
                    visits_tab = self.tab_widget.widget(i)
                    print(f"تم العثور على تبويب زيارات أولياء الأمور بالمؤشر {i}")
                    break

            if visits_tab_index == -1:
                print("لم يتم العثور على تبويب زيارات أولياء الأمور")
                return

            # الانتقال إلى تبويب زيارات أولياء الأمور
            self.tab_widget.setCurrentIndex(visits_tab_index)

            # استخدام تأخير قصير قبل البحث عن التبويبات الفرعية
            QTimer.singleShot(50, lambda: self._find_and_setup_visit_subtabs(visits_tab))

        except Exception as e:
            print(f"خطأ في تضمين نافذة معالجة زيارات أولياء الأمور: {e}")
            traceback.print_exc()
            self._show_visit_management_error(str(e))

    def _find_and_setup_visit_subtabs(self, visits_tab):
        """البحث عن التبويبات الفرعية وإعدادها"""
        try:
            # البحث عن التبويبات الفرعية داخل تبويب الزيارات
            view_visits_subtab = None
            visits_subtabs = None

            # البحث عن التبويبات الفرعية
            for i in range(visits_tab.layout().count()):
                widget = visits_tab.layout().itemAt(i).widget()
                if isinstance(widget, QTabWidget):
                    visits_subtabs = widget
                    print(f"تم العثور على تبويبات فرعية للزيارات بـ {widget.count()} تبويب")
                    break

            if not visits_subtabs:
                print("لم يتم العثور على تبويبات فرعية للزيارات")
                return

            # البحث عن تبويب استعراض سجلات الزيارات
            view_visits_tab_index = -1
            for j in range(visits_subtabs.count()):
                if visits_subtabs.tabText(j) == "استعراض سجل الزيارات":
                    view_visits_tab_index = j
                    view_visits_subtab = visits_subtabs.widget(j)
                    print(f"تم العثور على تبويب استعراض سجل الزيارات بالمؤشر {j}")
                    break

            if view_visits_tab_index == -1 or not view_visits_subtab:
                print("لم يتم العثور على تبويب استعراض سجل الزيارات")
                return

            # الانتقال إلى تبويب استعراض سجل الزيارات
            visits_subtabs.setCurrentIndex(view_visits_tab_index)

            # استخدام تأخير قصير قبل تنظيف وإعداد التخطيط
            QTimer.singleShot(50, lambda: self._setup_visit_management_layout(view_visits_subtab))

        except Exception as e:
            print(f"خطأ في البحث عن التبويبات الفرعية: {e}")
            traceback.print_exc()
            self._show_visit_management_error(str(e))

    def _setup_visit_management_layout(self, view_visits_subtab):
        """إعداد تخطيط نافذة معالجة زيارات أولياء الأمور"""
        try:
            # التحقق من وجود تخطيط سابق وتنظيفه
            current_layout = view_visits_subtab.layout()

            # إذا كان هناك تخطيط سابق، نحذف كل العناصر فيه
            if current_layout:
                # حذف العناصر من التخطيط السابق - بطريقة آمنة
                while current_layout.count():
                    item = current_layout.takeAt(0)
                    widget = item.widget()
                    # تحقق من أن العنصر ليس None قبل محاولة حذفه
                    if widget:
                        widget.hide()  # إخفاء العنصر أولاً
                        widget.setParent(None)  # فصله عن الأب
                        widget.deleteLater()  # جدولة حذفه

                # استخدام التخطيط الحالي بعد تنظيفه
                view_visits_layout = current_layout
            else:
                # إنشاء تخطيط جديد إذا لم يكن هناك تخطيط
                view_visits_layout = QVBoxLayout()
                view_visits_subtab.setLayout(view_visits_layout)

            # استخدام تأخير قصير قبل إنشاء النافذة المضمنة
            QTimer.singleShot(50, lambda: self._create_embedded_visit_management_window(view_visits_subtab, view_visits_layout))

        except Exception as e:
            print(f"خطأ في إعداد تخطيط نافذة معالجة زيارات أولياء الأمور: {e}")
            traceback.print_exc()
            self._show_visit_management_error(str(e))

    def _create_embedded_visit_management_window(self, view_visits_subtab, view_visits_layout):
        """إنشاء نافذة معالجة زيارات أولياء الأمور المضمنة"""
        try:
            # إنشاء نافذة معالجة زيارات أولياء الأمور (جديدة في كل مرة)
            if PARENT_VISIT_MANAGEMENT_WINDOW_AVAILABLE:
                print("إنشاء نافذة معالجة زيارات أولياء الأمور...")

                # إنشاء كائن نافذة جديد
                embedded_window = ParentVisitManagementWindow(
                    student_code=self.current_record_id,
                    parent=view_visits_subtab
                )

                # تعيين علامات النافذة إلى Widget
                embedded_window.setWindowFlags(Qt.Widget)
                print("تم تعيين خصائص النافذة إلى Widget")

                # إخفاء زر الإغلاق
                if hasattr(embedded_window, 'close_button'):
                    embedded_window.close_button.hide()
                    print("تم إخفاء زر الإغلاق")

                # تعيين الحجم المناسب
                embedded_window.setMinimumHeight(400)
                embedded_window.setMinimumWidth(600)

                # إضافة النافذة إلى التخطيط مباشرة
                view_visits_layout.addWidget(embedded_window)
                print("تم إضافة النافذة إلى التخطيط")

                # حفظ مرجع للنافذة المضمنة في المتغير العضو
                self.embedded_parent_visit_management = embedded_window

                # استخدام تأخير قصير قبل تحديث معلومات الطالب
                QTimer.singleShot(50, lambda: self._update_visit_management_student_info(embedded_window))

            else:
                # إضافة رسالة توضيحية إذا كانت النافذة غير متوفرة
                info_label = QLabel("نافذة معالجة سجلات زيارات أولياء الأمور غير متوفرة")
                info_label.setFont(QFont("Arial", 12))
                info_label.setStyleSheet("color: red; font-weight: bold;")
                info_label.setAlignment(Qt.AlignCenter)
                view_visits_layout.addWidget(info_label)
                print("تم عرض رسالة عدم توفر النافذة")

        except Exception as e:
            print(f"خطأ في إنشاء نافذة معالجة زيارات أولياء الأمور المضمنة: {e}")
            traceback.print_exc()
            self._show_visit_management_error(str(e))

    def _update_visit_management_student_info(self, embedded_window):
        """تحديث معلومات الطالب في نافذة معالجة زيارات أولياء الأمور"""
        try:
            # تحديث معلومات الطالب
            student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
            embedded_window.update_window_title(student_name)
            embedded_window.update_student_info(self.current_record_id, student_name)
            print(f"تم تحديث معلومات الطالب: {student_name} ({self.current_record_id})")

            # إظهار النافذة بشكل صريح
            embedded_window.show()
            print("تم إظهار النافذة المضمنة بشكل صريح")

        except Exception as e:
            print(f"خطأ في تحديث معلومات الطالب في نافذة معالجة زيارات أولياء الأمور: {e}")
            traceback.print_exc()

    def _show_visit_management_error(self, error_message):
        """عرض رسالة خطأ في نافذة معالجة زيارات أولياء الأمور"""
        try:
            error_label = QLabel(f"خطأ في تحميل نافذة زيارات أولياء الأمور: {error_message}")
            error_label.setStyleSheet("color: red; font-weight: bold;")
            error_label.setAlignment(Qt.AlignCenter)

            # البحث عن التبويب والتخطيط المناسب
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "زيارات أولياء الأمور":
                    visits_tab = self.tab_widget.widget(i)

                    # البحث عن التبويبات الفرعية
                    for j in range(visits_tab.layout().count()):
                        widget = visits_tab.layout().itemAt(j).widget()
                        if isinstance(widget, QTabWidget):
                            # البحث عن تبويب استعراض سجلات الزيارات
                            for k in range(widget.count()):
                                if widget.tabText(k) == "استعراض سجل الزيارات":
                                    subtab = widget.widget(k)

                                    # تنظيف التخطيط الحالي وإضافة رسالة الخطأ
                                    if subtab.layout():
                                        layout = subtab.layout()
                                        while layout.count():
                                            item = layout.takeAt(0)
                                            if item.widget():
                                                item.widget().setParent(None)
                                                item.widget().deleteLater()
                                        layout.addWidget(error_label)
                                    else:
                                        new_layout = QVBoxLayout(subtab)
                                        new_layout.addWidget(error_label)
                                    break
                            break
                    break
        except:
            # في حالة فشل عرض الخطأ، نتجاهل
            pass

    def save_contact_info(self, show_success_message=True):
        """حفظ معلومات الاتصال المعدلة في قاعدة البيانات"""
        if self.current_record_id:
            try:
                phone1 = self.phone1_field.text()
                phone2 = self.phone2_field.text()
                notes = self.notes_field.toPlainText() # QTextEdit uses toPlainText

                if self.db and self.db.isOpen():
                    query = QSqlQuery(self.db)
                    query.prepare("""
                        UPDATE السجل_العام
                        SET الهاتف_الأول = :p1, الهاتف_الثاني = :p2, ملاحظات = :notes
                        WHERE الرمز = :code
                    """)
                    query.bindValue(":p1", phone1)
                    query.bindValue(":p2", phone2)
                    query.bindValue(":notes", notes)
                    query.bindValue(":code", self.current_record_id)
                    if not query.exec_():
                         raise Exception(f"QSqlQuery Error: {query.lastError().text()}")
                    # QSqlDatabase usually auto-commits or requires transaction management

                elif self.conn and self.cursor:
                    self.cursor.execute("""
                        UPDATE السجل_العام
                        SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?
                        WHERE الرمز = ?
                    """, (phone1, phone2, notes, self.current_record_id))
                    self.conn.commit()
                else:
                    raise Exception("لا يوجد اتصال قاعدة بيانات صالح.")

                # عرض رسالة النجاح فقط إذا كان المعامل show_success_message بقيمة True
                if show_success_message:
                    self.show_custom_success_message("تم حفظ بيانات الاتصال بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ بيانات الاتصال:\n{str(e)}")
                print(f"Error saving contact info: {e}")
                traceback.print_exc()
        else:
            QMessageBox.warning(self, "تنبيه", "لا يوجد سجل محدد للتعديل")

    def show_custom_success_message(self, message):
        """عرض رسالة نجاح مخصصة مع أيقونة البرنامج"""
        # إنشاء نافذة حوار مخصصة
        success_dialog = QDialog(self)
        success_dialog.setWindowTitle("تم بنجاح")
        success_dialog.setFixedSize(400, 200)
        success_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            success_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        success_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #3498db;
                border-radius: 5px;
            }
            QLabel {
                color: #2c3e50;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(success_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة النجاح
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)

        # محاولة تحميل أيقونة البرنامج، وإذا فشلت استخدم أيقونة النجاح الافتراضية
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
            else:
                # استخدام أيقونة النجاح الافتراضية
                success_icon = self.style().standardIcon(QStyle.SP_DialogApplyButton)
                icon_label.setPixmap(success_icon.pixmap(64, 64))
        except:
            # استخدام نص بديل في حالة فشل تحميل الأيقونة
            icon_label.setText("✓")
            icon_label.setStyleSheet("font-size: 48px; color: #27ae60;")

        layout.addWidget(icon_label)

        # إضافة رسالة النجاح
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setFont(QFont("Calibri", 14, QFont.Bold))
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر موافق
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.setMinimumHeight(40)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        ok_button.clicked.connect(success_dialog.accept)
        layout.addWidget(ok_button)

        # عرض النافذة
        success_dialog.exec_()

    def show_quick_notes_dialog(self):
        """فتح نافذة إضافة ملاحظات مختصرة"""
        try:
            if not self.current_record_id:
                QMessageBox.warning(self, "تنبيه", "لم يتم تحديد تلميذ")
                return

            # إنشاء نافذة جديدة للملاحظات المختصرة
            quick_notes_dialog = QuickNotesDialog(parent=self)

            # عرض النافذة وانتظار النتيجة
            result = quick_notes_dialog.exec_()

            # إذا تم اختيار ملاحظة، استبدل الملاحظات الموجودة بالملاحظة المختارة
            if result == QDialog.Accepted and quick_notes_dialog.selected_note:
                # تحديث حقل الملاحظات بالملاحظة المختارة فقط
                self.notes_field.setPlainText(quick_notes_dialog.selected_note)

                # حفظ الملاحظات تلقائياً (بدون عرض رسالة النجاح القياسية)
                self.save_contact_info(show_success_message=False)

                # عرض رسالة تأكيد مخصصة
                self.show_custom_success_message("تم تحديث الملاحظات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
            print(f"خطأ في فتح نافذة الملاحظات المختصرة: {e}")
            traceback.print_exc()

    def _close_current_window(self):
        """إغلاق النافذة النشطة الحالية قبل فتح نافذة جديدة"""
        if self.current_active_window and hasattr(self.current_active_window, 'close'):
            try:
                self.current_active_window.close()
                self.current_active_window = None
                print("تم إغلاق النافذة النشطة السابقة")
            except Exception as e:
                print(f"خطأ عند إغلاق النافذة السابقة: {e}")

    def print_entry_records(self):
        """طباعة سجلات ورقة السماح بالدخول للتلميذ الحالي"""
        if not hasattr(self, 'current_record_id') or not self.current_record_id:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحميل بيانات تلميذ أولاً.")
            return

        try:
            # استيراد وظيفة الطباعة من print2
            try:
                from print2 import print_entry_records
                print("تم استيراد دالة طباعة سجلات الدخول من print2.py بنجاح")
            except ImportError as e:
                QMessageBox.critical(self, "خطأ", f"لم يتم العثور على وحدة print2: {str(e)}")
                return
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء استيراد دالة الطباعة: {str(e)}")
                return

            # استيراد المكتبات المطلوبة للتعامل مع المسارات والتاريخ
            import os
            import pathlib
            from datetime import datetime

            # جمع بيانات المؤسسة
            institution_data = {}
            try:
                if self.db and self.db.isOpen():
                    query = QSqlQuery(self.db)
                    query.prepare("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                    if query.exec_() and query.next():
                        institution_data['institution'] = query.value(0)
                        institution_data['school_year'] = query.value(1)
                elif self.conn and self.cursor:
                    self.cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                    result = self.cursor.fetchone()
                    if result:
                        institution_data['institution'] = result[0]
                        institution_data['school_year'] = result[1]
            except Exception as e:
                print(f"خطأ في استخراج بيانات المؤسسة: {e}")

            # جمع بيانات الطالب وسجلاته
            student_id = self.current_record_id
            student_name = self.name_display.text()
            level = self.level_display.text()
            class_name = self.class_display.text()

            # جمع سجلات السماح
            permission_records = []
            entry_model = self.entry_records_table.model()
            for row in range(entry_model.rowCount()):
                record_row = []
                for col in range(entry_model.columnCount()):
                    value = entry_model.index(row, col).data() or ""
                    record_row.append(value)
                permission_records.append(record_row)

            # جمع سجلات التأخر
            late_records = []
            late_model = self.late_records_table.model()
            for row in range(late_model.rowCount()):
                record_row = []
                for col in range(late_model.columnCount()):
                    value = late_model.index(row, col).data() or ""
                    record_row.append(value)
                late_records.append(record_row)

            # إعداد البيانات المطلوبة لطباعة السجلات
            entry_data = {
                'student_code': student_id,
                'student_name': student_name,
                'level': level,
                'class': class_name,
                'permission_records': permission_records,
                'late_records': late_records
            }

            # إنشاء مجلد رئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_reports_dir):
                os.makedirs(main_reports_dir)
                print(f"تم إنشاء المجلد الرئيسي: {main_reports_dir}")

            # إنشاء مجلد فرعي لتقارير سجلات الدخول والتأخر
            reports_dir = os.path.join(main_reports_dir, "تقارير سجلات الدخول والتأخر")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                print(f"تم إنشاء المجلد الفرعي: {reports_dir}")

            # إنشاء مجلد داخل البرنامج للتوافقية
            local_reports_dir = os.path.join(os.path.dirname(__file__), "تقارير_سجلات_ورقة_الدخول")
            if not os.path.exists(local_reports_dir):
                os.makedirs(local_reports_dir)
                print(f"تم إنشاء المجلد المحلي للتوافقية: {local_reports_dir}")

            # إنشاء اسم ملف فريد باستخدام اسم الطالب ورمزه والتاريخ الحالي
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_student_name = ''.join(c if c.isalnum() else '_' for c in student_name)
            file_name = f"سجلات_الدخول_والتأخر_{safe_student_name}_{student_id}_{timestamp}"

            # تمرير معلومات المجلد واسم الملف إلى دالة الطباعة
            institution_data['output_dir'] = reports_dir
            institution_data['file_name'] = file_name

            print(f"سيتم حفظ الملف في المجلد: {reports_dir}")
            print(f"اسم الملف: {file_name}")

            # تم إزالة رسالة التأكيد قبل الطباعة بناءً على طلب المستخدم

            # استدعاء وظيفة الطباعة من print2 مباشرة
            success = print_entry_records(institution_data, entry_data)

            if success:
                # عرض رسالة نجاح مع مسار الملف
                file_path = os.path.join(reports_dir, f"{file_name}.pdf")
                print(f"تم حفظ الملف بنجاح في: {file_path}")

                # محاولة فتح المجلد الذي يحتوي على الملف
                try:
                    if os.path.exists(file_path):
                        import subprocess
                        # فتح المجلد الذي يحتوي على الملف
                        subprocess.Popen(f'explorer /select,"{file_path}"')
                except Exception as e:
                    print(f"خطأ في فتح المجلد: {e}")
            else:
                QMessageBox.warning(self, "تنبيه", "لم يتم إنشاء تقرير سجلات ورقة السماح بالدخول")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء طباعة السجلات:\n{str(e)}")
            print(f"خطأ في طباعة سجلات ورقة السماح بالدخول: {e}")
            import traceback
            traceback.print_exc()

    def delete_selected_entry_record(self):
        """حذف سجل السماح المحدد من ورقة السماح بالدخول"""
        try:
            # التحقق من وجود صف محدد
            selected_indexes = self.entry_records_table.selectionModel().selectedRows()
            if not selected_indexes:
                QMessageBox.warning(self, "تنبيه", "الرجاء تحديد سجل لحذفه")
                return

            # الحصول على رقم_الورقة للسجل المحدد (العمود الأول)
            selected_row = selected_indexes[0].row()
            model = self.entry_records_table.model()

            if not model:
                QMessageBox.warning(self, "تنبيه", "لا توجد بيانات للحذف")
                return

            record_id_index = model.index(selected_row, 0)  # رقم_الورقة في العمود الأول
            record_id = model.data(record_id_index, Qt.DisplayRole)

            if not record_id:
                QMessageBox.warning(self, "تنبيه", "تعذر الحصول على رقم السجل المحدد")
                return

            # تم إزالة رسالة التأكيد قبل الحذف بناءً على طلب المستخدم

            # حذف السجل وتحديث قاعدة البيانات
            self.delete_specific_record(record_id)
        except Exception as e:
            print(f"خطأ في حذف السجل المحدد: {e}")
            traceback.print_exc()

    def delete_selected_late_record(self):
        """حذف سجل التأخر المحدد من ورقة السماح بالدخول"""
        try:
            # التحقق من وجود صف محدد
            selected_indexes = self.late_records_table.selectionModel().selectedRows()
            if not selected_indexes:
                QMessageBox.warning(self, "تنبيه", "الرجاء تحديد سجل لحذفه")
                return

            # الحصول على رقم_الورقة للسجل المحدد (العمود الأول)
            selected_row = selected_indexes[0].row()
            model = self.late_records_table.model()

            if not model:
                QMessageBox.warning(self, "تنبيه", "لا توجد بيانات للحذف")
                return

            record_id_index = model.index(selected_row, 0)  # رقم_الورقة في العمود الأول
            record_id = model.data(record_id_index, Qt.DisplayRole)

            if not record_id:
                QMessageBox.warning(self, "تنبيه", "تعذر الحصول على رقم السجل المحدد")
                return

            # تم إزالة رسالة التأكيد قبل الحذف بناءً على طلب المستخدم

            # حذف السجل وتحديث قاعدة البيانات
            self.delete_specific_record(record_id)
        except Exception as e:
            print(f"خطأ في حذف السجل المحدد: {e}")
            traceback.print_exc()

    def delete_specific_record(self, record_id):
        """حذف سجل محدد من ورقة السماح بالدخول وتحديث الإحصائيات"""
        try:
            if self.db and self.db.isOpen():
                # استخدام QSqlDatabase
                self.db.transaction() # بدء معاملة
                delete_query = QSqlQuery(self.db)
                delete_query.prepare("DELETE FROM ورقة_السماح_بالدخول WHERE رقم_الورقة = :id")
                delete_query.bindValue(":id", record_id)
                if not delete_query.exec_():
                    self.db.rollback()
                    raise Exception(f"QSqlQuery Delete Error: {delete_query.lastError().text()}")

                update_query = QSqlQuery(self.db)
                update_query.prepare("""
                    UPDATE السجل_العام
                    SET
                        السماح = (SELECT COUNT(*) FROM ورقة_السماح_بالدخول w WHERE w.الرمز = :code AND w.سماح = 1),
                        التأخر = (SELECT COUNT(*) FROM ورقة_السماح_بالدخول w WHERE w.الرمز = :code AND w.سماح = 0)
                    WHERE الرمز = :code
                """)
                update_query.bindValue(":code", self.current_record_id)
                if not update_query.exec_():
                    self.db.rollback()
                    raise Exception(f"QSqlQuery Update Error: {update_query.lastError().text()}")

                if not self.db.commit():
                     print("فشل في تنفيذ commit للمعاملة QSqlDatabase")
                     self.db.rollback() # محاولة تراجع عند فشل الـ commit
                     raise Exception("فشل في تنفيذ commit للمعاملة QSqlDatabase")

            elif self.conn and self.cursor:
                # استخدام sqlite3
                self.cursor.execute("DELETE FROM ورقة_السماح_بالدخول WHERE رقم_الورقة = ?", (record_id,))
                self.cursor.execute("""
                    UPDATE السجل_العام
                    SET
                        السماح = (SELECT COUNT(*) FROM ورقة_السماح_بالدخول w WHERE w.الرمز = ? AND w.سماح = 1),
                        التأخر = (SELECT COUNT(*) FROM ورقة_السماح_بالدخول w WHERE w.الرمز = ? AND w.سماح = 0)
                    WHERE الرمز = ?
                """, (self.current_record_id, self.current_record_id, self.current_record_id))
                self.conn.commit()
            else:
                 raise Exception("لا يوجد اتصال قاعدة بيانات صالح.")

            self.load_entry_records() # إعادة تحميل البيانات في الجداول
            self.show_custom_success_message("تم حذف السجل بنجاح")

        except Exception as e:
            # تراجع إذا كان يستخدم sqlite3 وفشلت المعاملة في منتصف الطريق
            if self.conn:
                try: self.conn.rollback()
                except: pass
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة الحذف: {str(e)}")
            print(f"خطأ في حذف السجل: {e}")
            traceback.print_exc()

    def load_entry_records(self):
        """تحميل سجلات ورقة السماح بالدخول للطالب الحالي"""
        if not hasattr(self, 'current_record_id') or not self.current_record_id:
            return

        try:
            # تعديل الاستعلام للسماح بالدخول - تغيير ترتيب الأعمدة ليتناسب مع التقرير
            entry_query = f"""
                SELECT
                    رقم_الورقة,
                    التاريخ,
                    الوقت,
                    الأسدس,
                    السنة_الدراسية
                FROM
                    ورقة_السماح_بالدخول
                WHERE
                    الرمز = '{self.current_record_id}'
                    AND سماح = 1
                ORDER BY
                    التاريخ DESC, الوقت DESC
            """

            # تعديل الاستعلام للتأخر - تغيير ترتيب الأعمدة ليتناسب مع التقرير
            late_query = f"""
                SELECT
                    رقم_الورقة,
                    التاريخ,
                    الوقت,
                    الأسدس,
                    السنة_الدراسية
                FROM
                    ورقة_السماح_بالدخول
                WHERE
                    الرمز = '{self.current_record_id}'
                    AND سماح = 0
                ORDER BY
                    التاريخ DESC, الوقت DESC
            """

            if hasattr(self.db, 'isOpen') and self.db.isOpen():
                # استخدام واجهة QSql إذا كان الاتصال من نوع QSqlDatabase
                print(f"جاري تنفيذ استعلام QSql للطالب {self.current_record_id}")

                # تنفيذ استعلام السماح
                entry_query_obj = QSqlQuery(self.db)
                if entry_query_obj.exec_(entry_query):
                    self.entry_records_model.setQuery(entry_query_obj)
                    print(f"تم تحميل {self.entry_records_model.rowCount()} سجل سماح")
                else:
                    print(f"خطأ في تنفيذ استعلام السماح: {entry_query_obj.lastError().text()}")

                # تنفيذ استعلام التأخر
                late_query_obj = QSqlQuery(self.db)
                if late_query_obj.exec_(late_query):
                    self.late_records_model.setQuery(late_query_obj)
                    print(f"تم تحميل {self.late_records_model.rowCount()} سجل تأخر")
                else:
                    print(f"خطأ في تنفيذ استعلام التأخر: {late_query_obj.lastError().text()}")

            elif self.conn:
                # استخدام واجهة sqlite3 إذا كان الاتصال من نوع sqlite3.Connection
                print(f"جاري تنفيذ استعلام SQLite للطالب {self.current_record_id}")

                # تنفيذ استعلام السماح
                self.cursor.execute(entry_query)
                entry_columns = [desc[0] for desc in self.cursor.description]
                entry_data = self.cursor.fetchall()
                print(f"تم تحميل {len(entry_data)} سجل سماح")

                # تنفيذ استعلام التأخر
                self.cursor.execute(late_query)
                late_columns = [desc[0] for desc in self.cursor.description]
                late_data = self.cursor.fetchall()
                print(f"تم تحميل {len(late_data)} سجل تأخر")

                # نموذج محلي لعرض البيانات
                class CustomSqlModel(QAbstractTableModel):
                    def __init__(self, data, columns, parent=None):
                        super().__init__(parent)
                        self._data = data
                        self._columns = columns

                    def rowCount(self, parent=None):
                        return len(self._data)

                    def columnCount(self, parent=None):
                        return len(self._columns)

                    def data(self, index, role):
                        if not index.isValid():
                            return None
                        if role == Qt.DisplayRole:
                            return str(self._data[index.row()][index.column()])
                        return None

                    def headerData(self, section, orientation, role):
                        if role == Qt.DisplayRole and orientation == Qt.Horizontal:
                            return self._columns[section]
                        return None

                # إنشاء نماذج البيانات
                entry_model = CustomSqlModel(entry_data, entry_columns, self)
                if hasattr(self, 'entry_records_table'):
                    self.entry_records_table.setModel(entry_model)
                    print(f"تم تعيين نموذج السماح المخصص للجدول")

                late_model = CustomSqlModel(late_data, late_columns, self)
                if hasattr(self, 'late_records_table'):
                    self.late_records_table.setModel(late_model)
                    print(f"تم تعيين نموذج التأخر المخصص للجدول")

        except Exception as e:
            print(f"خطأ في تحميل سجلات ورقة السماح بالدخول: {e}")
            traceback.print_exc()

    def open_violations_management(self):
        """فتح نافذة معالجة المخالفات"""
        if not hasattr(self, 'current_record_id') or not self.current_record_id:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحميل بيانات التلميذ أولاً")
            return

        try:
            if VIOLATIONS_MANAGEMENT_WINDOW_AVAILABLE:
                # بدلاً من فتح نافذة جديدة، سنحاول استخدام نافذة مدمجة إن أمكن
                if self.tab_widget.count() > 2:  # تأكد من أن تبويب المخالفات موجود
                    # الانتقال إلى تبويب المخالفات
                    self.tab_widget.setCurrentIndex(2)

                    # إذا كان لدينا تبويبات فرعية للمخالفات، ننتقل إلى تبويب معالجة المخالفات
                    violations_tab = self.tab_widget.widget(2)
                    if hasattr(violations_tab, 'layout') and violations_tab.layout():
                        # البحث عن التبويبات الفرعية
                        for i in range(violations_tab.layout().count()):
                            widget = violations_tab.layout().itemAt(i).widget()
                            if isinstance(widget, QTabWidget):
                                # الانتقال إلى تبويب معالجة المخالفات (عادة المؤشر 1)
                                widget.setCurrentIndex(1)
                                break

                # إنشاء نافذة معالجة المخالفات المدمجة إذا لم تكن موجودة
                if not hasattr(self, 'embedded_violations_management_window') or not self.embedded_violations_management_window:
                    # إضافة إطار مدمج جديد إن لم يكن موجود
                    management_subtab = QWidget()
                    management_layout = QVBoxLayout(management_subtab)
                    management_layout.setContentsMargins(0, 0, 0, 0)

                    # إنشاء نافذة معالجة المخالفات
                    self.embedded_violations_management_window = ViolationsManagementWindow(
                        student_code=self.current_record_id,
                        parent=management_subtab
                    )

                    # تعديل خصائص النافذة لتعمل كعنصر مضمن
                    if hasattr(self.embedded_violations_management_window, 'setWindowFlags'):
                        self.embedded_violations_management_window.setWindowFlags(Qt.Widget)

                    # إخفاء زر الإغلاق إذا كان موجودًا
                    if hasattr(self.embedded_violations_management_window, 'close_button'):
                        self.embedded_violations_management_window.close_button.hide()

                    # الحل: إعداد الإشارة إلى النافذة الأم للمدمجات المنبثقة اللاحقة
                    if hasattr(self.embedded_violations_management_window, 'set_parent_window'):
                        self.embedded_violations_management_window.set_parent_window(management_subtab)

                    # إضافة دالة مخصصة لتحديث معلومات الطالب في نافذة معالجة المخالفات
                    self.add_student_update_adapter_to_violations_management()

                    # الحل البديل: إضافة patch لتعديل سلوك فتح النوافذ المنبثقة
                    # إضافة وظيفة لجعل النوافذ الفرعية مدمجة أيضًا
                    if hasattr(self.embedded_violations_management_window, 'open_edit_window'):
                        original_open_edit = self.embedded_violations_management_window.open_edit_window

                        def wrapped_open_edit(violation_id):
                            """تغليف دالة فتح نافذة التعديل لجعلها مدمجة"""
                            try:
                                if hasattr(self.embedded_violations_management_window, 'edit_window'):
                                    # إغلاق أي نافذة تعديل مفتوحة سابقًا
                                    if self.embedded_violations_management_window.edit_window:
                                        self.embedded_violations_management_window.edit_window.close()

                                # استدعاء الدالة الأصلية
                                edit_window = original_open_edit(violation_id)

                                # تعديل خصائص النافذة لتكون مدمجة
                                if edit_window:
                                    edit_window.setWindowFlags(Qt.Widget)
                                    if hasattr(edit_window, 'close_button'):
                                        # إضافة زر إغلاق إذا لم يكن موجودًا
                                        if not edit_window.close_button:
                                            edit_window.close_button = QPushButton("×")
                                            edit_window.close_button.clicked.connect(edit_window.close)
                                        # جعل زر الإغلاق يعود للوحة الرئيسية بدلاً من إغلاق النافذة
                                        edit_window.close_button.clicked.disconnect()
                                        edit_window.close_button.clicked.connect(lambda: self.tab_widget.widget(2).layout().itemAt(0).widget().setCurrentIndex(1))
                                return edit_window
                            except Exception as e:
                                print(f"خطأ في تعديل نافذة التعديل: {e}")
                                traceback.print_exc()
                                # استخدم الدالة الأصلية في حالة الخطأ
                                return original_open_edit(violation_id)

                        # استبدال الدالة الأصلية بالنسخة المغلفة
                        self.embedded_violations_management_window.open_edit_window = wrapped_open_edit

                    # إضافة النافذة المدمجة إلى التخطيط
                    management_layout.addWidget(self.embedded_violations_management_window)

                    # الحصول على التبويب الفرعي الثاني (معالجة المخالفات) واستبداله
                    violations_tab = self.tab_widget.widget(2)
                    if hasattr(violations_tab, 'layout') and violations_tab.layout():
                        for i in range(violations_tab.layout().count()):
                            widget = violations_tab.layout().itemAt(i).widget()
                            if isinstance(widget, QTabWidget):
                                # الحصول على التبويب الفرعي الثاني
                                if widget.count() > 1:
                                    # استبدال محتوى التبويب الفرعي بالنافذة المدمجة
                                    old_tab = widget.widget(1)
                                    widget.removeTab(1)
                                    widget.insertTab(1, management_subtab, "معالجة المخالفات")
                                break

                    # تحديث المحتوى لعرض بيانات الطالب الحالي من خلال المحول الذي أضفناه
                    self.update_violations_management_student_data()

                else:
                    # إذا كانت النافذة المدمجة موجودة بالفعل، قم بتحديث بيانات الطالب فقط
                    self.update_violations_management_student_data()
            else:
                QMessageBox.warning(self, "تنبيه", "نافذة معالجة المخالفات غير متوفرة")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل نافذة معالجة المخالفات:\n{str(e)}")
            print(f"خطأ في تحميل نافذة معالجة المخالفات: {e}")
            traceback.print_exc()

    def add_student_update_adapter_to_violations_management(self):
        """إضافة محول (adapter) لتحديث بيانات الطالب في نافذة معالجة المخالفات"""
        if hasattr(self, 'embedded_violations_management_window') and self.embedded_violations_management_window:
            # إضافة دالة تحديث بيانات الطالب مباشرة كدالة في النافذة
            def update_student_data_adapter(student_code):
                """دالة وسيطة لتحديث بيانات الطالب في نافذة معالجة المخالفات"""
                try:
                    # طرق مختلفة محتملة لتحديث بيانات الطالب استناداً إلى الإصدارات المختلفة من ViolationsManagementWindow

                    # إذا كان هناك جدول للمخالفات وله تحديث بيانات
                    if hasattr(self.embedded_violations_management_window, 'violations_table'):
                        violations_table = self.embedded_violations_management_window.violations_table
                        if hasattr(violations_table, 'filter_student'):
                            violations_table.filter_student(student_code)
                            print(f"تم تحديث بيانات الطالب في جدول المخالفات: {student_code}")
                            return True

                    # إذا كان هناك نموذج بيانات للمخالفات
                    if hasattr(self.embedded_violations_management_window, 'violations_model'):
                        if hasattr(self.embedded_violations_management_window.violations_model, 'setFilter'):
                            # عادة ما تكون هناك عبارة WHERE مخصصة
                            filter_clause = f"رمز_التلميذ = '{student_code}'"
                            self.embedded_violations_management_window.violations_model.setFilter(filter_clause)
                            print(f"تم تحديث تصفية نموذج المخالفات: {filter_clause}")
                            return True

                    # قد تكون هناك دالة تحديث مباشرة
                    if hasattr(self.embedded_violations_management_window, 'update_for_student'):
                        self.embedded_violations_management_window.update_for_student(student_code)
                        print(f"تم استدعاء update_for_student: {student_code}")
                        return True

                    # إذا كان هناك عنصر واجهة يعرض اسم الطالب
                    if hasattr(self.embedded_violations_management_window, 'student_info_label') and \
                       hasattr(self, 'name_display') and hasattr(self, 'level_display') and \
                       hasattr(self, 'class_display'):
                        student_name = self.name_display.text()
                        level = self.level_display.text()
                        class_name = self.class_display.text()
                        info_text = f"{student_name} ({level} / {class_name})"
                        self.embedded_violations_management_window.student_info_label.setText(info_text)
                        print(f"تم تحديث عنوان معلومات الطالب: {info_text}")
                        return True

                    # إذا كان هناك زر تحديث يمكن النقر عليه
                    if hasattr(self.embedded_violations_management_window, 'refresh_button') and \
                       hasattr(self.embedded_violations_management_window.refresh_button, 'click'):
                        self.embedded_violations_management_window.refresh_button.click()
                        print("تم النقر على زر التحديث تلقائيًا")
                        return True

                    # عدم وجود طريقة معروفة للتحديث
                    print("لم يتم العثور على طريقة مناسبة لتحديث بيانات الطالب في نافذة المخالفات")
                    return False
                except Exception as e:
                    print(f"خطأ في دالة محول تحديث بيانات الطالب: {e}")
                    traceback.print_exc()
                    return False

            # تعيين الدالة الجديدة في كائن النافذة
            self.embedded_violations_management_window.update_student_data_adapter = update_student_data_adapter
            print("تم إضافة دالة محول تحديث بيانات الطالب إلى نافذة معالجة المخالفات")

    def update_violations_management_student_data(self):
        """تحديث بيانات الطالب في نافذة معالجة المخالفات باستخدام المحول"""
        if hasattr(self, 'embedded_violations_management_window') and self.embedded_violations_management_window:
            if hasattr(self, 'current_record_id') and self.current_record_id:
                # استخدام المحول الذي أضفناه
                if hasattr(self.embedded_violations_management_window, 'update_student_data_adapter'):
                    success = self.embedded_violations_management_window.update_student_data_adapter(self.current_record_id)
                    if success:
                        print(f"تم تحديث بيانات الطالب بنجاح في نافذة معالجة المخالفات: {self.current_record_id}")
                    else:
                        print(f"حاولنا تحديث بيانات الطالب من خلال المحول، لكن لم ننجح: {self.current_record_id}")

                        # محاولة بديلة: تحديث العنوان فقط بمعلومات الطالب إذا أمكن
                        if hasattr(self.embedded_violations_management_window, 'setWindowTitle'):
                            student_name = self.name_display.text() if hasattr(self, 'name_display') else ""
                            title = f"معالجة مخالفات: {student_name} ({self.current_record_id})"
                            self.embedded_violations_management_window.setWindowTitle(title)
                            print(f"تم تحديث عنوان النافذة بمعلومات الطالب: {title}")
                else:
                    print("لم يتم العثور على دالة المحول لتحديث بيانات الطالب")
            else:
                print("لا يوجد رمز طالب محدد لتحديث بيانات النافذة")

    def handle_violations_subtab_change(self, index):
        """معالجة تغيير التبويب الفرعي في قسم المخالفات"""
        # طباعة معلومات تشخيصية
        print(f"تم استدعاء handle_violations_subtab_change مع المؤشر {index}")

        # تأخير تنفيذ العمليات الثقيلة لتحسين الاستجابة
        QTimer.singleShot(50, lambda: self._delayed_violations_subtab_handler(index))

    def _delayed_violations_subtab_handler(self, index):
        """معالجة تغيير التبويب الفرعي في قسم المخالفات بعد تأخير قصير"""
        try:
            # طباعة معلومات تشخيصية
            print(f"تنفيذ _delayed_violations_subtab_handler مع المؤشر {index}")

            # إذا كان التبويب المختار هو "معالجة المخالفات"
            if index == 1:
                # التحقق مما إذا كانت النافذة موجودة بالفعل
                if not hasattr(self, 'embedded_violations_management_window') or not self.embedded_violations_management_window:
                    # فتح نافذة معالجة المخالفات كعنصر مضمن فقط إذا لم تكن موجودة
                    print("فتح نافذة معالجة المخالفات لأول مرة")
                    self.open_violations_management()
                    # تطبيق التصحيح على نافذة معالجة المخالفات لدعم النوافذ المنبثقة المدمجة
                    if hasattr(self, 'patch_violations_management_window'):
                        self.patch_violations_management_window()
                else:
                    # إذا كانت النافذة موجودة بالفعل، فقط قم بتحديث البيانات
                    print("تحديث بيانات نافذة معالجة المخالفات الموجودة")
                    self.update_violations_management_student_data()

                # تعيين مؤشر اليد للتبويبات الفرعية مرة أخرى بعد التبديل
                QTimer.singleShot(100, self.set_hand_cursor_for_all_tabs)

            # إذا كان التبويب المختار هو "مسك المخالفات"
            elif index == 0:
                print("تم الانتقال إلى تبويب مسك المخالفات")
                if hasattr(self, 'embedded_violations_window'):
                    # تحديث بيانات التلميذ في نافذة المخالفات المضمنة عند العودة إليها
                    print("تحديث بيانات نافذة مسك المخالفات")
                    self.update_embedded_violations_window()

                # تعيين مؤشر اليد للتبويبات الفرعية مرة أخرى بعد التبديل
                QTimer.singleShot(100, self.set_hand_cursor_for_all_tabs)
        except Exception as e:
            print(f"خطأ في معالجة تغيير التبويب الفرعي للمخالفات: {e}")
            traceback.print_exc()

    def handle_absence_subtab_change(self, index):
        """معالجة تغيير التبويب الفرعي في قسم تبرير الغياب"""
        # تأخير تنفيذ العمليات الثقيلة لتحسين الاستجابة
        QTimer.singleShot(10, lambda: self._delayed_absence_subtab_handler(index))

    def _delayed_absence_subtab_handler(self, index):
        """معالجة تغيير التبويب الفرعي في قسم تبرير الغياب بعد تأخير قصير"""
        try:
            # إذا كان التبويب المختار هو "مسك تبرير الغياب"
            if index == 0:
                # التحقق مما إذا كانت النافذة موجودة بالفعل
                if not hasattr(self, 'embedded_absence_window') or not self.embedded_absence_window:
                    # إنشاء نافذة مسك تبرير الغياب إذا لم تكن موجودة
                    self.create_embedded_absence_window()
                else:
                    # تحديث بيانات الطالب في نافذة تبرير الغياب المضمنة
                    self.update_embedded_absence_window()
            # إذا كان التبويب المختار هو "معالجة تبرير الغياب"
            elif index == 1:
                # التحقق مما إذا كانت النافذة موجودة بالفعل
                if not hasattr(self, 'embedded_absence_management_window') or not self.embedded_absence_management_window:
                    # إنشاء نافذة معالجة تبرير الغياب إذا لم تكن موجودة
                    self.create_embedded_absence_management_window()
                else:
                    # تحديث البيانات في نافذة معالجة تبرير الغياب عند الانتقال إليها
                    self.update_embedded_absence_management_window()
        except Exception as e:
            print(f"خطأ في معالجة تغيير التبويب الفرعي لتبرير الغياب: {e}")
            traceback.print_exc()

    def handle_visits_subtab_change(self, index):
        """معالجة تغيير التبويب الفرعي في قسم زيارات أولياء الأمور"""
        # تأخير تنفيذ العمليات الثقيلة لتحسين الاستجابة
        QTimer.singleShot(50, lambda: self._delayed_visits_subtab_handler(index))

    def _delayed_visits_subtab_handler(self, index):
        """معالجة تغيير التبويب الفرعي في قسم زيارات أولياء الأمور بعد تأخير قصير"""
        try:
            # إذا كان التبويب المختار هو "استعراض سجل الزيارات"
            if index == 1:
                # بدلاً من فتح نافذة منفصلة، نقوم بتضمين النافذة داخل التبويب
                self.update_embedded_parent_visit_management_window()
            # إذا كان التبويب المختار هو "توثيق زيارة جديدة"
            elif index == 0 and hasattr(self, 'embedded_parent_visit'):
                # تحديث بيانات الطالب في نافذة توثيق الزيارة المضمنة
                self.update_embedded_parent_visit_window()
        except Exception as e:
            print(f"خطأ في معالجة تغيير التبويب الفرعي لزيارات أولياء الأمور: {e}")
            traceback.print_exc()

    def view_parent_visits(self):
        """عرض سجلات زيارات أولياء الأمور للتلميذ الحالي"""
        if not hasattr(self, 'current_record_id') or not self.current_record_id:
            QMessageBox.warning(self, "تنبيه", "الرجاء تحميل بيانات التلميذ أولاً")
            return

        try:
            print("عرض سجلات زيارات أولياء الأمور")
            # الانتقال إلى تبويب زيارات أولياء الأمور
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "زيارات أولياء الأمور":
                    self.tab_widget.setCurrentIndex(i)
                    break

            # البحث عن التبويبات الفرعية وتبديل إلى تبويب استعراض السجلات
            visits_tab = self.tab_widget.currentWidget()
            if visits_tab:
                for i in range(visits_tab.layout().count()):
                    widget = visits_tab.layout().itemAt(i).widget()
                    if isinstance(widget, QTabWidget):
                        for j in range(widget.count()):
                            if widget.tabText(j) == "استعراض سجل الزيارات":
                                widget.setCurrentIndex(j)
                                break
                        break

            # بدلاً من فتح نافذة منفصلة، نقوم بتحديث النافذة المضمنة
            self.update_embedded_parent_visit_management_window()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح سجلات زيارات أولياء الأمور:\n{str(e)}")
            print(f"خطأ في فتح سجلات زيارات أولياء الأمور: {e}")
            traceback.print_exc()

    def patch_violations_management_window(self):
        """إصلاح وظائف نافذة معالجة المخالفات لدعم تسلسل عمل التعديل المحسّن"""
        if hasattr(self, 'embedded_violations_management_window') and self.embedded_violations_management_window:
            # تصحيح التفاعل مع جدول المخالفات
            if hasattr(self.embedded_violations_management_window, 'violations_table'):
                violations_table = self.embedded_violations_management_window.violations_table

                # إضافة طريقة للتعامل مع حدث تحديد الصفوف
                if hasattr(violations_table, 'selectionModel'):
                    selection_model = violations_table.selectionModel()

                    # إزالة أي اتصالات سابقة لتجنب الازدواجية
                    try:
                        selection_model.selectionChanged.disconnect()
                    except:
                        pass  # تجاهل الأخطاء إذا لم تكن هناك اتصالات

                    # إضافة اتصال جديد
                    selection_model.selectionChanged.connect(self.handle_violation_selection)

                    print("تم إعداد مراقبة تحديد المخالفات لدعم تسلسل عمل التعديل الجديد")

                # تم إزالة الكود الخاص بإضافة زر التعديل في تبويب مسك المخالفات

            # استبدال طريقة فتح نافذة التعديل الأصلية
            if hasattr(self.embedded_violations_management_window, 'open_edit_window'):
                # حفظ الدالة الأصلية في متغير محلي
                _original_open_edit = self.embedded_violations_management_window.open_edit_window

                def wrapped_open_edit(violation_id):
                    """وظيفة مغلفة تحفظ معرف المخالفة وتنتقل إلى تبويب مسك المخالفات"""
                    # استخدام الدالة الأصلية المحفوظة في المتغير المحلي
                    nonlocal _original_open_edit
                    # تخزين معرف المخالفة المحددة
                    self.selected_violation_for_edit = violation_id

                    # الانتقال مباشرة إلى تبويب مسك المخالفات دون طرح سؤال
                    self.edit_selected_violation_in_tab()
                    return None

                # تحديث وظيفة فتح نافذة التعديل
                self.embedded_violations_management_window.open_edit_window = wrapped_open_edit

            print("تم تطبيق التصحيحات على نافذة معالجة المخالفات")

    def handle_violation_selection(self, selected, *_args):
        """معالجة تغيير تحديد المخالفات في جدول المخالفات"""
        # الحصول على الصفوف المحددة
        selected_rows = selected.indexes()

        if selected_rows and hasattr(self, 'embedded_violations_management_window'):
            try:
                # الحصول على الصف المحدد
                row = selected_rows[0].row()

                # الحصول على نموذج البيانات
                model = self.embedded_violations_management_window.violations_table.model()

                # الحصول على معرف المخالفة من العمود الأول
                violation_id = model.index(row, 0).data()

                # تخزين معرف المخالفة المحددة
                self.selected_violation_for_edit = violation_id

                print(f"تم تحديد المخالفة رقم: {violation_id} للتعديل")

                # تم إزالة الكود الخاص بتمكين زر التعديل
            except Exception as e:
                print(f"خطأ في معالجة تحديد المخالفة: {e}")
                traceback.print_exc()

                # تعيين متغير المخالفة المحددة للتعديل إلى None في حالة حدوث خطأ
                self.selected_violation_for_edit = None
        else:
            # تعيين متغير المخالفة المحددة للتعديل إلى None إذا لم يكن هناك صفوف محددة
            self.selected_violation_for_edit = None

    def edit_selected_violation_in_tab(self):
        """تحرير المخالفة المحددة في تبويب مسك المخالفات"""
        # التأكد من وجود معرف مخالفة محدد
        if not self.selected_violation_for_edit:
            QMessageBox.warning(self, "تنبيه", "لم يتم تحديد أي مخالفة للتعديل")
            return

        try:
            # تبديل إلى تبويب المخالفات الرئيسي
            self.tab_widget.setCurrentIndex(2)  # تبويب المخالفات

            # البحث عن التبويبات الفرعية وتبديل إلى تبويب مسك المخالفات
            violations_tab = self.tab_widget.widget(2)

            if hasattr(violations_tab, 'layout') and violations_tab.layout():
                for i in range(violations_tab.layout().count()):
                    widget = violations_tab.layout().itemAt(i).widget()
                    if isinstance(widget, QTabWidget):
                        # تبديل إلى تبويب مسك المخالفات (المؤشر 0)
                        widget.setCurrentIndex(0)
                        break

            # انتظار لتحديث واجهة المستخدم
            QApplication.processEvents()

            # التأكد من وجود نافذة مخالفات مضمنة
            if not hasattr(self, 'embedded_violations_window') or not self.embedded_violations_window:
                QMessageBox.warning(self, "تنبيه", "نافذة مسك المخالفات غير متاحة")
                return

            # التحقق من وجود دالة لتحميل المخالفة للتعديل
            if hasattr(self.embedded_violations_window, 'load_violation_for_edit'):
                # تحميل المخالفة للتعديل
                result = self.embedded_violations_window.load_violation_for_edit(self.selected_violation_for_edit)

                if result:
                    print(f"تم تحميل المخالفة {self.selected_violation_for_edit} للتعديل في تبويب مسك المخالفات")
                    # عرض رسالة للتأكيد
                    QMessageBox.information(
                        self,
                        "تحرير المخالفة",
                        f"تم تحميل المخالفة رقم {self.selected_violation_for_edit} للتعديل.\n"
                        "يمكنك الآن تعديل المخالفة في نموذج مسك المخالفات."
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "تنبيه",
                        f"تعذر تحميل المخالفة رقم {self.selected_violation_for_edit} للتعديل"
                    )
            else:
                QMessageBox.warning(self, "تنبيه", "وظيفة تحميل المخالفة للتعديل غير متاحة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة تحرير المخالفة:\n{str(e)}")
            print(f"خطأ في تحرير المخالفة في التبويب: {e}")
            traceback.print_exc()

    def closeEvent(self, event):
        if self.conn:
            self.conn.close()

        # تأكيد قبل الإغلاق بدلاً من الإغلاق المباشر، يمكن تعليقه إذا لم تكن هناك حاجة لتأكيد الإغلاق
        """
        reply = QMessageBox.question(
            self, 'تأكيد الإغلاق',
            'هل تريد إغلاق هذه النافذة؟',
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
        """

        # قبول حدث الإغلاق
        event.accept()

class QuickNotesDialog(QDialog):
    """نافذة إضافة ملاحظات مختصرة"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_note = ""
        self.initUI()

    def initUI(self):
        # تعيين خصائص النافذة
        self.setWindowTitle("إضافة ملاحظات مختصرة")
        self.setFixedSize(400, 300)
        self.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            self.setWindowIcon(app_icon)
        except Exception as e:
            # في حالة عدم وجود الأيقونة، نطبع الخطأ ونتجاهله
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")
            pass

        self.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #3498db;
                border-radius: 5px;
            }
            QLabel {
                font-weight: bold;
                color: #2c3e50;
            }
            QListWidget {
                background-color: white;
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
            QLineEdit {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 5px;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # إضافة عنوان
        title_label = QLabel("اختر ملاحظة مختصرة أو أضف ملاحظة جديدة")
        title_label.setFont(QFont("Calibri", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # إضافة قائمة الملاحظات المختصرة
        self.notes_list = QListWidget()
        self.notes_list.setFont(QFont("Calibri", 12))

        # إضافة الملاحظات المختصرة الافتراضية مع أيقونات مميزة
        self.add_default_notes()

        # ربط حدث النقر المزدوج بدالة اختيار الملاحظة
        self.notes_list.itemDoubleClicked.connect(self.select_note)

        layout.addWidget(self.notes_list)

        # إضافة حقل لإدخال ملاحظة جديدة
        input_layout = QHBoxLayout()

        self.new_note_input = QLineEdit()
        self.new_note_input.setFont(QFont("Calibri", 12))
        self.new_note_input.setPlaceholderText("أدخل ملاحظة جديدة هنا...")

        add_button = QPushButton("إضافة")
        add_button.setFont(QFont("Calibri", 12, QFont.Bold))
        add_button.setCursor(Qt.PointingHandCursor)
        add_button.clicked.connect(self.add_new_note)

        input_layout.addWidget(self.new_note_input)
        input_layout.addWidget(add_button)

        layout.addLayout(input_layout)

        # إضافة أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        select_button = QPushButton("اختيار")
        select_button.setFont(QFont("Calibri", 12, QFont.Bold))
        select_button.setCursor(Qt.PointingHandCursor)
        select_button.clicked.connect(self.select_note)

        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_button.setCursor(Qt.PointingHandCursor)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(select_button)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def add_default_notes(self):
        """إضافة الملاحظات المختصرة الافتراضية مع أيقونات مميزة"""
        default_notes = [
            "❌ تم اخفاء التلميذ(ة) من اللوائح",
            "⚠️ يجب احضار ولي أمره",
            "📄 ملفه المدرسي تنقصه بعض الوثائق",
            "⏰ يعاني من مشكلة التأخر المتكرر",
            "🔔 تنبيه بخصوص السلوك",
            "📝 بحاجة إلى متابعة خاصة",
            "🏥 يعاني من مرض مزمن"
        ]

        for note_text in default_notes:
            item = QListWidgetItem(note_text)
            self.notes_list.addItem(item)

    def add_new_note(self):
        """إضافة ملاحظة جديدة إلى القائمة"""
        note_text = self.new_note_input.text().strip()
        if note_text:
            # إضافة الملاحظة الجديدة
            item = QListWidgetItem(note_text)
            self.notes_list.addItem(item)
            self.new_note_input.clear()

            # تحديد الملاحظة الجديدة
            self.notes_list.setCurrentItem(item)

    def select_note(self):
        """اختيار الملاحظة المحددة وإغلاق النافذة"""
        current_item = self.notes_list.currentItem()
        if current_item:
            self.selected_note = current_item.text()
            self.accept()
        else:
            QMessageBox.warning(self, "تنبيه", "الرجاء اختيار ملاحظة أولاً")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # Set RTL for the entire application
    window = StudentCardWindow()
    # Ensure the window is visible by explicitly showing it
    window.show()
    # Print confirmation message to console
    print("النافذة الرئيسية تم إظهارها. إذا كنت لا ترى النافذة، تحقق من أنها ليست خلف نوافذ أخرى.")
    # Make sure window appears on top of other windows
    window.raise_()
    window.activateWindow()
    # Enter the Qt event loop and exit when it's done
    sys.exit(app.exec_())






