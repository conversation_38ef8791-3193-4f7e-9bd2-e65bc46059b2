#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from sub100_window import ConfirmationDialogs

# تضمين دالة إشعار الشهادات الطبية من print6.py
try:
    from print6 import print_medical_certificate_notification
    PRINT6_AVAILABLE = True
    print("تم استيراد دالة إشعار الشهادات الطبية من print6.py بنجاح")
except ImportError:
    PRINT6_AVAILABLE = False
    print("تحذير: فشل استيراد print_medical_certificate_notification من print6.py")
    # Define dummy function if import fails
    def print_medical_certificate_notification(*args, **kwargs):
        print("خطأ: دالة الطباعة print_medical_certificate_notification غير متوفرة.")
        return False

# تضمين دالة طباعة سجلات الدخول والتأخر من print2.py
try:
    from print2 import print_entry_records
    PRINT2_AVAILABLE = True
    print("تم استيراد دالة طباعة سجلات الدخول والتأخر من print2.py بنجاح")
except ImportError:
    PRINT2_AVAILABLE = False
    print("تحذير: فشل استيراد print_entry_records من print2.py")
    # Define dummy function if import fails
    def print_entry_records(*args, **kwargs):
        print("خطأ: دالة الطباعة print_entry_records غير متوفرة.")
        return False

class UniversalStudentRecordsWindow(QMainWindow):
    """نافذة عرض سجلات جميع التلاميذ - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, record_type="entry_permissions", parent=None):
        # إنشاء النافذة بدون parent لضمان فتحها في كامل الشاشة
        super().__init__(None)

        # تعيين خصائص النافذة لضمان فتحها في كامل الشاشة
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        self.setAttribute(Qt.WA_DeleteOnClose, True)

        # المتغيرات الأساسية
        self.parent_window = parent
        self.record_type = record_type
        self.records_data = []
        self.filtered_data = []
        
        # إعدادات أنواع السجلات
        self.record_configs = {
            "entry_permissions": {
                "title": "🚪 سجل السماح بالدخول والتأخر",
                "table": "ورقة_السماح_بالدخول",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_والنسب", "اسم التلميذ"),
                    ("الوقت", "الوقت"),
                    ("ورقة_السماح", "نوع السماح"),
                    ("السنة_الدراسية", "السنة الدراسية"),
                    ("الأسدس", "الأسدس"),
                    ("رقم_الورقة", "رقم الورقة")
                ],
                "filters": [
                    ("ورقة_السماح", "📋 نوع السماح"),
                    ("الأسدس", "� الأسدس")
                ],
                "stats": [
                    ("إجمالي السجلات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("سماح دخول", "len([row for row in data if str(row[5]).strip() == 'سماح'])"),
                    ("تأخر", "len([row for row in data if str(row[5]).strip() == 'تأخر'])"),
                    ("الأسدس المختلفة", "len(set(row[7] for row in data if row[7]))")
                ]
            },
            "absence_records": {
                "title": "📝 سجل الغياب الأسبوعي",
                "table": "مسك_الغياب_الأسبوعي",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("رمز_التلميذ", "رمز التلميذ"),
                    ("اسم_التلميذ", "اسم التلميذ"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("غياب_غير_مبرر", "غياب غير مبرر"),
                    ("السنة_الدراسية", "السنة الدراسية"),
                    ("الأسدس", "الأسدس")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى")
                ],
                "stats": [
                    ("إجمالي السجلات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("الأقسام", "len(set(row[4] for row in data if row[4]))"),
                    ("إجمالي أيام الغياب", "sum(int(row[6] or 0) for row in data)")
                ]
            },
            "doctor_visits": {
                "title": "🏥 سجل زيارات الطبيب",
                "table": "زيارة_الطبيب",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_والنسب", "اسم التلميذ"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("السبب", "سبب الزيارة"),
                    ("الملاحظات", "ملاحظات"),
                    ("الوقت", "الوقت")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى"),
                    ("السبب", "🩺 سبب الزيارة")
                ],
                "stats": [
                    ("إجمالي الزيارات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("الأقسام", "len(set(row[4] for row in data if row[4]))"),
                    ("اليوم", "len([row for row in data if row[1] == datetime.now().strftime('%Y-%m-%d')])")
                ]
            },
            "student_cards": {
                "title": "👤 بطاقات التلاميذ",
                "table": "تلاميذ",
                "columns": [
                    ("rowid", "الرقم"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_واللقب", "اسم التلميذ"),
                    ("تاريخ_الازدياد", "تاريخ الازدياد"),
                    ("مكان_الازدياد", "مكان الازدياد"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("الجنس", "الجنس"),
                    ("رقم_الهاتف", "رقم الهاتف"),
                    ("العنوان", "العنوان")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى"),
                    ("الجنس", "👫 الجنس")
                ],
                "stats": [
                    ("إجمالي التلاميذ", "len(data)"),
                    ("ذكور", "len([row for row in data if str(row[7]).strip() == 'ذكر'])"),
                    ("إناث", "len([row for row in data if str(row[7]).strip() == 'أنثى'])"),
                    ("الأقسام", "len(set(row[5] for row in data if row[5]))")
                ]
            }
        }
        
        # الحصول على إعدادات السجل الحالي
        self.current_config = self.record_configs.get(record_type, self.record_configs["entry_permissions"])
        
        # إعداد النافذة
        self.setupUI()

        # تحميل البيانات
        self.load_records_data()

        # التأكد من فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة timer لضمان فتح النافذة في كامل الشاشة بعد التحميل الكامل
        QTimer.singleShot(100, self.ensure_maximized)

    def showEvent(self, event):
        """التأكد من فتح النافذة في كامل الشاشة عند عرضها"""
        super().showEvent(event)

        # ضمان فتح النافذة في كامل الشاشة
        self.setWindowState(Qt.WindowMaximized)
        self.showMaximized()

        # إضافة timer إضافي للتأكد
        QTimer.singleShot(50, lambda: self.setWindowState(Qt.WindowMaximized))
        QTimer.singleShot(100, self.showMaximized)

    def resizeEvent(self, event):
        """منع تصغير النافذة والحفاظ على كامل الشاشة"""
        super().resizeEvent(event)
        # يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle(f"{self.current_config['title']} ")
        self.setLayoutDirection(Qt.RightToLeft)

        # ضمان فتح النافذة في كامل الشاشة
        self.setWindowState(Qt.WindowMaximized)
        self.showMaximized()

        # إزالة أي قيود على الحجم
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2196F3,
                    stop: 1 #1976d2
                );
                border-radius: 10px;
                padding: 10px;
            }
        """)

        # تحديد حد أقصى لارتفاع شريط الأدوات
        toolbar_frame.setMaximumHeight(200)
        toolbar_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # تغيير التخطيط إلى عمودي لتجنب مشكلة العرض الكبير
        toolbar_layout = QVBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(10)

        # الصف الأول: البحث ونوع السماح
        first_row_layout = QHBoxLayout()
        first_row_layout.setSpacing(15)
        
        # شريط البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("color: darkblue; font-family: Calibri; font-size: 17px; font-weight: bold;")
        first_row_layout.addWidget(search_label)

        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("ابحث في جميع الحقول...")
        self.search_entry.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #1565c0;
                border-radius: 8px;
                padding: 8px 12px;
                font-family: Calibri;
                font-size: 17px;
                color: darkblack;
                font-weight: bold;
                min-width: 250px;
                max-width: 300px;
            }
            QLineEdit:focus {
                border-color: #0d47a1;
                box-shadow: 0 0 5px rgba(13, 71, 161, 0.5);
            }
        """)
        self.search_entry.textChanged.connect(self.on_search)
        first_row_layout.addWidget(self.search_entry)

        first_row_layout.addStretch()
        
        # إضافة فلتر نوع السماح إذا كان موجوداً في السجل الحالي
        self.filter_combos = {}
        if "ورقة_السماح" in [f[0] for f in self.current_config["filters"]]:
            # إنشاء تسمية فلتر نوع السماح
            permission_label = QLabel("📋 نوع السماح:")
            permission_label.setStyleSheet("color: darkblue; font-family: Calibri; font-size: 17px; font-weight: bold;")
            first_row_layout.addWidget(permission_label)
            
            # إنشاء مربع فلتر نوع السماح
            permission_combo = QComboBox()
            permission_combo.setStyleSheet("""
                QComboBox {
                    background: white;
                    border: 2px solid #1565c0;
                    border-radius: 8px;
                    padding: 8px 12px;
                    font-family: Calibri;
                    font-size: 17px;
                    color: darkblack;
                    font-weight: bold;
                    min-width: 120px;
                    max-width: 150px;
                }
                QComboBox:focus {
                    border-color: #0d47a1;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
            """)
            permission_combo.currentTextChanged.connect(self.on_filter)
            first_row_layout.addWidget(permission_combo)
            
            # حفظ مرجع فلتر نوع السماح
            self.filter_combos["ورقة_السماح"] = permission_combo
        
        # زر تحديث البيانات
        self.refresh_button = QPushButton("🔄 تحديث")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #45a049
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-family: Calibri;
                font-size: 17px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5cbf60,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3d8b40,
                    stop: 1 #2e7d32
                );
            }
        """)
        self.refresh_button.clicked.connect(self.load_records_data)
        first_row_layout.addWidget(self.refresh_button)
        
        # الصف الثاني: باقي الفلاتر (بدون نوع السماح)
        second_row_layout = QHBoxLayout()
        second_row_layout.setSpacing(15)

        # إنشاء باقي الفلاتر (استثناء نوع السماح)
        for filter_field, filter_label in self.current_config["filters"]:
            if filter_field != "ورقة_السماح":  # تجاهل نوع السماح لأنه في الصف الأول
                # إنشاء تسمية الفلتر
                label = QLabel(filter_label + ":")
                label.setStyleSheet("color: darkblue; font-family: Calibri; font-size: 17px; font-weight: bold;")
                second_row_layout.addWidget(label)

                # إنشاء مربع الفلتر
                combo = QComboBox()
                combo.setStyleSheet("""
                    QComboBox {
                        background: white;
                        border: 2px solid #1565c0;
                        border-radius: 8px;
                        padding: 8px 12px;
                        font-family: Calibri;
                        font-size: 17px;
                        color: darkblack;
                        font-weight: bold;
                        min-width: 120px;
                        max-width: 150px;
                    }
                    QComboBox:focus {
                        border-color: #0d47a1;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 20px;
                    }
                """)
                combo.currentTextChanged.connect(self.on_filter)
                second_row_layout.addWidget(combo)

                # حفظ مرجع الفلتر
                self.filter_combos[filter_field] = combo

        second_row_layout.addStretch()

        # إضافة الصفوف إلى التخطيط الرئيسي
        toolbar_layout.addLayout(first_row_layout)
        # إضافة الصف الثاني فقط إذا كان يحتوي على فلاتر
        if len([f for f in self.current_config["filters"] if f[0] != "ورقة_السماح"]) > 0:
            toolbar_layout.addLayout(second_row_layout)

        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الأزرار السفلي
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #37474f,
                    stop: 1 #263238
                );
                border-radius: 10px;
                padding: 5px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(15, 10, 15, 10)
        buttons_layout.setSpacing(15)
        
        # زر طباعة التقرير
        self.print_button = QPushButton("📊 تقرير سجلات PDF")
        self.print_button.setFont(QFont("Calibri", 17, QFont.Bold))
        self.print_button.setMinimumHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FF9800,
                    stop: 1 #F57C00
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FFB74D,
                    stop: 1 #FF9800
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #F57C00,
                    stop: 1 #E65100
                );
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.print_button.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_button)
        
        # زر حذف السجلات المحددة
        self.delete_button = QPushButton("🗑️ حذف المحددة")
        self.delete_button.setFont(QFont("Calibri", 17, QFont.Bold))
        self.delete_button.setMinimumHeight(40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f44336,
                    stop: 1 #d32f2f
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f66356,
                    stop: 1 #f44336
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c62828,
                    stop: 1 #b71c1c
                );
            }
        """)
        self.delete_button.clicked.connect(self.delete_selected_records)
        buttons_layout.addWidget(self.delete_button)
        
        # زر محو جميع التحديدات
        self.clear_selections_button = QPushButton("🧹 محو التحديدات")
        self.clear_selections_button.setFont(QFont("Calibri", 17, QFont.Bold))
        self.clear_selections_button.setMinimumHeight(40)
        self.clear_selections_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6c757d,
                    stop: 1 #495057
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #868e96,
                    stop: 1 #6c757d
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #495057,
                    stop: 1 #343a40
                );
            }
        """)
        self.clear_selections_button.clicked.connect(self._clear_all_selections)
        buttons_layout.addWidget(self.clear_selections_button)
        
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def load_records_data(self):
        """تحميل بيانات السجلات من قاعدة البيانات"""
        try:
            self.status_bar.showMessage("جاري تحميل البيانات...")
            
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # بناء استعلام ديناميكي حسب نوع السجل
            table_name = self.current_config["table"]
            columns = [col[0] for col in self.current_config["columns"]]
            
            # التحقق من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                print(f"الجدول {table_name} غير موجود")
                self.status_bar.showMessage(f"الجدول {table_name} غير موجود")
                QMessageBox.warning(self, "تنبيه", f"الجدول {table_name} غير موجود في قاعدة البيانات")
                conn.close()
                return
            
            # التحقق من الأعمدة الموجودة في الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            existing_columns = [column[1] for column in cursor.fetchall()]
            print(f"الأعمدة الموجودة في جدول {table_name}: {existing_columns}")
            
            # تصفية الأعمدة الموجودة فقط
            valid_columns = []
            for col in columns:
                if col == "rowid" or col in existing_columns:
                    valid_columns.append(col)
                else:
                    print(f"العمود {col} غير موجود في الجدول {table_name}")
            
            if not valid_columns:
                print(f"لا توجد أعمدة صالحة في الجدول {table_name}")
                self.status_bar.showMessage("لا توجد أعمدة صالحة")
                conn.close()
                return
            
            # تحديث تكوين الأعمدة مع الأعمدة الصالحة فقط
            original_columns = self.current_config["columns"]
            self.current_config["columns"] = [
                (col[0], col[1]) for col in original_columns 
                if col[0] == "rowid" or col[0] in existing_columns
            ]
            
            # تحميل جميع السجلات مع ترتيب آمن
            try:
                # محاولة الترتيب حسب التاريخ إذا كان موجوداً
                if "التاريخ" in valid_columns:
                    query = f"SELECT {', '.join(valid_columns)} FROM {table_name} ORDER BY التاريخ DESC"
                else:
                    query = f"SELECT {', '.join(valid_columns)} FROM {table_name} ORDER BY rowid DESC"
                
                cursor.execute(query)
                self.records_data = cursor.fetchall()
                print(f"تم تحميل {len(self.records_data)} سجل من جدول {table_name}")
                
            except Exception as e:
                print(f"خطأ في استعلام البيانات: {e}")
                # محاولة استعلام بسيط بدون ترتيب
                query = f"SELECT {', '.join(valid_columns)} FROM {table_name}"
                cursor.execute(query)
                self.records_data = cursor.fetchall()
            
            # تحميل قوائم الفلاتر
            for filter_field, _ in self.current_config["filters"]:
                if filter_field in self.filter_combos and filter_field in existing_columns:
                    # الفلاتر العادية الموجودة في الجدول الأساسي
                    try:
                        cursor.execute(f'SELECT DISTINCT {filter_field} FROM {table_name} WHERE {filter_field} IS NOT NULL AND {filter_field} != ""')
                        values = [row[0] for row in cursor.fetchall()]
                        
                        combo = self.filter_combos[filter_field]
                        combo.clear()
                        combo.addItems(['الكل'] + sorted([str(v) for v in values if v]))
                        print(f"تم تحميل {len(values)} عنصر للفلتر {filter_field}")
                    except Exception as e:
                        print(f"خطأ في تحميل فلتر {filter_field}: {e}")
            
            conn.close()
            
            # تطبيق الفلاتر وإنشاء HTML
            self.apply_filters()
            
            self.status_bar.showMessage(f"تم تحميل {len(self.records_data)} سجل")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def apply_filters(self):
        """تطبيق الفلاتر على البيانات"""
        search_term = self.search_entry.text().lower()
        
        self.filtered_data = []
        for row in self.records_data:
            # تطبيق فلاتر مربعات الاختيار
            skip_row = False
            for i, (filter_field, _) in enumerate(self.current_config["filters"]):
                if filter_field in self.filter_combos:
                    filter_value = self.filter_combos[filter_field].currentText()
                    if filter_value != 'الكل':
                        # العثور على فهرس العمود في البيانات
                        column_names = [col[0] for col in self.current_config["columns"]]
                        if filter_field in column_names:
                            column_index = column_names.index(filter_field)
                            if column_index < len(row) and str(row[column_index]) != filter_value:
                                skip_row = True
                                break
            
            if skip_row:
                continue
                
            # تطبيق البحث النصي
            if search_term:
                row_text = ' '.join(str(cell or '') for cell in row).lower()
                if search_term not in row_text:
                    continue
                    
            self.filtered_data.append(row)
        
        # إنشاء وعرض HTML
        self.generate_html()
    
    def on_search(self):
        """معالج البحث"""
        self.apply_filters()
    
    def on_filter(self):
        """معالج التصفية"""
        self.apply_filters()
    
    def generate_html(self):
        """توليد HTML لعرض السجلات"""
        data_to_display = self.filtered_data
        html_content = self.create_html_template(data_to_display)
        self.web_view.setHtml(html_content)
    
    def create_html_template(self, data):
        """إنشاء قالب HTML حديث"""
        # حساب الإحصائيات
        stats_html = ""
        for stat_name, stat_formula in self.current_config["stats"]:
            try:
                stat_value = eval(stat_formula)
                stats_html += f"""
                <div class="stat-card">
                    <div class="stat-number">{stat_value}</div>
                    <div>{stat_name}</div>
                </div>
                """
            except:
                stats_html += f"""
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div>{stat_name}</div>
                </div>
                """
        
        # إنشاء رؤوس الجدول
        table_headers = """
        <th>
            <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
            اختيار الكل
        </th>
        """
        
        for _, column_title in self.current_config["columns"]:
            table_headers += f"<th>{column_title}</th>"
        
        # إنشاء صفوف الجدول
        table_rows = ""
        for row in data:
            row_html = '<tr class="record-row">'
            
            # إضافة مربع الاختيار
            row_html += f"""
            <td class="record-checkbox">
                <input type="checkbox" class="record-select" 
                       data-id="{row[0] or ''}"
                       data-student-name="{row[3] if len(row) > 3 else ''}"
                       data-date="{row[1] if len(row) > 1 else ''}">
            </td>
            """
            
            # إضافة بيانات الصف
            for i, cell in enumerate(row):
                if i == 0:  # العمود الأول (الرقم)
                    row_html += f'<td><span class="record-id">{cell or "غير محدد"}</span></td>'
                elif "تاريخ" in self.current_config["columns"][i][1].lower():
                    row_html += f'<td class="date">{cell or "غير محدد"}</td>'
                elif "اسم" in self.current_config["columns"][i][1].lower():
                    row_html += f'<td class="student-name">{cell or "غير محدد"}</td>'
                elif "سماح" in self.current_config["columns"][i][1].lower():
                    cell_value = str(cell or "غير محدد").strip()
                    if cell_value == "سماح":
                        row_html += f'<td><span class="permission-tag entry">{cell_value}</span></td>'
                    elif cell_value == "تأخر":
                        row_html += f'<td><span class="permission-tag late">{cell_value}</span></td>'
                    else:
                        row_html += f'<td>{cell_value}</td>'
                else:
                    row_html += f'<td>{cell or "غير محدد"}</td>'
            
            row_html += '</tr>'
            table_rows += row_html
        
        # HTML الكامل
        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.current_config['title']}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-family: 'Calibri', sans-serif;
            font-size: 30px;
            font-weight: bold;
            color: #f8f9fa;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }}
        
        .table-container {{
            padding: 20px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        th {{
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        td {{
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }}
        
        tr:hover td {{
            background-color: #f8f9fa;
        }}
        
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .record-id {{
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }}
        
        .student-name {{
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .date {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .permission-tag {{
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9em;
        }}
        
        .permission-tag.entry {{
            background: #27ae60;
            color: white;
        }}
        
        .permission-tag.late {{
            background: #e74c3c;
            color: white;
        }}
        
        .record-checkbox {{
            text-align: center;
            width: 60px;
        }}
        
        .record-select {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        #select-all {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        .record-row.selected {{
            background-color: #e3f2fd !important;
            border-right: 4px solid #2196f3;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }}
        
        .empty-icon {{
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            opacity: 0.5;
        }}
    </style>
    
    <script>
        function toggleSelectAll() {{
            const selectAllBox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.record-select');
            
            checkboxes.forEach(checkbox => {{
                checkbox.checked = selectAllBox.checked;
                updateRowSelection(checkbox);
            }});
        }}
        
        function updateRowSelection(checkbox) {{
            const row = checkbox.closest('tr');
            if (checkbox.checked) {{
                row.classList.add('selected');
            }} else {{
                row.classList.remove('selected');
            }}
        }}
        
        function getSelectedRecords() {{
            const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
            const selectedRecords = [];
            
            selectedCheckboxes.forEach(checkbox => {{
                selectedRecords.push({{
                    id: checkbox.getAttribute('data-id'),
                    studentName: checkbox.getAttribute('data-student-name'),
                    date: checkbox.getAttribute('data-date')
                }});
            }});
            
            return selectedRecords;
        }}
        
        document.addEventListener('DOMContentLoaded', function() {{
            const checkboxes = document.querySelectorAll('.record-select');
            checkboxes.forEach(checkbox => {{
                checkbox.addEventListener('change', function() {{
                    updateRowSelection(this);
                    
                    const allCheckboxes = document.querySelectorAll('.record-select');
                    const checkedBoxes = document.querySelectorAll('.record-select:checked');
                    const selectAllBox = document.getElementById('select-all');
                    
                    if (checkedBoxes.length === 0) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = false;
                    }} else if (checkedBoxes.length === allCheckboxes.length) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = true;
                    }} else {{
                        selectAllBox.indeterminate = true;
                    }}
                }});
            }});
        }});
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{self.current_config['title']}</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            <p style="font-size: 0.9em; opacity: 0.8;">📊 لإنشاء تقرير PDF: حدد السجلات المطلوبة (اختيارية) ثم اضغط على زر "تقرير سجلات PDF"</p>
        </div>
        
        <div class="stats">
            {stats_html}
        </div>
        
        <div class="table-container">
        """
        
        if data:
            html += f"""
            <table>
                <thead>
                    <tr>
                        {table_headers}
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>
            """
        else:
            html += """
            <div class="empty-state">
                <span class="empty-icon">📋</span>
                <h3>لا توجد سجلات</h3>
                <p>لم يتم العثور على أي سجلات تطابق معايير البحث</p>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def delete_selected_records(self):
        """حذف السجلات المحددة"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل أنت متأكد من حذف السجلات المحددة؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تنفيذ جافا سكريبت للحصول على السجلات المحددة
            js_code = """
            (function() {
                const selectedRecords = getSelectedRecords();
                return JSON.stringify(selectedRecords);
            })();
            """
            
            def handle_selected_records(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    selected_records = json.loads(result)
                    if not selected_records:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    # حذف السجلات من قاعدة البيانات
                    conn = sqlite3.connect('data.db')
                    cursor = conn.cursor()
                    deleted_count = 0
                    
                    for record in selected_records:
                        # استخدام rowid بدلاً من id
                        cursor.execute(
                            f"DELETE FROM {self.current_config['table']} WHERE rowid = ?",
                            (record['id'],)
                        )
                        if cursor.rowcount > 0:
                            deleted_count += 1
                    
                    conn.commit()
                    conn.close()
                    
                    # إظهار نتيجة الحذف
                    if deleted_count > 0:
                        QMessageBox.information(
                            self, 
                            "نجح الحذف", 
                            f"تم حذف {deleted_count} سجل بنجاح! ✅"
                        )
                        # إعادة تحميل البيانات
                        self.load_records_data()
                        self.status_bar.showMessage(f"تم حذف {deleted_count} سجل")
                        
                        # محو جميع التحديدات بعد نجاح الحذف
                        print("🧹 محو التحديدات بعد نجاح عملية الحذف...")
                        QTimer.singleShot(500, self._clear_all_selections)  # تأخير قصير للسماح بإعادة التحميل
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف السجلات!")
                        
                except Exception as e:
                    print(f"خطأ في معالجة السجلات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_records)
            
        except Exception as e:
            print(f"خطأ في حذف السجلات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
    
    def print_report(self):
        """طباعة تقرير سجلات الدخول والتأخر للتلميذ المحدد"""
        try:
            # التحقق من توفر دالة الطباعة
            if not PRINT2_AVAILABLE:
                QMessageBox.warning(
                    self, 
                    "خطأ", 
                    "دالة طباعة تقرير سجلات الدخول والتأخر غير متوفرة!\n\nتأكد من وجود ملف print2.py."
                )
                return

            # الحصول على السجلات المحددة من المستخدم
            js_code = """
            (function() {
                const selectedRecords = getSelectedRecords();
                return JSON.stringify(selectedRecords);
            })();
            """
            
            def handle_selected_records(result):
                try:
                    selected_records = []
                    if result:
                        selected_records = json.loads(result)
                    
                    # إذا لم يحدد المستخدم أي سجلات، استخدم جميع السجلات المعروضة
                    if not selected_records:
                        reply = QMessageBox.question(
                            self, 
                            "تأكيد الطباعة", 
                            "لم يتم تحديد أي سجلات.\n\nهل تريد طباعة تقرير بجميع السجلات المعروضة حالياً؟",
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.Yes
                        )
                        if reply != QMessageBox.Yes:
                            return
                        
                        # استخدام جميع السجلات المعروضة
                        target_data = self.filtered_data
                    else:
                        # استخدام السجلات المحددة فقط
                        selected_ids = [record['id'] for record in selected_records]
                        target_data = [row for row in self.filtered_data if str(row[0]) in selected_ids]

                    print(f"🔄 بدء معالجة {len(target_data)} سجل للطباعة")

                    # تجميع البيانات حسب رمز التلميذ (وليس رقم الورقة)
                    students_data = {}
                    for row in target_data:
                        # ترتيب الحقول في البيانات الأصلية (حسب جدول ورقة_السماح_بالدخول):
                        # row[0] = rowid (ID السجل)
                        # row[1] = التاريخ  
                        # row[2] = الرمز (رمز التلميذ)
                        # row[3] = الاسم_والنسب (اسم التلميذ)
                        # row[4] = الوقت
                        # row[5] = ورقة_السماح (نوع السماح)
                        # row[6] = السنة_الدراسية
                        # row[7] = الأسدس
                        # row[8] = رقم_الورقة
                        # ملاحظة: المستوى والقسم سيتم جلبهما من جدول اللوائح باستخدام الرمز
                        
                        student_code = str(row[2]).strip() if len(row) > 2 else "غير محدد"  # رمز التلميذ
                        student_name = str(row[3]).strip() if len(row) > 3 else "غير محدد"  # اسم التلميذ
                        
                        # التأكد من أن رمز التلميذ ليس فارغ
                        if not student_code or student_code == "غير محدد":
                            print(f"⚠️ تجاهل سجل بدون رمز تلميذ صحيح: {row}")
                            continue
                        
                        # إنشاء مجموعة جديدة للتلميذ إذا لم تكن موجودة
                        if student_code not in students_data:
                            # محاولة الحصول على المستوى والصف من جدول اللوائح في قاعدة البيانات
                            level = "غير محدد"
                            class_name = "غير محدد"
                            
                            # البحث عن بيانات التلميذ في جدول اللوائح
                            try:
                                temp_conn = sqlite3.connect('data.db')
                                temp_cursor = temp_conn.cursor()
                                
                                # محاولة البحث في جدول اللوائح باستخدام أسماء أعمدة مختلفة
                                search_queries = [
                                    "SELECT المستوى, القسم FROM اللوائح WHERE الرمز = ? LIMIT 1",
                                    "SELECT المستوى, القسم FROM اللوائح WHERE رمز_التلميذ = ? LIMIT 1",
                                    "SELECT level, class FROM اللوائح WHERE code = ? LIMIT 1",
                                    "SELECT level, section FROM اللوائح WHERE student_code = ? LIMIT 1"
                                ]
                                
                                student_info = None
                                for query in search_queries:
                                    try:
                                        temp_cursor.execute(query, (student_code,))
                                        student_info = temp_cursor.fetchone()
                                        if student_info:
                                            level = str(student_info[0]).strip() if student_info[0] else "غير محدد"
                                            class_name = str(student_info[1]).strip() if student_info[1] else "غير محدد"
                                            print(f"   📋 تم العثور على بيانات التلميذ في جدول اللوائح: المستوى={level}, القسم={class_name}")
                                            break
                                    except Exception as query_error:
                                        # تجاهل أخطاء الاستعلام الفردية ومحاولة الاستعلام التالي
                                        continue
                                
                                if not student_info:
                                    print(f"   ⚠️ لم يتم العثور على بيانات التلميذ {student_code} في جدول اللوائح")
                                    
                            except Exception as e:
                                print(f"   ❌ خطأ في البحث عن بيانات التلميذ {student_code} في جدول اللوائح: {e}")
                            finally:
                                try:
                                    temp_conn.close()
                                except:
                                    pass
                            
                            students_data[student_code] = {
                                'student_name': student_name,
                                'student_code': student_code,
                                'level': level,
                                'class': class_name,
                                'permission_records': [],
                                'late_records': [],
                                'all_records': []  # لحفظ جميع السجلات
                            }
                            print(f"📝 إنشاء مجموعة جديدة للتلميذ: {student_name} (الرمز: {student_code}) - المستوى: {level} - الصف: {class_name}")
                        
                        # إنشاء بيانات السجل بالتنسيق المطلوب لـ print2.py
                        # print2.py يتوقع: [رقم_الورقة, التاريخ, الوقت, ...]
                        record_data = [
                            str(row[0]) if len(row) > 0 else "",  # رقم الورقة [0]
                            str(row[1]) if len(row) > 1 else "",  # التاريخ [1]
                            str(row[4]) if len(row) > 4 else "",  # الوقت [2]
                            str(row[5]) if len(row) > 5 else "",  # نوع السماح [3]
                            str(row[6]) if len(row) > 6 else "",  # السنة الدراسية [4]
                            str(row[7]) if len(row) > 7 else "",  # الأسدس [5]
                        ]
                        
                        # التحقق من وجود التاريخ والوقت (مطلوبين للطباعة)
                        if not record_data[1] or not record_data[2]:
                            print(f"⚠️ سجل بدون تاريخ أو وقت صحيح - سيتم تجاهله: {record_data}")
                            continue
                        
                        # إضافة السجل إلى جميع السجلات
                        students_data[student_code]['all_records'].append(record_data)
                        
                        # تصنيف السجل حسب نوع السماح
                        permission_type = str(row[5]).strip() if len(row) > 5 else ""  # نوع السماح من البيانات الأصلية
                        
                        if permission_type == "سماح":
                            students_data[student_code]['permission_records'].append(record_data)
                            print(f"   ✅ إضافة سجل سماح للتلميذ {student_name}: التاريخ {record_data[1]} - الوقت {record_data[2]}")
                        elif permission_type == "تأخر":
                            students_data[student_code]['late_records'].append(record_data)
                            print(f"   ⏰ إضافة سجل تأخر للتلميذ {student_name}: التاريخ {record_data[1]} - الوقت {record_data[2]}")
                        else:
                            # إضافة السجلات الأخرى إلى قائمة السماح العامة
                            students_data[student_code]['permission_records'].append(record_data)
                            print(f"   📋 إضافة سجل عام للتلميذ {student_name}: التاريخ {record_data[1]} - الوقت {record_data[2]} (نوع: '{permission_type}')")

                    print(f"🔍 إجمالي عدد التلاميذ بعد التجميع: {len(students_data)}")
                    
                    # تنظيف البيانات - إزالة التلاميذ الذين ليس لديهم سجلات صالحة
                    valid_students_data = {}
                    for student_code, student_data in students_data.items():
                        total_records = len(student_data['all_records'])
                        if total_records > 0:
                            valid_students_data[student_code] = student_data
                            print(f"✅ التلميذ {student_data['student_name']} (رمز: {student_code}) صالح - {total_records} سجل")
                        else:
                            print(f"❌ التلميذ {student_data['student_name']} (رمز: {student_code}) لا يحتوي على سجلات صالحة - سيتم تجاهله")
                    
                    # استبدال البيانات بالبيانات المنظفة
                    students_data = valid_students_data
                    print(f"🔄 عدد التلاميذ بعد التنظيف: {len(students_data)}")
                    
                    # التحقق من وجود تلاميذ صالحين للطباعة
                    if not students_data:
                        QMessageBox.information(
                            self, 
                            "تنبيه", 
                            "لا توجد سجلات صالحة للطباعة!\n\n"
                            "تأكد من أن السجلات تحتوي على:\n"
                            "• رموز تلاميذ صحيحة\n"
                            "• تواريخ وأوقات صالحة\n"
                            "• بيانات كاملة في جميع الحقول المطلوبة"
                        )
                        return
                    
                    # طباعة تفاصيل كل تلميذ
                    for student_code, entry_data in students_data.items():
                        total_records = len(entry_data['all_records'])
                        permission_count = len(entry_data['permission_records'])
                        late_count = len(entry_data['late_records'])
                        
                        print(f"📊 التلميذ: {entry_data['student_name']} (الرمز: {student_code})")
                        print(f"   - إجمالي السجلات: {total_records}")
                        print(f"   - سجلات السماح: {permission_count}")
                        print(f"   - سجلات التأخر: {late_count}")

                    # جمع بيانات المؤسسة
                    institution_data = {}
                    try:
                        conn = sqlite3.connect('data.db')
                        cursor = conn.cursor()
                        cursor.execute("SELECT المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                        result = cursor.fetchone()
                        if result:
                            institution_data['institution'] = result[0]
                            institution_data['school_year'] = result[1]
                        else:
                            institution_data['institution'] = 'مؤسسة تعليمية'
                            institution_data['school_year'] = '2024/2025'
                        conn.close()
                    except Exception as e:
                        print(f"خطأ في استخراج بيانات المؤسسة: {e}")
                        institution_data['institution'] = 'مؤسسة تعليمية'
                        institution_data['school_year'] = '2024/2025'

                    # إنشاء مجلد التقارير على سطح المكتب
                    import os
                    from datetime import datetime
                    
                    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
                    main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
                    reports_dir = os.path.join(main_reports_dir, "تقارير سجلات الدخول والتأخر")
                    
                    if not os.path.exists(reports_dir):
                        os.makedirs(reports_dir)

                    # معالجة كل تلميذ على حدة وإنشاء تقرير موحد لكل تلميذ
                    success_count = 0
                    total_students = len(students_data)
                    
                    print(f"🚀 بدء إنشاء التقارير لـ {total_students} تلميذ...")
                    
                    for student_code, entry_data in students_data.items():
                        # إنشاء اسم ملف فريد لكل تلميذ (باستخدام رمز التلميذ)
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        # تنظيف اسم التلميذ من الرموز غير المسموحة في أسماء الملفات
                        safe_student_name = ''.join(c if c.isalnum() or c in '_-' else '_' for c in entry_data['student_name'])
                        # إزالة المسافات الزائدة والشرطات المتكررة
                        safe_student_name = '_'.join(filter(None, safe_student_name.split('_')))
                        # التأكد من عدم تجاوز طول اسم الملف
                        if len(safe_student_name) > 30:
                            safe_student_name = safe_student_name[:30]
                        # تنظيف رمز التلميذ أيضاً
                        safe_student_code = ''.join(c if c.isalnum() or c in '_-' else '_' for c in str(student_code))
                        
                        file_name = f"تقرير_سجلات_{safe_student_name}_رمز_{safe_student_code}_{timestamp}"
                        
                        # تمرير معلومات المجلد واسم الملف
                        institution_data['output_dir'] = reports_dir
                        institution_data['file_name'] = file_name
                        
                        # طباعة معلومات التقرير الذي سيتم إنشاؤه
                        total_records = len(entry_data['all_records'])
                        permission_records = len(entry_data['permission_records'])
                        late_records = len(entry_data['late_records'])
                        
                        print(f"📄 إنشاء تقرير للتلميذ: {entry_data['student_name']} (رمز: {student_code})")
                        print(f"   � المستوى: {entry_data['level']} - الصف: {entry_data['class']}")
                        print(f"   �📊 سيتم تضمين {total_records} سجل إجمالي:")
                        print(f"   ✅ {permission_records} سجل سماح")
                        print(f"   ⏰ {late_records} سجل تأخر")
                        print(f"   📁 اسم الملف: {file_name}.pdf")
                        
                        # طباعة هيكل البيانات للتشخيص
                        print(f"   🔍 هيكل البيانات المرسلة:")
                        print(f"      - student_code: {entry_data['student_code']}")
                        print(f"      - student_name: {entry_data['student_name']}")
                        print(f"      - level: {entry_data['level']}")
                        print(f"      - class: {entry_data['class']}")
                        print(f"      - permission_records count: {len(entry_data['permission_records'])}")
                        print(f"      - late_records count: {len(entry_data['late_records'])}")
                        if entry_data['permission_records']:
                            print(f"      - sample permission record: {entry_data['permission_records'][0]}")
                        if entry_data['late_records']:
                            print(f"      - sample late record: {entry_data['late_records'][0]}")
                        
                        # طباعة التقرير للتلميذ
                        try:
                            self.status_bar.showMessage(f"جاري إنشاء تقرير التلميذ {entry_data['student_name']} ({success_count + 1}/{total_students})...")
                            success = print_entry_records(institution_data, entry_data)
                            if success:
                                success_count += 1
                                print(f"   ✅ تم إنشاء التقرير بنجاح!")
                            else:
                                print(f"   ❌ فشل في إنشاء التقرير!")
                        except Exception as e:
                            print(f"   💥 خطأ في إنشاء التقرير: {e}")
                            # طباعة تفاصيل أكثر عن الخطأ
                            import traceback
                            print(f"   📋 تفاصيل الخطأ: {traceback.format_exc()}")

                    print(f"🏁 انتهاء إنشاء التقارير. النتيجة: {success_count}/{total_students}")

                    # إظهار نتيجة العملية
                    if success_count > 0:
                        # حساب إحصائيات التقارير
                        total_records_printed = sum(len(data['all_records']) for data in students_data.values())
                        
                        success_message = (
                            f"🎉 تم إنشاء {success_count} تقرير من أصل {total_students} تلميذ بنجاح!\n\n"
                            f"📊 إحصائيات التقارير المُنشأة:\n"
                            f"• عدد التلاميذ: {success_count}\n"
                            f"• إجمالي السجلات المطبوعة: {total_records_printed}\n"
                            f"• متوسط السجلات لكل تلميذ: {total_records_printed//success_count if success_count > 0 else 0}\n\n"
                            f"📁 مسار حفظ التقارير:\n{reports_dir}\n\n"
                            f"💡 كل تقرير يحتوي على جميع سجلات التلميذ مجمعة حسب رمز التلميذ\n"
                            f"📋 التقارير تشمل سجلات السماح والتأخر منظمة في جداول منفصلة"
                        )
                        
                        QMessageBox.information(self, "تم بنجاح ✅", success_message)
                        self.status_bar.showMessage(f"تم إنشاء {success_count} تقرير بنجاح - {total_records_printed} سجل إجمالي ✅")
                        
                        # محو جميع التحديدات بعد نجاح الطباعة
                        self._clear_all_selections()
                        
                        # محاولة فتح مجلد التقارير تلقائياً
                        try:
                            import subprocess
                            subprocess.Popen(f'explorer "{reports_dir}"')
                            print(f"📂 تم فتح مجلد التقارير تلقائياً: {reports_dir}")
                        except Exception as e:
                            print(f"تعذر فتح مجلد التقارير تلقائياً: {e}")
                    else:
                        error_message = (
                            f"❌ فشل في إنشاء التقارير!\n\n"
                            f"📊 تم محاولة إنشاء {total_students} تقرير لكن لم ينجح أي منها.\n\n"
                            f"الأسباب المحتملة:\n"
                            f"• مشكلة في دالة print_entry_records في ملف print2.py\n"
                            f"• مشكلة في صيغة البيانات المرسلة\n"
                            f"• مشكلة في صلاحيات كتابة الملفات\n"
                            f"• مشكلة في تثبيت مكتبة reportlab\n\n"
                            f"💡 يُرجى التحقق من وحدة التحكم (Console) لمزيد من التفاصيل."
                        )
                        QMessageBox.warning(self, "خطأ في الطباعة ❌", error_message)
                        self.status_bar.showMessage("فشل في إنشاء التقارير ❌")
                        
                except Exception as e:
                    print(f"خطأ في معالجة السجلات للطباعة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحضير التقرير:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت للحصول على السجلات المحددة
            self.web_view.page().runJavaScript(js_code, handle_selected_records)
            
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في طباعة التقرير:\n{str(e)}")
    
    def _clear_all_selections(self):
        """محو جميع التحديدات في الجدول"""
        try:
            print("🧹 بدء محو جميع التحديدات...")
            
            # JavaScript لمحو جميع التحديدات
            js_code = """
            (function() {
                try {
                    // إلغاء تحديد مربع "اختيار الكل"
                    const selectAllBox = document.getElementById('select-all');
                    if (selectAllBox) {
                        selectAllBox.checked = false;
                        selectAllBox.indeterminate = false;
                    }
                    
                    // إلغاء تحديد جميع مربعات التحديد الفردية
                    const checkboxes = document.querySelectorAll('.record-select');
                    let clearedCount = 0;
                    
                    checkboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            checkbox.checked = false;
                            clearedCount++;
                            
                            // إزالة التأثير المرئي للتحديد
                            const row = checkbox.closest('tr');
                            if (row) {
                                row.classList.remove('selected');
                            }
                        }
                    });
                    
                    return {
                        success: true,
                        clearedCount: clearedCount,
                        totalCheckboxes: checkboxes.length
                    };
                    
                } catch (error) {
                    return {
                        success: false,
                        error: error.message
                    };
                }
            })();
            """
            
            # تنفيذ الجافا سكريبت
            def handle_clear_result(result):
                try:
                    if result:
                        import json
                        clear_data = json.loads(result)
                        
                        if clear_data.get('success', False):
                            cleared_count = clear_data.get('clearedCount', 0)
                            total_checkboxes = clear_data.get('totalCheckboxes', 0)
                            print(f"✅ تم محو {cleared_count} تحديد من أصل {total_checkboxes} مربع تحديد")
                            
                            # تحديث شريط الحالة
                            if cleared_count > 0:
                                self.status_bar.showMessage(f"تم محو {cleared_count} تحديد ✅")
                            else:
                                self.status_bar.showMessage("لا توجد تحديدات لمحوها ℹ️")
                        else:
                            error_msg = clear_data.get('error', 'خطأ غير معروف')
                            print(f"❌ فشل في محو التحديدات: {error_msg}")
                            self.status_bar.showMessage("فشل في محو التحديدات ❌")
                    else:
                        print("⚠️ لم يتم إرجاع نتيجة من JavaScript")
                        
                except Exception as e:
                    print(f"❌ خطأ في معالجة نتيجة محو التحديدات: {e}")
            
            # تنفيذ الجافا سكريبت مع معالج النتيجة
            self.web_view.page().runJavaScript(js_code, handle_clear_result)
            
        except Exception as e:
            print(f"❌ خطأ في دالة محو التحديدات: {e}")
    
    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        # إيقاف مراقبة النافذة
        if hasattr(self, 'window_monitor_timer'):
            self.window_monitor_timer.stop()
        event.accept()

    def ensure_maximized(self):
        """دالة مساعدة لضمان فتح النافذة في كامل الشاشة"""
        # إزالة أي قيود على الحجم
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)

        # تعيين حالة النافذة
        self.setWindowState(Qt.WindowMaximized)

        # إظهار النافذة في كامل الشاشة
        self.showMaximized()

        # تفعيل النافذة ورفعها للمقدمة
        self.activateWindow()
        self.raise_()

        # التأكد من التركيز
        self.setFocus()

        # بدء مراقبة حالة النافذة
        # بدء مراقبة حالة النافذة
        self.start_window_monitor()

    def start_window_monitor(self):
        """بدء مراقبة حالة النافذة للتأكد من بقائها في كامل الشاشة"""
        self.window_monitor_timer = QTimer()
        self.window_monitor_timer.timeout.connect(self.check_window_state)
        self.window_monitor_timer.start(1000)  # فحص كل ثانية

    def check_window_state(self):
        """فحص حالة النافذة والتأكد من أنها في كامل الشاشة"""
        if self.windowState() != Qt.WindowMaximized:
            self.setWindowState(Qt.WindowMaximized)
            self.showMaximized()


# للتوافق مع الكود القديم
class StudentRecordsWindow(UniversalStudentRecordsWindow):
    """نافذة سجلات التلميذ - للتوافق مع الكود القديم"""
    
    def __init__(self, student_code=None, student_name=None, db=None, parent=None):
        # تحويل للنافذة العامة مع فلتر التلميذ
        super().__init__("entry_permissions", parent)
        
        # إضافة فلتر للتلميذ المحدد إذا كان موجوداً
        if student_code:
            # يمكن إضافة منطق لفلترة البيانات حسب رمز التلميذ
            self.student_code = student_code
            self.student_name = student_name or "غير محدد"


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إنشاء النافذة
    window = UniversalStudentRecordsWindow(record_type="entry_permissions")

    # ضمان فتح النافذة في كامل الشاشة بطرق متعددة
    window.setWindowState(Qt.WindowMaximized)
    window.showMaximized()
    window.ensure_maximized()

    # إضافة timer للتأكد من فتح النافذة في كامل الشاشة بعد بدء التطبيق
    QTimer.singleShot(200, window.ensure_maximized)
    QTimer.singleShot(500, window.ensure_maximized)

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
